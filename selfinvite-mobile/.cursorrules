# Selfinvite Mobile App Migration - Cursor Rules

## Project Context
You are helping migrate the Selfinvite social dining platform from an older React Native architecture to a modern, scalable solution. The app connects people for shared culinary experiences across Europe.

## Technology Stack & Architecture

### Core Technologies
- **React Native** with Expo SDK 53+
- **Package Manager** use yarn
- **TypeScript** for type safety
- **Supabase** for backend (auth, database, realtime)
- **TanStack Query** (React Query) for server state management
- **Zustand** for client state management
- **Expo Router** for routing
- **Stripe** for payments
- **Meilisearch** for search functionality
- **Expo Notifications** for push notifications

### Migration Strategy
- Create new Scaffold Expo app in mobile path/
- Consider the current implementation in the parent folder
- Replace Redux Saga with TanStack Query + Zustand
- Migrate from custom backend to Supabase
- Replace Algolia with Meilisearch
- Modernize UI with React Native Paper or NativeBase
- Implement proper TypeScript throughout

## Code Standards & Best Practices

### File Structure
```
mobile/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API and external services
│   ├── stores/             # Zustand stores
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Utility functions
│   ├── constants/          # App constants
│   └── navigation/         # Navigation configuration
├── assets/                 # Images, fonts, etc.
└── app.json               # Expo configuration
```

### Naming Conventions
- **Components**: PascalCase (e.g., `EventCard.tsx`)
- **Hooks**: camelCase with 'use' prefix (e.g., `useEventData.ts`)
- **Files**: kebab-case for utilities, PascalCase for components
- **Types**: PascalCase with descriptive names (e.g., `EventData`, `UserProfile`)

### Component Guidelines
- Use functional components with hooks
- Implement proper TypeScript interfaces for props
- Follow the single responsibility principle
- Use React.memo for performance optimization when needed
- Implement proper error boundaries

### State Management
- **Server State**: Use TanStack Query for API data
- **Client State**: Use Zustand for UI state, user preferences
- **Form State**: Use React Hook Form with Zod validation
- Avoid prop drilling - use context or stores when needed

## Key Features to Implement

### 1. Authentication & User Management
- Supabase Auth integration
- Social login (Google, Apple, Facebook)
- User profile management
- Avatar upload and management

### 2. Event Management
- Create/edit events with rich details
- Event categories and tags
- Location services with maps
- Dietary preferences and restrictions
- Event images and media

### 3. Search & Discovery
- Meilisearch integration for fast search
- Advanced filtering (location, date, cuisine, dietary)
- Personalized recommendations
- Saved searches and favorites

### 4. Social Features
- Event requests and invitations
- User reviews and ratings
- Block/unblock users
- Real-time messaging
- Push notifications

### 5. Payment Integration
- Stripe payment processing
- Event pricing and deposits
- Refund handling
- Payment history

## Development Guidelines

### API Integration
- Use TanStack Query for all API calls
- Implement proper error handling
- Use optimistic updates for better UX
- Implement retry logic for failed requests
- Cache data appropriately

### Performance Optimization
- Implement lazy loading for images
- Use FlatList for long lists
- Optimize bundle size
- Implement proper memoization
- Use React Native Performance Monitor

### Testing Strategy
- Unit tests for utilities and hooks
- Component tests with React Native Testing Library
- Integration tests for critical flows
- E2E tests for main user journeys

### Error Handling
- Implement global error boundaries
- Use proper error states in components
- Log errors appropriately
- Provide user-friendly error messages
- Implement retry mechanisms

## UI/UX Guidelines

### Design System
- Use consistent spacing and typography
- Implement dark/light theme support
- Follow accessibility guidelines
- Use proper loading states
- Implement smooth animations

### Navigation
- Use Expo Router for navigation
- Implement proper deep linking
- Handle back navigation correctly
- Use bottom tab navigation for main sections
- Implement proper screen transitions

## Security Considerations
- Implement proper input validation
- Use secure storage for sensitive data
- Implement proper authentication flows
- Validate all user inputs
- Use HTTPS for all API calls

## Migration Priorities
1. Set up new project structure with modern tooling
2. Implement authentication with Supabase
3. Create basic event management features
4. Implement search functionality
5. Add social features (messaging, reviews)
6. Integrate payment processing
7. Add push notifications
8. Implement advanced features and optimizations

## Code Quality
- Use ESLint and Prettier
- Implement proper TypeScript strict mode
- Use meaningful commit messages
- Write self-documenting code
- Add proper JSDoc comments for complex functions

## When Writing Code
- Always consider the user experience first
- Think about error states and edge cases
- Implement proper loading states
- Consider accessibility from the start
- Write code that's easy to test and maintain
- Follow the established patterns in the codebase
- Use modern React Native patterns and hooks
- Prioritize performance and user experience
- use the proper logger
- use the globalDialog rather than the simple alert

## Common Patterns to Follow
- Use custom hooks for reusable logic
- Implement proper TypeScript types
- Use React Query for data fetching
- Implement proper error boundaries
- Use Zustand for global state
- Follow the established file structure
- Use proper naming conventions
- Implement proper testing

Remember: This is a migration project, so focus on modern best practices while maintaining the core functionality of the original Selfinvite app. 