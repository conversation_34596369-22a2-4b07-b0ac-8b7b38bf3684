# Selfinvite Mobile App

A modern React Native social dining platform built with Expo 53, connecting people through shared food experiences.

## 🚀 Tech Stack

- **React Native** with Expo SDK 53
- **TypeScript** for type safety
- **Expo Router** for file-based navigation
- **React Native Paper** for Material Design 3 UI
- **TanStack Query** for server state management
- **Zustand** for client state management
- **Supabase** for backend services
- **Stripe** for payments
- **Meilisearch** for search functionality

## 📱 Features

- **Event Discovery**: Browse and search food events with advanced filtering
- **Event Management**: Create, host, and manage your own events
- **Request System**: Send and manage participation requests
- **Real-time Messaging**: Chat with hosts and guests
- **User Profiles**: Comprehensive profile management with reviews and ratings
- **Payment Integration**: Secure payment processing with Stripe
- **Social Features**: Reviews, ratings, and social connections

## 🛠 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd selfinvite/mobile
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Fill in your environment variables:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_key
   EXPO_PUBLIC_MEILISEARCH_URL=url
   EXPO_PUBLIC_MEILISEARCH_API_KEY=your_meilisearch_key
   ```

4. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

## 📂 Project Structure

```
mobile/
├── app/                    # Expo Router pages
│   ├── (tabs)/            # Bottom tab navigation
│   │   ├── search.tsx     # Event discovery
│   │   ├── events.tsx     # User events
│   │   ├── requests.tsx   # Request management
│   │   ├── messages.tsx   # Chat conversations
│   │   └── profile.tsx    # User profile
│   └── _layout.tsx        # Root layout with providers
├── src/
│   ├── components/        # Reusable UI components
│   ├── constants/         # App constants and theme
│   ├── hooks/            # Custom React hooks
│   ├── services/         # API services and utilities
│   ├── stores/           # Zustand state stores
│   ├── types/            # TypeScript type definitions
│   └── utils/            # Utility functions
├── assets/               # Static assets (images, fonts)
├── .cursorrules          # Cursor IDE configuration
└── README.md
```

## 🎨 Design System

The app follows Material Design 3 principles with a custom Selfinvite brand theme:

- **Primary Color**: `#F24958` (Selfinvite Red)
- **Secondary Color**: `#4A90E2` (Blue)
- **Typography**: Material Design 3 type scale
- **Components**: React Native Paper Material Design 3

## 🧪 Development Scripts

```bash
npm start          # Start Expo development server
npm run android    # Run on Android device/emulator
npm run ios        # Run on iOS device/simulator
npm run web        # Run on web browser
npm run build:android  # Build Android APK
npm run build:ios      # Build iOS IPA
npm run test       # Run tests
npm run lint       # Run ESLint
npm run type-check # Run TypeScript checks
```

## 📱 Platform Support

- **iOS**: 13.0+
- **Android**: API level 23 (Android 6.0)+
- **Web**: Modern browsers (Chrome, Firefox, Safari, Edge)

## 🔧 Configuration

### Expo Configuration (`app.json`)
- App name, slug, and version
- Icon and splash screen settings
- Platform-specific configurations
- Plugin configurations

### Theme Configuration (`src/constants/theme.ts`)
- Material Design 3 color schemes
- Typography definitions
- Spacing and border radius values
- Light and dark theme support

## 🚦 Getting Started

1. **Run the app**: Use `npm start` and scan the QR code with Expo Go
2. **Navigate**: Use the bottom tab navigation to explore features
3. **Customize**: Modify theme colors in `src/constants/theme.ts`
4. **Add features**: Create new screens in the `app/` directory

## 🔄 Migration from Legacy App

This is a complete rewrite of the original Selfinvite app with modern architecture:

### Key Improvements
- **State Management**: Redux Saga → TanStack Query + Zustand
- **Navigation**: React Navigation → Expo Router
- **Backend**: JHipster → Supabase + Go Serverless
- **Search**: Algolia → Meilisearch
- **UI Library**: Custom → React Native Paper (Material Design 3)

### Migration Benefits
- Reduced complexity and boilerplate
- Better TypeScript support
- Improved performance
- Modern development experience
- Lower operational costs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions and support:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `docs/` folder

---

Built with ❤️ by the Selfinvite team 