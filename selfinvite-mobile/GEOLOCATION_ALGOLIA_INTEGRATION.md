# Geolocation Integration with Algolia Search

This guide explains how to integrate geolocation features with Algolia search in the Selfinvite mobile app, enabling location-based event discovery and search.

## Overview

The geolocation integration allows users to:
- Search for events near their current location
- Filter events by distance radius
- Sort events by proximity
- Get location-based search suggestions
- Use map view with location-aware results

## Key Components

### 1. Location Service (`src/components/LocationService.tsx`)
- Handles location permissions
- Gets current user location
- Updates location in search store
- Provides location status UI

### 2. Enhanced Search Filters
- Automatically includes user coordinates when available
- Sets default search radius (25km)
- Enables distance-based sorting

### 3. Algolia Search Integration
- Uses `aroundLatLng` parameter for location-based search
- Supports `aroundRadius` for distance filtering
- Provides distance-based sorting options

## Implementation Details

### Location-Based Search Filters

```typescript
// Enhanced filters with location data
const enhancedFilters = useMemo(() => {
  const baseFilters = { ...appliedFilters };
  
  // Add location-based filtering if user location is available
  if (userLocation && useAlgoliaSearch) {
    baseFilters.coordinates = {
      lat: userLocation.latitude,
      lng: userLocation.longitude
    };
    // Set default radius if not already set
    if (!baseFilters.radius) {
      baseFilters.radius = 25; // 25km default radius
    }
  }
  
  return baseFilters;
}, [appliedFilters, userLocation, useAlgoliaSearch]);
```

### Algolia Search with Location

```typescript
// Algolia search functionality with location
const algoliaSearch = useAlgoliaSearchWithStore(
  query,
  enhancedFilters,
  {
    enabled: useAlgoliaSearch,
    useInfinite: true,
    sort: currentSort.field === 'distance' && userLocation 
      ? [{ field: 'distance', direction: currentSort.direction }]
      : [{ field: currentSort.field as any, direction: currentSort.direction }]
  }
);
```

### Location Service Integration

```typescript
// Location service component
<LocationService onLocationUpdate={() => {}} />
```

## Usage Examples

### 1. Basic Location-Based Search

```typescript
import { useAlgoliaNearbySearch } from '../hooks/useAlgoliaSearch';

const MyComponent = () => {
  const { userLocation } = useSearchStore();
  
  const nearbySearch = useAlgoliaNearbySearch(
    userLocation?.latitude,
    userLocation?.longitude,
    10, // 10km radius
    { eventTypes: ['DINNER'] },
    { enabled: !!userLocation }
  );

  return (
    <FlatList
      data={nearbySearch.data?.hits || []}
      renderItem={({ item }) => <EventCard event={item} />}
    />
  );
};
```

### 2. Search with Custom Radius

```typescript
const [searchRadius, setSearchRadius] = useState(25); // km

const locationSearch = useAlgoliaSearchWithStore(
  'italian dinner',
  {
    coordinates: userLocation ? {
      lat: userLocation.latitude,
      lng: userLocation.longitude
    } : undefined,
    radius: searchRadius,
    priceRange: [20, 50]
  },
  { enabled: !!userLocation }
);
```

### 3. Distance-Based Sorting

```typescript
const sortOptions = [
  { field: 'event_date', direction: 'asc', label: 'Date (Earliest)' },
  { field: 'price_per_person', direction: 'asc', label: 'Price (Low to High)' },
];

// Add distance sorting if user location is available
if (userLocation) {
  sortOptions.push({ 
    field: 'distance', 
    direction: 'asc', 
    label: 'Distance (Nearest)' 
  });
}
```

### 4. Direct Algolia Service Usage

```typescript
import { algoliaSearchService } from '../services/algoliaService';

// Search nearby events
const searchNearby = async () => {
  const results = await algoliaSearchService.searchNearby(
    userLocation.latitude,
    userLocation.longitude,
    15, // 15km radius
    { eventTypes: ['DINNER', 'LUNCH'] }
  );
  
  console.log(`Found ${results.hits.length} events nearby`);
};

// Search by city
const searchByCity = async () => {
  const results = await algoliaSearchService.searchByCity(
    'Paris',
    'italian dinner'
  );
  
  console.log(`Found ${results.length} Italian dinners in Paris`);
};
```

## UI Components

### 1. Location Status Indicator

```typescript
{/* Location Status */}
{userLocation && (
  <IconButton
    icon="map-marker"
    iconColor={theme.colors.primary}
    size={20}
  />
)}
```

### 2. Search Mode Toggle

```typescript
{/* Search Mode Toggle */}
<IconButton
  icon={useAlgoliaSearch ? "lightning-bolt" : "database"}
  onPress={() => setUseAlgoliaSearch(!useAlgoliaSearch)}
  iconColor={useAlgoliaSearch ? theme.colors.primary : theme.colors.onSurface}
  size={20}
/>
```

### 3. Results with Location Info

```typescript
<Text style={styles.resultsCount}>
  {totalCount} events found
  {useAlgoliaSearch && ' (Algolia)'}
  {userLocation && ' • Location-based'}
</Text>
```

## Configuration

### Environment Variables

```bash
# Algolia Configuration
EXPO_PUBLIC_ALGOLIA_APP_ID=your_algolia_app_id
EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY=your_algolia_search_api_key
```

### Location Permissions

The app automatically requests location permissions when needed:

```typescript
// Check location permission
const checkLocationPermission = async () => {
  const { status } = await Location.getForegroundPermissionsAsync();
  const granted = status === 'granted';
  setLocationPermission(granted);
  
  if (granted && !userLocation) {
    getCurrentLocation();
  }
};
```

## Search Store Integration

The search store manages location state:

```typescript
interface SearchState {
  userLocation: {
    latitude: number;
    longitude: number;
    city?: string;
  } | null;
  locationPermissionGranted: boolean;
  
  // Actions
  setUserLocation: (location: LocationData | null) => void;
  setLocationPermission: (granted: boolean) => void;
}
```

## Distance Calculation

Events are enhanced with distance information:

```typescript
const eventsWithDistance = useMemo(() => {
  if (!userLocation) return events;
  
  return events.map(event => {
    const distance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      event.latitude,
      event.longitude
    );
    return { ...event, distance };
  });
}, [events, userLocation, calculateDistance]);
```

## Best Practices

### 1. Location Permission Handling
- Always check permissions before requesting location
- Provide clear explanations for why location is needed
- Handle permission denial gracefully

### 2. Performance Optimization
- Cache location data to avoid repeated requests
- Use reasonable default radius values
- Implement location-based caching for search results

### 3. User Experience
- Show location status in the UI
- Provide options to change search radius
- Allow users to disable location-based search

### 4. Error Handling
- Handle location service errors
- Provide fallback to non-location search
- Show appropriate error messages

## Testing

### 1. Test Location Service
```typescript
import { GeolocationAlgoliaExample } from '../components/GeolocationAlgoliaExample';

// Add to your app for testing
<GeolocationAlgoliaExample />
```

### 2. Mock Location for Testing
```typescript
// Mock location for testing
const mockLocation = {
  latitude: 48.8566,
  longitude: 2.3522,
  city: 'Paris'
};

setUserLocation(mockLocation);
```

### 3. Test Different Scenarios
- No location permission
- Location permission granted
- Location service unavailable
- Network connectivity issues

## Troubleshooting

### Common Issues

1. **Location Not Available**
   - Check location permissions
   - Verify location services are enabled
   - Test with mock location data

2. **Search Not Working with Location**
   - Verify Algolia configuration
   - Check if coordinates are being passed correctly
   - Test with simple location-based search

3. **Distance Calculation Issues**
   - Verify coordinate format (latitude, longitude)
   - Check distance calculation function
   - Test with known coordinates

### Debug Mode

Enable debug logging to troubleshoot issues:

```typescript
log.info('Location data:', userLocation);
log.info('Enhanced filters:', enhancedFilters);
log.info('Search results:', searchResults);
```

## Future Enhancements

### Potential Improvements

1. **Advanced Location Features**
   - Geofencing for location-based notifications
   - Location history for better recommendations
   - Multi-location support for travelers

2. **Enhanced Search**
   - Location-based search suggestions
   - Popular events in user's area
   - Location-aware trending searches

3. **Map Integration**
   - Interactive map with event markers
   - Route planning to events
   - Location-based event clustering

4. **Privacy Features**
   - Location data encryption
   - User control over location sharing
   - Anonymous location-based search

## API Reference

### Algolia Search Service Methods

```typescript
// Search nearby events
searchNearby(lat: number, lng: number, radius: number, filters?: SearchFilters): Promise<SearchResults>

// Search by city
searchByCity(city: string, query?: string): Promise<Event[]>

// Search with price range
searchByPriceRange(minPrice: number, maxPrice: number, query?: string): Promise<Event[]>

// Health check
healthCheck(): Promise<boolean>
```

### Search Hooks

```typescript
// Nearby search hook
useAlgoliaNearbySearch(lat?, lng?, radius?, filters?, options?)

// Location-based search hook
useAlgoliaSearchWithStore(query?, filters?, options?)

// City search hook
useAlgoliaCitySearch(city, query?, options?)
```

This integration provides a powerful location-aware search experience that enhances user discovery of nearby events while maintaining the flexibility to search globally when needed.
