import * as AuthSession from 'expo-auth-session';
import * as <PERSON><PERSON>rowser from 'expo-web-browser';
import * as SecureStore from 'expo-secure-store';

// Complete the auth session for better UX
WebBrowser.maybeCompleteAuthSession();

const redirectUri = AuthSession.makeRedirectUri({
  scheme: 'selfinvite',
  path: 'auth/callback',
});

export interface AuthConfig {
  clientId: string;
  authorizationEndpoint: string;
  tokenEndpoint: string;
  scopes: string[];
}

export class SocialAuth {
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
  }

  async startAuthSession() {
    try {
      const request = new AuthSession.AuthRequest({
        clientId: this.config.clientId,
        scopes: this.config.scopes,
        redirectUri,
        responseType: AuthSession.ResponseType.Code,
        state: this.generateState(),
        codeChallenge: await this.generateCodeChallenge(),
        codeChallengeMethod: AuthSession.CodeChallengeMethod.S256,
      });

      const result = await request.promptAsync({
        authorizationEndpoint: this.config.authorizationEndpoint,
        showInRecents: true,
      });

      return result;
    } catch (error) {
      console.error('Auth session error:', error);
      throw error;
    }
  }

  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  private async generateCodeChallenge(): Promise<string> {
    const codeVerifier = AuthSession.AuthRequest.createRandomCodeChallenge();
    return codeVerifier;
  }

  async exchangeCodeForTokens(code: string, codeVerifier?: string) {
    try {
      const tokenRequest = new AuthSession.AccessTokenRequest({
        clientId: this.config.clientId,
        code,
        redirectUri,
        grantType: AuthSession.GrantType.AuthorizationCode,
        codeVerifier,
      });

      const tokenResult = await AuthSession.exchangeCodeAsync(
        tokenRequest,
        {
          tokenEndpoint: this.config.tokenEndpoint,
        }
      );

      // Store tokens securely
      if (tokenResult.accessToken) {
        await SecureStore.setItemAsync('access_token', tokenResult.accessToken);
      }
      if (tokenResult.refreshToken) {
        await SecureStore.setItemAsync('refresh_token', tokenResult.refreshToken);
      }

      return tokenResult;
    } catch (error) {
      console.error('Token exchange error:', error);
      throw error;
    }
  }

  async getStoredTokens() {
    try {
      const accessToken = await SecureStore.getItemAsync('access_token');
      const refreshToken = await SecureStore.getItemAsync('refresh_token');
      
      return {
        accessToken,
        refreshToken,
      };
    } catch (error) {
      console.error('Error getting stored tokens:', error);
      return null;
    }
  }

  async clearTokens() {
    try {
      await SecureStore.deleteItemAsync('access_token');
      await SecureStore.deleteItemAsync('refresh_token');
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }
}

// Example configurations for popular providers
export const GoogleAuthConfig: AuthConfig = {
  clientId: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID || '',
  authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
  tokenEndpoint: 'https://oauth2.googleapis.com/token',
  scopes: ['openid', 'profile', 'email'],
};

export const FacebookAuthConfig: AuthConfig = {
  clientId: process.env.EXPO_PUBLIC_FACEBOOK_CLIENT_ID || '',
  authorizationEndpoint: 'https://www.facebook.com/v18.0/dialog/oauth',
  tokenEndpoint: 'https://graph.facebook.com/v18.0/oauth/access_token',
  scopes: ['email', 'public_profile'],
};

// Usage example:
// const googleAuth = new SocialAuth(GoogleAuthConfig);
// const result = await googleAuth.startAuthSession();
