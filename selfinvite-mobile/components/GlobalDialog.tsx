import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Dialog, Portal, Text, Button, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { useDialogStore, DialogType } from '../src/stores/dialogStore';

const getDialogIcon = (type: DialogType, theme: any) => {
  const iconSize = 48;
  
  switch (type) {
    case 'error':
      return (
        <MaterialIcons 
          name="error" 
          size={iconSize} 
          color={theme.colors.error} 
        />
      );
    case 'warning':
      return (
        <MaterialIcons 
          name="warning" 
          size={iconSize} 
          color={theme.colors.warning || '#F57C00'} 
        />
      );
    case 'success':
      return (
        <MaterialCommunityIcons 
          name="check-circle" 
          size={iconSize} 
          color={theme.colors.primary} 
        />
      );
    case 'confirmation':
      return (
        <MaterialCommunityIcons 
          name="help-circle" 
          size={iconSize} 
          color={theme.colors.primary} 
        />
      );
    case 'info':
    default:
      return (
        <MaterialCommunityIcons 
          name="information" 
          size={iconSize} 
          color={theme.colors.primary} 
        />
      );
  }
};

const getDialogColors = (type: DialogType, theme: any, dialogColor: string) => {
  switch (type) {
    case 'error':
      return {
        borderColor: dialogColor,
        backgroundColor: theme.colors.errorContainer || theme.colors.surface,
      };
    case 'warning':
      return {
        borderColor: dialogColor,
        backgroundColor: theme.colors.surface,
      };
    case 'success':
      return {
        borderColor: dialogColor,
        backgroundColor: theme.colors.surface,
      };
    case 'confirmation':
    case 'info':
    default:
      return {
        borderColor: dialogColor,
        backgroundColor: theme.colors.surface,
      };
  }
};

const getButtonStyle = (buttonStyle: string | undefined, theme: any) => {
  switch (buttonStyle) {
    case 'destructive':
      return {
        buttonColor: theme.colors.error,
        textColor: theme.colors.onError,
      };
    case 'secondary':
      return {
        buttonColor: 'transparent',
        textColor: theme.colors.onSurface,
      };
    case 'primary':
    default:
      return {
        buttonColor: theme.colors.primary,
        textColor: theme.colors.onPrimary,
      };
  }
};

export const GlobalDialog: React.FC = () => {
  const theme = useTheme();
  const { visible, type, title, message, buttons, dismissible, color, hideDialog } = useDialogStore();

  const dialogColors = getDialogColors(type, theme, color);
  const icon = getDialogIcon(type, theme);

  const handleDismiss = () => {
    if (dismissible) {
      hideDialog();
    }
  };

  const handleButtonPress = (button: any) => {
    if (button.onPress) {
      button.onPress();
    } else {
      hideDialog();
    }
  };

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={handleDismiss}
        dismissable={dismissible}
        style={[
          styles.dialog,
          {
            backgroundColor: dialogColors.backgroundColor,
            borderColor: dialogColors.borderColor,
          },
        ]}
      >
        {title && title.length > 0 && (
          <Dialog.Title style={styles.title}>
            <View style={styles.titleContainer}>
              {icon}
              <Text variant="titleLarge" style={[styles.titleText, { color: theme.colors.onSurface }]}>
                {title}
              </Text>
            </View>
          </Dialog.Title>
        )}
        
        <Dialog.Content style={styles.content}>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
            {message}
          </Text>
        </Dialog.Content>

        <Dialog.Actions style={styles.actions}>
          {buttons.map((button, index) => {
            const buttonColors = getButtonStyle(button.style, theme);
            return (
              <Button
                key={index}
                mode={button.mode || 'contained'}
                onPress={() => handleButtonPress(button)}
                style={[
                  styles.button,
                  index < buttons.length - 1 && styles.buttonMargin,
                ]}
                buttonColor={buttonColors.buttonColor}
                textColor={buttonColors.textColor}
              >
                {button.text}
              </Button>
            );
          })}
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
};

const styles = StyleSheet.create({
  dialog: {
    borderWidth: 2,
    borderRadius: 12,
    minHeight: 200,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  title: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 16,
  },
  titleText: {
    marginLeft: 16,
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingBottom: 16,
    minHeight: 60,
  },
  actions: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 8,
    justifyContent: 'flex-end',
  },
  button: {
    minWidth: 80,
  },
  buttonMargin: {
    marginRight: 8,
  },
}); 