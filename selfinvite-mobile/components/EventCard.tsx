import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { Surface, Text } from 'react-native-paper';
import { useTheme } from 'react-native-paper';

interface EventCardProps {
  title: string;
  date: string;
  location: string;
  status: string;
  statusColor?: string;
  onPress?: () => void;
}

export default function EventCard({ 
  title, 
  date, 
  location, 
  status, 
  statusColor,
  onPress
}: EventCardProps) {
  const theme = useTheme();

  const CardContent = () => (
    <>
      <Text style={[styles.eventTitle, { color: theme.colors.onSurface }]}>{title}</Text>
      <Text style={[styles.eventDate, { color: theme.colors.primary }]}>{date}</Text>
      <Text style={[styles.eventLocation, { color: theme.colors.onSurface }]}>{location}</Text>
      <Text style={[styles.eventStatus, { color: statusColor || theme.colors.primary }]}>{status}</Text>
    </>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        <Surface style={[styles.eventCard, { backgroundColor: theme.colors.surface }]}>
          <CardContent />
        </Surface>
      </TouchableOpacity>
    );
  }

  return (
    <Surface style={[styles.eventCard, { backgroundColor: theme.colors.surface }]}>
      <CardContent />
    </Surface>
  );
}

const styles = StyleSheet.create({
  eventCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  eventDate: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  eventLocation: {
    fontSize: 14,
    marginBottom: 4,
    opacity: 0.7,
  },
  eventStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
}); 