import { Platform, StyleSheet, View } from 'react-native';
import * as AppleAuthentication from 'expo-apple-authentication';
import {supabaseAuth} from '../lib/supabase';
import { router } from 'expo-router';
import { useAuthStore } from '../src/stores/authStore';
import { showErrorDialog, showSuccessDialog } from '../src/stores';
import log from '../src/common/logger';

export function AppleSocialAuth() {
  const { setSession, setUser } = useAuthStore();
  if (Platform.OS === 'ios')
    return (
      <View style={styles.container}>
        <View style={styles.verticallySpaced}>
          <AppleAuthentication.AppleAuthenticationButton
            buttonType={
              AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN
            }
            buttonStyle={
              AppleAuthentication.AppleAuthenticationButtonStyle.BLACK
            }
            cornerRadius={25}
            style={{ width: '100%', height: 48 }}
            onPress={async () => {
              try {
                const credential = await AppleAuthentication.signInAsync({
                  requestedScopes: [
                    AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
                    AppleAuthentication.AppleAuthenticationScope.EMAIL,
                  ],
                });
                log.info('credential', credential)
                // Sign in via Supabase Auth.
                if (credential.identityToken) {
                  log.info('Attempting Supabase sign in with Apple token');
                  const {
                    error,
                    data,
                  } = await supabaseAuth.auth.signInWithIdToken({
                    provider: 'apple',
                    token: credential.identityToken,
                  });
                  log.info("AppleSocialAuth", { error, data });
                  
                  // Add more detailed error logging
                  if (error) {
                    log.error('Supabase auth error details:', {
                      message: error.message,
                      name: error.name,
                      status: error.status
                    });
                  }
                  
                  if (!error) {
                    // User is signed in.
                    setUser(data.user);
                    setSession(data.session);
                    showSuccessDialog('Successfully signed in with Apple!');
                    router.push('/');
                  } else {
                    showErrorDialog(error.message || 'Failed to sign in with Apple', 'Authentication Error');
                  }
                } else {
                  showErrorDialog('No identity token received from Apple', 'Authentication Error');
                }
              } catch (e: any) {
                log.error('Apple auth error:', e);
                if (e.code === 'ERR_REQUEST_CANCELED') {
                  // User canceled the sign-in flow - no error dialog needed
                } else {
                  showErrorDialog(
                    e.message || 'An unexpected error occurred during sign in',
                    'Sign In Error'
                  );
                }
              }
            }}
          />
        </View>
      </View>
    );
  return <></>;
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  mt20: {
    marginTop: 20,
  },
})