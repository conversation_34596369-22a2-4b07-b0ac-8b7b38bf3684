import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'
import * as AuthSession from 'expo-auth-session';
import supabase from '../lib/supabase'
import { Button, Surface, TextInput } from 'react-native-paper'
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { useAuthStore } from '../src/stores/authStore';
import log from '../src/common/logger'
import theme from '../src/constants/theme';

export default function SignOut() {
  const [loading, setLoading] = useState(false)
  const {clearAuth} = useAuthStore()

  const signOut = async () => {
    const { error } = await supabase.auth.signOut()
    if (error) {
      log.error('Logout failed', error)
      return { error: 'Logout failed' }
    }

    clearAuth()
    return { error: 'Logout successful' }
  }

  return (
    <Surface style={[styles.logoutSection, { backgroundColor: theme.colors.surface }]}>
    <Button 
      mode="outlined" 
      icon="logout" 
      onPress={() => signOut()}
      style={styles.logoutButton}
      labelStyle={{ color: theme.colors.error }}
    >
      Sign Out
    </Button>
  </Surface>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  mt20: {
    marginTop: 20,
  },
  logoutSection: {
    padding: 16,
    marginBottom: 20,
  },
  logoutButton: {
    borderColor: '#B3261E',
  },
})