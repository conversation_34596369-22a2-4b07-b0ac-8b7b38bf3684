import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button } from 'react-native-paper';
import { 
  showErrorDialog, 
  showSuccessDialog, 
  showWarningDialog, 
  showConfirmationDialog,
  useDialogStore 
} from '../src/stores';

/**
 * Example component demonstrating how to use the global dialog system
 * This component shows different ways to trigger dialogs throughout your app
 */
export const DialogExamples: React.FC = () => {
  const { showDialog } = useDialogStore();

  // Example: Simple error dialog
  const handleShowError = () => {
    showErrorDialog(
      'Something went wrong. Please try again later.',
      'Network Error'
    );
  };

  // Example: Success dialog
  const handleShowSuccess = () => {
    showSuccessDialog(
      'Your event has been created successfully!',
      'Event Created'
    );
  };

  // Example: Warning dialog
  const handleShowWarning = () => {
    showWarningDialog(
      'This action cannot be undone. Are you sure you want to continue?',
      'Warning'
    );
  };

  // Example: Confirmation dialog
  const handleShowConfirmation = () => {
    showConfirmationDialog({
      title: 'Delete Event',
      message: 'Are you sure you want to delete this event? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: () => {
        console.log('Event deleted');
        showSuccessDialog('Event has been deleted successfully.');
      },
      onCancel: () => {
        console.log('Delete cancelled');
      },
    });
  };

  // Example: Custom dialog with multiple buttons
  const handleShowCustom = () => {
    showDialog({
      type: 'info',
      title: 'Save Changes',
      message: 'You have unsaved changes. What would you like to do?',
      dismissible: false,
      buttons: [
        {
          text: 'Discard',
          mode: 'outlined',
          style: 'destructive',
          onPress: () => {
            console.log('Changes discarded');
          },
        },
        {
          text: 'Save Draft',
          mode: 'outlined',
          style: 'secondary',
          onPress: () => {
            console.log('Draft saved');
            showSuccessDialog('Draft saved successfully');
          },
        },
        {
          text: 'Publish',
          mode: 'contained',
          style: 'primary',
          onPress: () => {
            console.log('Content published');
            showSuccessDialog('Content published successfully');
          },
        },
      ],
    });
  };

  // Example: API error handling
  const handleApiError = () => {
    // Simulate an API error response
    const apiError = {
      status: 400,
      message: 'Invalid request parameters',
      details: 'The event date must be in the future'
    };

    showErrorDialog(
      `${apiError.message}\n\nDetails: ${apiError.details}`,
      `API Error (${apiError.status})`
    );
  };

  return (
    <View style={styles.container}>
      <Button 
        mode="contained" 
        onPress={handleShowError}
        style={styles.button}
      >
        Show Error Dialog
      </Button>

      <Button 
        mode="contained" 
        onPress={handleShowSuccess}
        style={styles.button}
      >
        Show Success Dialog
      </Button>

      <Button 
        mode="contained" 
        onPress={handleShowWarning}
        style={styles.button}
      >
        Show Warning Dialog
      </Button>

      <Button 
        mode="contained" 
        onPress={handleShowConfirmation}
        style={styles.button}
      >
        Show Confirmation Dialog
      </Button>

      <Button 
        mode="contained" 
        onPress={handleShowCustom}
        style={styles.button}
      >
        Show Custom Dialog
      </Button>

      <Button 
        mode="contained" 
        onPress={handleApiError}
        style={styles.button}
      >
        Show API Error
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    gap: 12,
  },
  button: {
    marginVertical: 4,
  },
});

// Usage examples in other components:

/*
// 1. In a form submission handler:
const handleSubmit = async () => {
  try {
    await submitForm(formData);
    showSuccessDialog('Form submitted successfully!');
  } catch (error) {
    showErrorDialog(error.message || 'Failed to submit form');
  }
};

// 2. Before deleting an item:
const handleDelete = (itemId: string) => {
  showConfirmationDialog({
    title: 'Delete Item',
    message: 'This item will be permanently deleted.',
    onConfirm: () => deleteItem(itemId),
  });
};

// 3. In an API error interceptor:
const apiClient = axios.create();
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status >= 400) {
      showErrorDialog(
        error.response.data.message || 'An error occurred',
        `Error ${error.response.status}`
      );
    }
    return Promise.reject(error);
  }
);

// 4. For validation errors:
const validateForm = (data) => {
  if (!data.email) {
    showWarningDialog('Please enter your email address');
    return false;
  }
  return true;
};
*/ 