# Analytics & Remote Config Setup

This document explains how to use the newly implemented Expo Analytics and Remote Config features in the Selfinvite mobile app.

## 🎯 What's Included

### ✅ Expo Analytics
- **User behavior tracking** - Track user actions, screen views, and business events
- **Custom events** - Track specific app interactions
- **User properties** - Associate data with users
- **Privacy-focused** - GDPR compliant analytics

### ✅ Remote Config & Updates
- **Feature flags** - Enable/disable features remotely
- **A/B testing** - Test different UI variants
- **Dynamic content** - Update app content without app store releases
- **Over-the-air updates** - Push updates instantly

## 🚀 Quick Start

### 1. Using Analytics

```typescript
import { useAnalytics, useScreenTracking } from '../hooks/useAnalytics';

function MyComponent() {
  const analytics = useAnalytics();
  
  // Automatically track screen views
  useScreenTracking('My Screen', { 
    user_type: 'premium' 
  });
  
  // Track custom events
  const handleButtonPress = () => {
    analytics.trackAction('button_clicked', {
      button_name: 'subscribe',
      screen: 'my_screen'
    });
  };
  
  // Track business events
  const handleEventCreated = (eventId: string) => {
    analytics.trackEventCreated(eventId, {
      event_type: 'dinner',
      location: 'Paris',
      price: 25
    });
  };
}
```

### 2. Using Remote Config

```typescript
import { useRemoteConfig, useFeatureFlag, useABTest } from '../hooks/useRemoteConfig';

function MyComponent() {
  const remoteConfig = useRemoteConfig();
  const { isEnabled: newFeatureEnabled } = useFeatureFlag('enableNewSearchUI');
  const { variant: searchVariant } = useABTest('searchUIVariant');
  
  // Use feature flags
  if (newFeatureEnabled) {
    return <NewSearchUI />;
  }
  
  // Use A/B test variants
  const getSearchComponent = () => {
    switch (searchVariant) {
      case 'modern': return <ModernSearch />;
      case 'minimal': return <MinimalSearch />;
      default: return <ClassicSearch />;
    }
  };
  
  // Get dynamic content
  const welcomeMessage = remoteConfig.get('welcomeMessage');
  
  return (
    <View>
      <Text>{welcomeMessage}</Text>
      {getSearchComponent()}
    </View>
  );
}
```

## 📊 Available Analytics Events

### Screen Tracking
- Automatically tracks screen views with context
- Includes feature flag states and user properties

### User Actions
- `button_clicked` - Generic button interactions
- `event_viewed` - When users view event details
- `filter_applied` - When users apply search filters
- `search_performed` - When users search for events

### Business Events
- `event_created` - When users create new events
- `event_joined` - When users join events
- `payment_initiated` - When payments start
- `payment_completed` - When payments succeed

## 🎛️ Available Feature Flags

### Feature Toggles
- `enableNewSearchUI` - Enable new search interface
- `enableAdvancedFilters` - Enable advanced filtering options
- `enableSocialFeatures` - Enable social features
- `enablePushNotifications` - Enable push notifications

### A/B Tests
- `searchUIVariant` - Test different search UI designs
  - `classic` - Original search interface
  - `modern` - Modern design with enhanced features
  - `minimal` - Simplified, clean interface
- `onboardingFlow` - Test different onboarding experiences
  - `standard` - Standard onboarding flow
  - `quick` - Quick setup flow
  - `detailed` - Detailed onboarding with more steps

## 🔧 Configuration

### Remote Config Values
- `maxEventImages` - Maximum images per event (default: 5)
- `maxEventDescriptionLength` - Max description length (default: 500)
- `searchRadiusKm` - Default search radius (default: 50)
- `welcomeMessage` - Dynamic welcome message
- `apiBaseUrl` - API endpoint configuration
- `searchApiUrl` - Search service endpoint

## 📱 Example Implementation

See `src/components/AnalyticsExample.tsx` for a complete working example that demonstrates:
- Analytics initialization
- Event tracking
- Feature flag usage
- A/B testing
- Remote config updates
- Over-the-air updates

## 🔄 Updates & Deployment

### Over-the-Air Updates
```typescript
const remoteConfig = useRemoteConfig();

// Check for updates
const hasUpdate = await remoteConfig.checkForUpdates();

// Apply updates
await remoteConfig.applyUpdates();
```

### EAS Build Integration
The analytics and remote config are automatically included in EAS builds. No additional configuration needed.

## 🎯 Best Practices

### Analytics
1. **Track meaningful events** - Focus on user actions that drive business value
2. **Include context** - Add relevant properties to events
3. **Respect privacy** - Don't track sensitive user data
4. **Use consistent naming** - Follow a clear naming convention

### Remote Config
1. **Test thoroughly** - Always test feature flags in development
2. **Gradual rollouts** - Use A/B tests to validate changes
3. **Monitor impact** - Track how changes affect user behavior
4. **Have rollback plans** - Be ready to disable features quickly

## 🚨 Important Notes

- **Analytics is opt-in** - Users can disable tracking in settings
- **Remote config is cached** - Changes may take up to 5 minutes to propagate
- **Updates require restart** - Over-the-air updates need app restart
- **Feature flags are safe** - Disabled features won't break the app

## 🔍 Monitoring

### Analytics Dashboard
Access your analytics data through the Expo dashboard or integrate with your preferred analytics platform.

### Remote Config Monitoring
- Monitor feature flag usage in your analytics
- Track A/B test performance
- Monitor update success rates

## 🆘 Troubleshooting

### Analytics Not Working
1. Check if analytics is initialized
2. Verify user permissions
3. Check network connectivity
4. Review console logs for errors

### Remote Config Not Updating
1. Check network connectivity
2. Verify cache duration (5 minutes)
3. Force refresh configuration
4. Check for app updates

### Updates Not Applying
1. Verify EAS Updates is enabled
2. Check update channel configuration
3. Ensure app is not in development mode
4. Review update logs

## 📚 Further Reading

- [Expo Analytics Documentation](https://docs.expo.dev/guides/analytics/)
- [Expo Updates Documentation](https://docs.expo.dev/guides/config-plugins/)
- [React Native Analytics Best Practices](https://reactnative.dev/docs/analytics)
- [Feature Flag Best Practices](https://docs.launchdarkly.com/guides/flags/best-practices)
