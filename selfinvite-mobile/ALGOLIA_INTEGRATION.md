# Algolia Integration for Selfinvite Mobile

This document explains how to use the Algolia search integration in the Selfinvite mobile app, which replaces the previous backend API calls with direct Algolia client integration.

## Overview

The Algolia integration allows the mobile app to search events directly through Algolia's search API, providing faster search results and better user experience. The integration maintains backward compatibility with the existing search interface while leveraging Algolia's powerful search capabilities.

## Setup

### 1. Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# Algolia Configuration
EXPO_PUBLIC_ALGOLIA_APP_ID=your_algolia_app_id
EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY=your_algolia_search_api_key
```

### 2. Dependencies

The following packages are required and have been installed:

```bash
yarn add algoliasearch react-instantsearch-core
```

## Architecture

### Core Components

1. **AlgoliaSearchService** (`src/services/algoliaService.ts`)
   - Main service class that handles all Algolia API interactions
   - Transforms Algolia responses to match the existing `SearchResults` interface
   - Provides methods for various search types (text, filters, location-based, etc.)

2. **Updated Search API** (`src/api/searchApi.ts`)
   - Modified to use Algolia service instead of backend endpoints
   - Maintains the same interface for backward compatibility
   - All existing search functions now use Algolia

3. **Algolia Search Hooks** (`src/hooks/useAlgoliaSearch.ts`)
   - React Query hooks specifically for Algolia integration
   - Provide caching, background updates, and error handling
   - Include specialized hooks for different search types

4. **Example Component** (`src/components/AlgoliaSearchExample.tsx`)
   - Demonstrates how to use the Algolia integration
   - Shows various search patterns and use cases

## Usage

### Basic Search

```typescript
import { useAlgoliaSearch } from '../hooks/useAlgoliaSearch';

const MyComponent = () => {
  const { data, isLoading, error } = useAlgoliaSearch('italian dinner', {
    city: 'Paris',
    priceRange: [20, 50]
  });

  if (isLoading) return <Text>Loading...</Text>;
  if (error) return <Text>Error: {error.message}</Text>;

  return (
    <FlatList
      data={data?.hits || []}
      renderItem={({ item }) => <EventCard event={item} />}
    />
  );
};
```

### Infinite Scroll Search

```typescript
import { useInfiniteAlgoliaSearch } from '../hooks/useAlgoliaSearch';

const InfiniteSearchComponent = () => {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useInfiniteAlgoliaSearch('cooking class');

  const allEvents = data?.pages?.flatMap(page => page.hits) || [];

  return (
    <FlatList
      data={allEvents}
      renderItem={({ item }) => <EventCard event={item} />}
      onEndReached={() => hasNextPage && fetchNextPage()}
      onEndReachedThreshold={0.5}
      ListFooterComponent={() => 
        isFetchingNextPage ? <ActivityIndicator /> : null
      }
    />
  );
};
```

### Search with Filters

```typescript
import { useAlgoliaSearchWithStore } from '../hooks/useAlgoliaSearch';

const FilteredSearchComponent = () => {
  const {
    events,
    totalCount,
    isLoading,
    facetDistribution
  } = useAlgoliaSearchWithStore('dinner', {
    city: 'Paris',
    eventTypes: ['DINNER', 'LUNCH'],
    kitchenTypes: ['ITALIAN', 'FRENCH'],
    priceRange: [20, 50],
    dateRange: ['2024-01-01', '2024-12-31']
  });

  return (
    <View>
      <Text>Found {totalCount} events</Text>
      <FlatList
        data={events}
        renderItem={({ item }) => <EventCard event={item} />}
      />
    </View>
  );
};
```

### Location-Based Search

```typescript
import { useAlgoliaNearbySearch } from '../hooks/useAlgoliaSearch';

const NearbySearchComponent = () => {
  const { data, isLoading } = useAlgoliaNearbySearch(
    48.8566, // latitude
    2.3522,  // longitude
    10,      // radius in km
    { eventTypes: ['DINNER'] }
  );

  return (
    <FlatList
      data={data?.hits || []}
      renderItem={({ item }) => <EventCard event={item} />}
    />
  );
};
```

### Search Suggestions

```typescript
import { useAlgoliaSearchSuggestions } from '../hooks/useAlgoliaSearch';

const SearchSuggestionsComponent = ({ query }: { query: string }) => {
  const { data: suggestions } = useAlgoliaSearchSuggestions(query);

  return (
    <FlatList
      data={suggestions || []}
      renderItem={({ item }) => (
        <TouchableOpacity onPress={() => handleSuggestionPress(item.text)}>
          <Text>{item.highlighted}</Text>
        </TouchableOpacity>
      )}
    />
  );
};
```

## Search Filters

The integration supports comprehensive filtering options:

### Location Filters
- `city`: Filter by city name
- `country`: Filter by country
- `coordinates`: Geographic coordinates for location-based search
- `radius`: Search radius in kilometers

### Category Filters
- `kitchenTypes`: Array of cuisine types (e.g., ['ITALIAN', 'FRENCH'])
- `beverageTypes`: Array of beverage types
- `eventTypes`: Array of event types (e.g., ['DINNER', 'LUNCH'])
- `locationTypes`: Array of location types
- `intolerances`: Array of dietary restrictions

### Numeric Filters
- `priceRange`: [min, max] price range in euros
- `durationRange`: [min, max] duration in minutes
- `maxParticipants`: Maximum number of participants

### Date Filters
- `dateRange`: [startDate, endDate] as ISO strings

### Status Filters
- `status`: Array of event statuses
- `hasAvailableSpots`: Boolean for availability
- `isPrivate`: Boolean for private events

## Backward Compatibility

The integration maintains full backward compatibility with the existing search interface:

- All existing search hooks continue to work
- The `SearchResults` interface remains unchanged
- Search filters use the same structure
- Error handling follows the same patterns

## Migration from Backend API

The migration is seamless:

1. **No code changes required** in existing components using search hooks
2. **Environment variables** need to be updated with Algolia credentials
3. **Search behavior** remains the same from the user's perspective
4. **Performance improvements** are automatic due to direct Algolia integration

## Error Handling

The integration includes comprehensive error handling:

```typescript
const { data, error, isLoading } = useAlgoliaSearch('query');

if (error) {
  // Handle search errors
  console.error('Search failed:', error);
}
```

## Health Monitoring

Monitor Algolia service health:

```typescript
import { useAlgoliaHealth } from '../hooks/useAlgoliaSearch';

const HealthCheck = () => {
  const { data: isHealthy, error } = useAlgoliaHealth();
  
  return (
    <Text>
      Algolia Status: {isHealthy ? 'Healthy' : 'Unhealthy'}
    </Text>
  );
};
```

## Performance Considerations

1. **Caching**: React Query provides automatic caching of search results
2. **Debouncing**: Search suggestions are debounced to reduce API calls
3. **Pagination**: Infinite scroll reduces initial load time
4. **Background Updates**: Stale data is refreshed in the background

## Testing

Use the example component to test the integration:

```typescript
import { AlgoliaSearchExample } from '../components/AlgoliaSearchExample';

// Add to your app for testing
<AlgoliaSearchExample />
```

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   - Ensure `EXPO_PUBLIC_ALGOLIA_APP_ID` and `EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY` are set
   - Restart the development server after adding environment variables

2. **Search Not Working**
   - Check Algolia health status using `useAlgoliaHealth()`
   - Verify API keys have search permissions
   - Check network connectivity

3. **No Results**
   - Verify the Algolia index contains data
   - Check if filters are too restrictive
   - Ensure the index name matches `event_offers`

### Debug Mode

Enable debug logging by checking the console for search-related logs:

```typescript
// Search requests and responses are logged
log.info('Algolia search request:', searchParams);
log.debug('Algolia search response:', response);
```

## Future Enhancements

Potential improvements for the Algolia integration:

1. **Analytics Integration**: Track search behavior and popular queries
2. **Personalization**: Use Algolia's personalization features
3. **A/B Testing**: Test different search configurations
4. **Advanced Filtering**: Implement more sophisticated filter combinations
5. **Search Analytics**: Monitor search performance and user behavior

## Support

For issues related to the Algolia integration:

1. Check the console logs for detailed error messages
2. Verify environment configuration
3. Test with the example component
4. Review Algolia dashboard for index status and usage
