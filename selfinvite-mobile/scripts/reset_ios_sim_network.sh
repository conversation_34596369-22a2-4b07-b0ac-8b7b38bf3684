#!/bin/bash

echo "🔧 Fixing iOS Simulator Network Issues..."

# Kill all simulators
echo "1. Shutting down simulators..."
xcrun simctl shutdown all

# Clear DNS cache
echo "2. Clearing DNS cache..."
sudo dscacheutil -flushcache
sudo killall -HUP mDNSResponder

# Reset network interfaces
echo "3. Resetting network interfaces..."
sudo ifconfig en0 down
sudo ifconfig en0 up

# Clear Expo/Metro cache
echo "4. Clearing Expo/Metro cache..."
rm -rf node_modules/.cache
rm -rf .expo

# Kill any lingering Metro processes
echo "5. Killing Metro processes..."
killall -9 node 2>/dev/null || true
killall -9 Metro 2>/dev/null || true

# Wait a moment
sleep 2

# Start fresh
echo "6. Starting fresh..."
echo "Run: npx expo start --tunnel"
echo "Or: npx expo start --localhost"

echo "✅ Network reset complete!"
echo ""
echo "Next steps:"
echo "1. Start your development server"
echo "2. Open a fresh simulator"
echo "3. If still having issues, try --tunnel flag"