# Selfinvite - Social Eating Platform PRD

## Overview  
Selfinvite is a social dining platform that connects people across Europe for shared culinary experiences through collaborative consumption.
The app allows users to either host food events (dinners, lunches, picnics, etc.) or join events created by others, fostering real connections through food and community.

**Problem Solved:** Social isolation and lack of authentic local experiences, especially for travelers and people new to cities.
Traditional dining apps focus on delivery/reservations, but don't facilitate human connection.

**Target Users:** 
- Social food enthusiasts aged 25-60
- Travelers seeking authentic local experiences  
- Residents wanting to meet new people in their city
- People with specific dietary preferences/restrictions

**Value Proposition:** "#BeSocialforReal" - Experience authentic local food culture while making genuine connections with like-minded people.

## Core Features  

### 1. Event Management System
**What it does:** Complete lifecycle management for food events
- Create events with rich details (cuisine type, location, price per person, duration, max participants, gps location, intolerances)
- Upload multiple event photos with automatic content moderation
- Edit/delete events with 24-hour restriction before event time
- Support for public and private events
- Clone existing events for recurring hosts

**Why it's important:** Core business logic enabling the marketplace between hosts and guests

**How it works:** Redux-based state management with form validation, image upload to cloud, geolocation integration

### 2. Advanced Search & Discovery
**What it does:** Algolia-powered search with sophisticated filtering
- Text search across event names, descriptions, locations
- Faceted filtering: cuisine type, beverages, event type, location type, dietary restrictions
- Numeric filters: price range, duration, number of guests, distance from user
- Date range selection
- Real-time filter chip management

**Why it's important:** Primary user acquisition and engagement mechanism - users must easily find relevant events

**How it works:** Algolia search index with complex query building, location-based distance calculations, filter state management

### 3. User Authentication & Profile Management  
**What it does:** Secure user onboarding and profile management
- Auth0 social login integration
- User profile creation with avatar, preferences, dietary restrictions
- Phone number verification with OTP
- Account preferences and user blocking functionality
- KYC document upload for payment verification

**Why it's important:** Trust and safety foundation for peer-to-peer interactions

**How it works:** Auth0 JWT tokens, secure token storage, profile data management via Redux

### 4. Booking & Participation Management
**What it does:** Workflow for joining events
- Send participation bookings with guest count
- Host approval/rejection workflow  
- Payment processing integration
- Booking status tracking (pending_confirmation (to be accepted), confirmed (to be paid), approved, paid, cancelled)
- Refund handling

**Why it's important:** Core transaction flow enabling monetization

**How it works:** EventOfferKV entity system tracking booking states, Stripe payment integration

### 5. Real-time Messaging System
**What it does:** Communication between hosts and guests
- One-on-one chat functionality using GiftedChat
- Image sharing capabilities
- Push notifications for new messages
- Message read status tracking
- Contact list management

**Why it's important:** Essential for coordination and trust-building between users

**How it works:** WebSocket-based messaging with Firebase push notifications, thread-based conversation management

### 6. Payment Processing
**What it does:** Secure payment handling
- Stripe integration with Apple Pay/Google Pay support
- KYC verification for hosts
- Payment intent creation and processing
- Bank account management for payouts
- Transaction status tracking

**Why it's important:** Revenue generation and financial trust

**How it works:** Stripe Payment Sheet integration, managed KYC flows, webhook handling for payment status

### 7. Review & Feedback System
**What it does:** Post-event rating and review system
- Bidirectional feedback (host ↔ guest)
- Rating system with written reviews
- Profile reputation building
- Review moderation capabilities

**Why it's important:** Trust building and quality assurance mechanism

**How it works:** Feedback entity system with sender/receiver tracking, integration with user profiles

## User Experience  

### User Personas
1. **The Social Host (Emma, 32, Marketing Manager)**
   - Loves cooking and meeting new people
   - Uses Selfinvite to share cultural experiences
   - Monetizes her cooking passion

2. **The Curious Traveler (Marco, 28, Software Developer)**  
   - Travels frequently for work
   - Seeks authentic local experiences beyond tourist restaurants
   - Values genuine cultural immersion

3. **The Urban Explorer (Sarah, 35, Teacher)**
   - New to the city, wants to build social connections
   - Interested in specific dietary communities (vegan, gluten-free)
   - Limited time but high interest in social activities

### Key User Flows
1. **Guest Discovery Journey:** Browse → Filter by multiple event criterias → View Details → Book → Pay → Attend → Review
2. **Host Creation Journey:** Create Event → Set Details → Manage Bookings → Accept/Reject → Host → Feedback
3. **Communication Flow:** After event paid -> message chat is open between host and guest -> system messages of the previous steps are included in the thread
4. **Guest Cancel Journey:** Browse → Filter by multiple event criterias → View Details → Book → Pay → Cancel
5. **Guest Asks Refund, Host Accepts Journey:** Browse → Filter by multiple event criterias → View Details → Book → Pay → Attend -> Ask for Refund -> (host accepts) -> payment refund
6. **Guest Asks Refund, Host Rejects Journey:** Browse → Filter by multiple event criterias → View Details → Book → Pay → Attend -> Ask for Refund -> (host refuses) -> payment not refund


### UI/UX Considerations
- Material Design principles with React Native Paper
- Bottom navigation for main features
- Image-first event presentation
- Intuitive filter system with visual chips
- Real-time messaging interface
- Seamless payment flow integration

## Technical Architecture  

### System Components
**Frontend (React Native):**
- Expo SDK 50.0.0 framework
- Redux + Redux Saga for state management
- Expo Router for routing
- React Native Paper for UI components

**Backend (Go Serverless on Vercel + Supabase):**
- Core microservice - Business logic (EventOffer, Message, Feedback, Payment)
- Postgres databases with Elasticsearch indexing
- Hazelcast distributed caching

**Third-Party Services:**
- Auth0: Authentication and user management
- Stripe: Payment processing and KYC
- Algolia: Search and filtering
- Firebase: Push notifications and analytics
- Cloud: File/image storage
- OpenStreetMap: Geocoding and mapping

### Data Models
**Core Entities:**
- EventOffer: Event details, location, pricing, metadata
- EventBooking: Booking/participation tracking with key-value flexibility
- Message: Thread-based messaging system
- Feedback: from host to guest feedback system
- Review: from guest to host review system
- Payment: Transaction tracking and status
- User: Account details, preferences, blocking

**Key Relationships:**
- Events belong to hosts (Users)
- EventBooking links users to events with booking status
- Messages form threaded conversations between users
- Feedback creates relationships host to guest post-event
- Review creates relationships guest to host post-event

### APIs and Integrations
- RESTful APIs with HAL+JSON format
- Auth0 OAuth2/OpenID Connect integration
- Stripe API for payment processing
- Algolia Search API for discovery
- Firebase Cloud Messaging for push notifications
- Nominatim API for geocoding

### Infrastructure Requirements
- Postgres databases (production and development)
- Elasticsearch cluster for search indexing
- Redis/Hazelcast for session management
- Cloud Blob Storage for images
- CDN for asset delivery
- Load balancing for high availability

## Development Roadmap  

### MVP Foundation (Already Implemented)
- User authentication and profile management
- Event creation and basic search
- Simple booking/approval workflow
- Basic messaging functionality
- Core payment integration

### Current Production Features (v3.0.0)
- Advanced Algolia search with sophisticated filtering
- Real-time messaging with image sharing
- Stripe payment integration with Apple/Google Pay
- Review and feedback system
- Multi-language support (6 languages)
- Push notification system
- KYC verification for hosts

### Future Enhancements (Potential Phase 2)
- Advanced recommendation engine using ML
- Social features (friend connections, event sharing)
- Integration with calendar applications
- Host verification and certification program
- Dynamic pricing suggestions
- Event analytics and insights dashboard

### Future Technical Improvements
- Enhanced offline capabilities
- Performance optimization for large event catalogs
- Advanced analytics and user behavior tracking
- A/B testing framework integration

## Logical Dependency Chain

### Foundation Layer (Critical for Basic Functionality)
1. **Authentication System**: Must be rock-solid for trust
2. **User Profile Management**: Essential for personalization and safety
3. **Event Creation/Management**: Core host functionality
4. **Basic Search/Discovery**: Core guest functionality

### Transaction Layer (Enables Monetization)
5. **Booking/Participation System**: Bridges hosts and guests
6. **Payment Processing**: Enables revenue generation
7. **Messaging System**: Facilitates coordination

### Trust & Quality Layer (Drives Retention)
8. **Review/Feedback System**: Builds community trust
9. **Advanced Search Features**: Improves discovery
10. **Push Notifications**: Enhances engagement

### Scaling & Optimization (Long-term Growth)
11. **Analytics and Insights**: Data-driven improvements
12. **Advanced Matching**: ML-powered recommendations
13. **Social Features**: Network effects

## Risks and Mitigations  

### Technical Challenges
**Risk:** Complex microservices architecture maintenance
**Mitigation:** Well-documented APIs, comprehensive testing, service monitoring

**Risk:** Payment processing compliance and security
**Mitigation:** Stripe handles PCI compliance, KYC verification implemented

**Risk:** Real-time messaging scalability
**Mitigation:** Firebase provides robust infrastructure, thread-based architecture

### Business Risks
**Risk:** Trust and safety in peer-to-peer transactions  
**Mitigation:** Review system, user verification, reporting mechanisms, content moderation

**Risk:** Market competition from established platforms
**Mitigation:** Focus on authentic local experiences and community building vs. commercial dining

**Risk:** Regulatory compliance across European markets
**Mitigation:** GDPR compliance implemented, local payment method support

### Technical Debt
**Risk:** Legacy React Native architecture
**Mitigation:** Gradual migration to New Architecture, component modernization

**Risk:** Complex state management with Redux Saga
**Mitigation:** Well-documented patterns, consider modern alternatives like RTK Query

## Appendix  

### Technical Specifications
- **Platform:** iOS 12+, Android 8+
- **Backend:** Go 1.20, Postgres
- **Performance:** Target 60fps UI, <3s search response times
- **Security:** OAuth2, JWT tokens, encrypted data transmission
- **Scalability:** Serverless architecture supports horizontal scaling

### Market Research Findings
- Primary market: European urban areas with high tourist traffic
- Strong demand for authentic local experiences over commercialized dining
- Users willing to pay premium for curated, personal experiences
- Safety and trust are primary concerns for adoption

### Success Metrics
- User engagement: Events created per active user
- Transaction completion: Booking-to-payment conversion rate
- Trust indicators: Average review scores, repeat participation
- Geographic expansion: City-by-city growth tracking

The platform represents a mature, well-architected solution for social dining that successfully bridges 
the gap between hosts wanting to share their culture and guests seeking authentic experiences.
The technical foundation supports current operations while providing flexibility for future enhancements and scaling. 


## Migration strategy
- expo 53
- social login with supabase
- redux saga alternatives?
- search with Meilisearch
- payment with stripe
- event requests are now called event bookings


## Architecture Improvement Suggestions

### 1. State Management Modernization
**Current:** Redux + Redux Saga (complex, boilerplate-heavy)
**Recommended:** 
- **RTK Query + Redux Toolkit**: Eliminates 90% of Redux boilerplate, built-in caching, automatic re-fetching
- **Alternative:** TanStack Query (React Query) + Zustand for lighter state management
- **Benefits:** Simplified data fetching, automatic background updates, optimistic updates, better TypeScript support

```typescript
// RTK Query example for events
const eventsApi = createApi({
  reducerPath: 'eventsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/',
    prepareHeaders: (headers, { getState }) => {
      headers.set('authorization', `Bearer ${getToken(getState())}`)
    },
  }),
  tagTypes: ['Event', 'EventBooking'],
  endpoints: (builder) => ({
    getEvents: builder.query({
      query: (filters) => `events?${new URLSearchParams(filters)}`,
      providesTags: ['Event'],
    }),
    createEventBooking: builder.mutation({
      query: (booking) => ({
        url: 'event-bookings',
        method: 'POST',
        body: booking,
      }),
      invalidatesTags: ['EventBooking'],
    }),
  }),
})
```

### 2. Push Notifications Modernization
**Current:** Firebase Cloud Messaging (basic)
**Recommended:** 
- **Expo Notifications**: Native Expo SDK for notifications with better TypeScript support
- **Supabase Realtime**: For real-time updates without push notifications overhead
- **OneSignal**: More advanced segmentation and A/B testing capabilities

```typescript
// Expo Notifications setup
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';

const registerForPushNotifications = async () => {
  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    const token = (await Notifications.getExpoPushTokenAsync()).data;
    return token;
  }
};
```

### 3. Real-time Features Enhancement
**Current:** Basic messaging with Firebase
**Recommended:**
- **Supabase Realtime**: Real-time subscriptions for events, messages, requests
- **React Native WebSocket**: For custom real-time features
- **Benefits:** Instant updates for event changes, live chat, real-time availability

```typescript
// Supabase realtime example
const supabase = createClient(url, key);

useEffect(() => {
  const subscription = supabase
    .channel('event-updates')
    .on('postgres_changes', 
      { event: 'UPDATE', schema: 'public', table: 'events' },
      (payload) => {
        // Update local state with real-time changes
        queryClient.invalidateQueries(['events']);
      }
    )
    .subscribe();

  return () => supabase.removeChannel(subscription);
}, []);
```

### 4. Search Architecture Improvement
**Current:** Algolia (expensive for large datasets)
**Recommended:** Meilisearch (as you mentioned) or Typesense
- **Meilisearch**: Open-source, typo-tolerant, instant results
- **Benefits:** Cost-effective, self-hosted option, excellent filtering
- **Implementation:** Docker deployment on Vercel or Railway

```typescript
// Meilisearch integration
import { MeiliSearch } from 'meilisearch';

const client = new MeiliSearch({
  host: 'https://your-meilisearch-instance.com',
  apiKey: 'your-api-key',
});

const searchEvents = async (query: string, filters: EventFilters) => {
  const index = client.index('events');
  
  return await index.search(query, {
    filter: [
      `typeKs IN [${filters.cuisine.join(', ')}]`,
      `pricepp >= ${filters.minPrice} AND pricepp <= ${filters.maxPrice}`,
      `date > ${Date.now() / 1000}`
    ],
    facets: ['typeKs', 'typeBs', 'typeEs', 'typeLs'],
    limit: 20,
  });
};
```

### 5. Authentication & Security Improvements
**Current:** Auth0 (expensive)
**Recommended:** Supabase Auth (as you mentioned)
- **Benefits:** Built-in social providers, RLS policies, JWT handling
- **Features:** Magic links, phone auth, multi-factor authentication

```typescript
// Supabase Auth implementation
const { data, error } = await supabase.auth.signInWithOAuth({
  provider: 'google',
  options: {
    redirectTo: 'selfinvite://auth/callback',
    queryParams: {
      access_type: 'offline',
      prompt: 'consent',
    },
  },
});
```

### 6. Payment Processing Enhancement
**Current:** Stripe (good choice, keep it)
**Recommended Improvements:**
- **Stripe Payment Element**: Simplified integration
- **Stripe Connect**: Better marketplace functionality for host payouts
- **Payment Links**: Easier payment flow for events

### 7. Backend Architecture Simplification
**Current:** JHipster microservices (complex)
**Your Plan:** Go Serverless + Supabase (excellent choice)
**Additional Recommendations:**
- **Vercel Edge Functions**: For compute-heavy operations
- **Supabase Edge Functions**: For database-related logic
- **Cloudflare Workers**: For global CDN and edge computing

### 8. Mobile Development Improvements
**Expo SDK 53 Migration Benefits:**
- **Expo Router**: File-based routing system (simpler than React Navigation for complex apps)
- **Expo SQLite**: Local storage for offline capabilities
- **Expo Image**: Better image handling and caching
- **New Architecture**: Improved performance with React Native 0.74+

```typescript
// Expo Router example
// app/(tabs)/search.tsx
export default function SearchScreen() {
  return <SearchComponent />;
}

// app/event/[id].tsx
export default function EventDetailsScreen() {
  const { id } = useLocalSearchParams();
  return <EventDetails eventId={id} />;
}
```

### 9. Performance Optimizations
**Recommended:**
- **React Native Flipper**: Enhanced debugging
- **Expo Dev Tools**: Better development experience
- **React Native Performance**: Hermes engine, fabric renderer
- **Image optimization**: WebP format, lazy loading, progressive loading

### 10. Offline Capabilities
**Recommended:**
- **Expo SQLite + Drizzle ORM**: Local data persistence
- **React Query offline**: Cache management
- **Background sync**: Queue actions when offline

### Migration Priority Recommendation:
1. **Phase 1**: State management (RTK Query) + Expo 53 upgrade
2. **Phase 2**: Backend migration (Go + Supabase)
3. **Phase 3**: Search migration (Meilisearch)
4. **Phase 4**: Enhanced real-time features
5. **Phase 5**: Performance optimizations and offline capabilities

This modernized architecture will be more maintainable, cost-effective, and provide better developer experience while maintaining all current functionality.
