Android Credentials
Project                 selfinvite-mobile
Application Identifier  com.selfinvite.mobile

Push Notifications (FCM Legacy)
  None assigned yet

Push Notifications (FCM V1): Google Service Account Key For FCM V1
Project ID      selfinvite-938cf
Client Email    <EMAIL>
Client ID       100658276365478305938
Private Key ID  922d0b44ca71e42cfa1d08adf572b649ea77f4c2
Updated         3 minutes ago

Submissions: Google Service Account Key for Play Store Submissions
  None assigned yet

Configuration: Build Credentials opx1NCUSgk (Default)
Keystore
Type                JKS
Key Alias           6cc5d73e0fc753defe0ef3d1cfe42220
MD5 Fingerprint     76:F3:3F:37:CA:86:50:61:6B:27:D6:13:A8:5E:3D:23
SHA1 Fingerprint    26:1B:A1:B1:C6:C8:07:67:A9:67:B3:0C:23:6C:E4:A1:DB:97:C6:6A
SHA256 Fingerprint  02:23:D1:91:7B:61:64:44:56:E7:D0:B8:45:61:3E:39:B3:A9:17:CD:C9:0D:A6:97:BF:F4:3D:97:7B:3C:09:F4
Updated             18 days ago

Configuration: Build Credentials DdDltkbQ6C
Keystore