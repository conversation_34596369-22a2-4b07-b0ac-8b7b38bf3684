name: Test Environment Variables

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  test-env:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Test environment variables (local)
      run: node scripts/test-env.js
      env:
        # Test with empty values to ensure script works
        EXPO_PUBLIC_SUPABASE_URL: ""
        EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY: ""
        EXPO_PUBLIC_SUPABASE_ANON_KEY: ""
        EXPO_PUBLIC_ALGOLIA_APP_ID: "DP7DN903MK"
        EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY: ""
        EXPO_PUBLIC_APP_ENV: "test"
        GOOGLE_CLIENT_ID: ""
        GOOGLE_CLIENT_SECRET: ""
        
    - name: Test EAS build environment variables
      run: |
        echo "Testing EAS build environment variable resolution..."
        echo "EXPO_PUBLIC_SUPABASE_URL: ${EXPO_PUBLIC_SUPABASE_URL:-'NOT_SET'}"
        echo "EXPO_PUBLIC_APP_ENV: ${EXPO_PUBLIC_APP_ENV:-'NOT_SET'}"
        echo "GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-'NOT_SET'}"
      env:
        # These would be set by EAS secrets in real builds
        EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY }}
        EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.EXPO_PUBLIC_SUPABASE_ANON_KEY }}
        EXPO_PUBLIC_ALGOLIA_APP_ID: "DP7DN903MK"
        EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY: ${{ secrets.EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY }}
        EXPO_PUBLIC_APP_ENV: "preview"
        EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN: ${{ secrets.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN }}
        EXPO_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY }}
        GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
        GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
        __DEV__: "0"

  test-eas-build:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'yarn'
        
    - name: Install dependencies
      run: yarn install --frozen-lockfile
      
    - name: Install EAS CLI
      run: npm install -g @expo/eas-cli
      
    - name: Login to EAS
      run: eas login --non-interactive
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
        
    - name: Test EAS build with environment variables
      run: |
        echo "Testing EAS build with environment variables..."
        eas build --platform android --profile preview --non-interactive --no-wait
      env:
        EXPO_PUBLIC_SUPABASE_URL: ${{ secrets.EXPO_PUBLIC_SUPABASE_URL }}
        EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY }}
        EXPO_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.EXPO_PUBLIC_SUPABASE_ANON_KEY }}
        EXPO_PUBLIC_ALGOLIA_APP_ID: "DP7DN903MK"
        EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY: ${{ secrets.EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY }}
        EXPO_PUBLIC_APP_ENV: "preview"
        EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN: ${{ secrets.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN }}
        EXPO_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY }}
        GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
        GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
        __DEV__: "0"
