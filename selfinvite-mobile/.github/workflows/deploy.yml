name: Deploy to App Stores

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to deploy to'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - android
        - ios

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'yarn'

      - name: Setup Yarn
        run: |
          npm install -g yarn@1.22.22
          yarn --version

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Configure EAS
        run: |
          echo ${{ secrets.EAS_CREDENTIALS }} | base64 -d > eas-credentials.json
          eas credentials --non-interactive --credentials-path eas-credentials.json

      - name: Deploy to Android
        if: github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all'
        run: |
          eas build --platform android --profile production --non-interactive
          eas submit --platform android --profile production --non-interactive

      - name: Deploy to iOS
        if: github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all'
        run: |
          eas build --platform ios --profile production --non-interactive
          eas submit --platform ios --profile production --non-interactive

      - name: Create Release
        if: startsWith(github.ref, 'refs/tags/')
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          body: |
            Automated release for version ${{ github.ref }}
            
            Changes in this release:
            - [Add your release notes here]
          draft: false
          prerelease: false