import { useEffect } from 'react';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useColorScheme, LogBox } from 'react-native';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as Linking from 'expo-linking';
import * as WebBrowser from 'expo-web-browser';
import { StripeProvider } from '@stripe/stripe-react-native';
import Constants from 'expo-constants';
import supabase from '../lib/supabase';
import { useAuthStore } from '../src/stores/authStore';
import { useOnboardingStore } from '../src/stores/onboardingStore';

import { lightTheme, darkTheme } from '../src/constants/theme';
import { GlobalDialog } from '../components/GlobalDialog';
import { NotificationProvider } from '../src/components/NotificationProvider';
import OnboardingScreen from '../src/screens/OnboardingScreen';
import log from '@/common/logger';

// Suppress specific warnings that are harmless but noisy
LogBox.ignoreLogs([
  'setLayoutAnimationEnabledExperimental is currently a no-op in the New Architecture',
]);

// Complete auth session for WebBrowser
WebBrowser.maybeCompleteAuthSession();

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();



if (__DEV__) {
  const originalFetch = global.fetch;
  global.fetch = async (...args) => {
    const url = args[0];
    
    // Skip logging internal Metro requests
    if (typeof url === 'string' && (
      url.includes('127.0.0.1:8081') || 
      url.includes('localhost:8081') ||
      url.includes('/symbolicate') ||
      url.includes('/status')
    )) {
      return originalFetch(...args);
    }
    
    log.info('🌐 Fetch Request:', url);
    try {
      const response = await originalFetch(...args);
      if (response.status >= 400) {
        throw new Error(`Fetch Error: ${response.status} ${response.statusText}`);
      }
      log.info('✅ Fetch Success:', response.status, url);
      return response;
    } catch (error: any) {
      log.error('❌ Fetch Error:', error.message, url);
      throw error;
    }
  };
}

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 2,
    },
  },
});

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const { setSession, refreshSession } = useAuthStore();
  const { hasCompletedOnboarding, isLoading: onboardingLoading } = useOnboardingStore();
  
  const [loaded] = useFonts({
    // SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Initialize auth session and set up auth state listener
  useEffect(() => {
    // Get initial session
    refreshSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      log.info('Auth state changed:', event, session?.user?.email);
      
      if (event === 'TOKEN_REFRESHED' && session) {
        log.info('Token refreshed, ensuring session is saved');
        // Ensure the refreshed session is saved
        await setSession(session);
      } else if (event === 'SIGNED_IN' && session) {
        log.info('User signed in, saving session');
        await setSession(session);
      } else if (event === 'SIGNED_OUT') {
        log.info('User signed out, clearing session');
        setSession(null);
      } else {
        // For other events, just update the session
        setSession(session);
      }
    });

    return () => subscription.unsubscribe();
  }, [setSession, refreshSession]);

  // Unified deep link handling for OAuth callbacks
  useEffect(() => {
    const handleDeepLink = async (url: string) => {
      log.info('Deep link received:', url);
      
      // Check if it's an auth callback (handles both selfinvite:// and https:// schemes)
      if (url.includes('/auth/callback') || url.startsWith('selfinvite://auth/callback') || url.startsWith('selfinvite:///auth/callback'))  {
        log.info('Handling auth callback URL:', url);
        
        // Close any opened auth browser on return (from SocialAuth behavior)
        try {
          WebBrowser.dismissBrowser();
        } catch (error) {
          // Browser might not be open, ignore error
        }

        try {
          // Handle both custom scheme and standard URL formats
          let urlToProcess = url;
          
          // If it's a custom scheme URL, we need to handle it differently
          if (url.startsWith('selfinvite://auth/callback') || url.startsWith('selfinvite:///auth/callback')) {
            // For custom scheme, check for established session
            const { data, error } = await supabase.auth.getSession();
            if (error) {
              log.error('Auth callback session error:', error);
            } else {
              log.info('Auth session established:', Boolean(data?.session));
            }
            return;
          }
          
          // For standard URLs, parse and exchange code for session
          const { hostname, pathname, search } = new URL(urlToProcess);
          
          // Parse the URL parameters
          const urlParams = new URLSearchParams(search);
          const code = urlParams.get('code');
          const error = urlParams.get('error');
          
          if (error) {
            log.error('OAuth error from URL:', error);
            return;
          }
          
          if (code) {
            log.info('Exchanging code for session...');
            const { data, error: sessionError } = await supabase.auth.exchangeCodeForSession(code);
            
            if (sessionError) {
              log.error('Session exchange error:', sessionError);
            } else {
              log.info('Session established successfully:', data);
            }
          }
        } catch (err) {
          log.error('Error parsing deep link:', err);
        }
      } else {
        log.info('Not managed deeplink:', url);
      }
    };

    // Handle initial URL if app was opened via deep link
    const getInitialURL = async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        await handleDeepLink(initialUrl);
      }
    };

    // Listen for incoming deep links while app is running
    const subscription = Linking.addEventListener('url', (event) => {
      handleDeepLink(event.url);
    });

    getInitialURL();

    return () => {
      subscription?.remove();
    };
  }, []);

  // Get Stripe publishable key from environment
  const stripePublishableKey = Constants.expoConfig?.extra?.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 
    process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY;

  if (!loaded || onboardingLoading) {
    return null;
  }

  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <QueryClientProvider client={queryClient}>
        <PaperProvider theme={theme}>

          <SafeAreaProvider>
            {!hasCompletedOnboarding ? (
              // Show onboarding screen if user hasn't completed it
              <OnboardingScreen />
            ) : (
              // Show main app if onboarding is completed
              <NotificationProvider>
                <StripeProvider publishableKey={stripePublishableKey || ''}>
                  <Stack>
                    <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                    <Stack.Screen name="event-details" options={{ headerShown: false }} />
                    <Stack.Screen name="booking-details" options={{ headerShown: false }} />
                    <Stack.Screen name="onboarding" options={{ headerShown: false }} />
                  </Stack>
                  <GlobalDialog />
                  <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
                </StripeProvider>
              </NotificationProvider>
            )}
          </SafeAreaProvider>
        </PaperProvider>
      </QueryClientProvider>
    </GestureHandlerRootView>
  );
}