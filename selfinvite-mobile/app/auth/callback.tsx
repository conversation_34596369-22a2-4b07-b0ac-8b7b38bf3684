import { useEffect, useState } from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { View, Text, ActivityIndicator, Alert } from 'react-native';
import { SocialAuth, GoogleAuthConfig } from '../../lib/auth';

export default function AuthCallback() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    handleAuthCallback();
  }, []);

  const handleAuthCallback = async () => {
    try {
      setIsProcessing(true);

      // Parse the callback parameters
      const { code, state, error, error_description } = params;

      if (error) {
        console.error('Auth error:', error, error_description);
        Alert.alert(
          'Authentication Error',
          error_description as string || 'An error occurred during authentication',
          [{ text: 'OK', onPress: () => router.replace('/(tabs)') }]
        );
        return;
      }

      if (code) {
        // Handle successful authentication
        console.log('Auth code received:', code);
        console.log('State:', state);

        // Initialize the appropriate auth provider
        // You might want to determine the provider from the state parameter
        const authProvider = new SocialAuth(GoogleAuthConfig);

        // Exchange code for tokens
        await authProvider.exchangeCodeForTokens(code as string);

        // Show success message and redirect
        Alert.alert(
          'Success',
          'Authentication successful!',
          [{ text: 'OK', onPress: () => router.replace('/(tabs)') }]
        );
      } else {
        // No code received, redirect to main app
        console.warn('No auth code received');
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error('Error handling auth callback:', error);
      Alert.alert(
        'Error',
        'Failed to complete authentication. Please try again.',
        [{ text: 'OK', onPress: () => router.replace('/(tabs)') }]
      );
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" />
      <Text style={{ marginTop: 16 }}>
        {isProcessing ? 'Processing authentication...' : 'Redirecting...'}
      </Text>
    </View>
  );
}
