import React from 'react';
import { ScrollView, StyleSheet, ActivityIndicator, View, RefreshControl } from 'react-native';
import { Surface, Text, Chip, Button } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { 
  useIncomingEventBookings, 
  useOutgoingEventBookings, 
  useRespondToEventBooking, 
  useAcceptParticipationEventBooking, 
  useRejectParticipationEventBooking,
  useCancelParticipationEventBooking 
} from '../../src/hooks/useEventBookings';
import type { AugmentedEventBooking, RespondEventBookingRequest } from '../../src/types';
import log from '@/common/logger';
import { BookingStatus } from '../../src/types/eventBooking';
import RequestCard from '../../src/components/RequestCard';
import { getCurrentUserId } from '../../src/stores';

type FilterType = 'incoming' | 'sent' | 'approved' | 'all';

export default function RequestsScreen() {
  const theme = useTheme();
  const [filterType, setFilterType] = React.useState<FilterType>('incoming');
  const [refreshing, setRefreshing] = React.useState(false);

  // Check if user is logged in
  const currentUserId = getCurrentUserId();
  const isLoggedIn = !!currentUserId;

  // Fetch data using React Query only if user is logged in
  const { data: incomingBookings = { data: [] }, isLoading: incomingLoading, error: incomingError, refetch: refetchIncoming } = useIncomingEventBookings();
  const { data: outgoingBookings = { data: [] }, isLoading: outgoingLoading, error: outgoingError, refetch: refetchOutgoing } = useOutgoingEventBookings();
  
  // Mutations for booking actions
  const respondToBooking = useRespondToEventBooking();
  const acceptParticipation = useAcceptParticipationEventBooking();
  const rejectParticipation = useRejectParticipationEventBooking();
  const cancelParticipation = useCancelParticipationEventBooking();

  // Handle pull-to-refresh
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([refetchIncoming(), refetchOutgoing()]);
    } catch (error) {
      log.error('Error refreshing bookings:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetchIncoming, refetchOutgoing]);

  // Handle booking response (legacy method using generic respond API)
  const handleBookingResponse = async (bookingId: string, status: BookingStatus, responseMessage?: string) => {
    try {
      let response: RespondEventBookingRequest = { event_booking: { id: bookingId, booking_status: status, event_offer_id: '', guest_user_id: '', number_of_guests: 1, total_amount_paid: 0, booked_at: '', notes_to_host: '', created_at: '', updated_at: '' }, message: responseMessage || '' };
      if (responseMessage) {
        response.message = responseMessage;
      }
      
      await respondToBooking.mutateAsync({
        bookingId,
        response
      });
    } catch (error) {
      log.error('Failed to respond to booking:', error);
    }
  };

  // Handle accepting a guest request (for hosts)
  const handleAcceptBooking = async (augmentedBooking: AugmentedEventBooking) => {
    try {
      const bookingData = {
        booking_id: augmentedBooking.event_booking?.id || '',
        number_of_guests: augmentedBooking.event_booking?.number_of_guests || 1,
        notes_to_host: augmentedBooking.event_booking?.message || ''
      };
      
      await acceptParticipation.mutateAsync(bookingData);
      log.info('Successfully accepted booking');
    } catch (error) {
      log.error('Failed to accept booking:', error);
    }
  };

  // Handle rejecting a guest request (for hosts)
  const handleRejectBooking = async (augmentedBooking: AugmentedEventBooking) => {
    try {
      const bookingData = {
        booking_id: augmentedBooking.event_booking?.id || '',
        number_of_guests: augmentedBooking.event_booking?.number_of_guests || 1,
        notes_to_host: augmentedBooking.event_booking?.message || ''
      };
      
      await rejectParticipation.mutateAsync(bookingData);
      log.info('Successfully rejected booking');
    } catch (error) {
      log.error('Failed to reject booking:', error);
    }
  };

  // Handle canceling own booking request (for guests)
  const handleCancelBooking = async (augmentedBooking: AugmentedEventBooking) => {
    try {
      const bookingData = {
        booking_id: augmentedBooking.event_booking?.id || '',
        number_of_guests: augmentedBooking.event_booking?.number_of_guests || 1,
        notes_to_host: augmentedBooking.event_booking?.message || ''
      };
      
      await cancelParticipation.mutateAsync(bookingData);
      log.info('Successfully canceled booking');
    } catch (error) {
      log.error('Failed to cancel booking:', error);
    }
  };

  // Get filtered bookings based on current filter
  const getFilteredBookings = (): AugmentedEventBooking[] => {
    if (!isLoggedIn) return [];
    
    // Now the data is properly structured as PaginatedResponse<AugmentedEventBooking>
    const incomingArray = incomingBookings.data || [];
    const outgoingArray = outgoingBookings.data || [];
    
    switch (filterType) {
      case 'incoming':
        return incomingArray.filter((augmentedBooking: any) => 
          augmentedBooking.event_booking?.booking_status === BookingStatus.PENDING_CONFIRMATION
        );
      case 'sent':
        return outgoingArray;
      case 'approved':
        return [...incomingArray, ...outgoingArray].filter((augmentedBooking: any) => 
          augmentedBooking.event_booking?.booking_status === BookingStatus.ACCEPTED
        );
      case 'all':
        return [...incomingArray, ...outgoingArray];
      default:
        return [];
    }
  };



  const isLoading = incomingLoading || outgoingLoading;
  const hasError = incomingError || outgoingError;

  // Show login prompt if user is not authenticated
  if (!isLoggedIn) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loginContainer}>
          <Text style={[styles.loginTitle, { color: theme.colors.onSurface }]}>
            You need to login
          </Text>
          <Text style={[styles.loginSubtitle, { color: theme.colors.onSurface }]}>
            Please sign in to view your bookings
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading bookings...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (hasError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <ScrollView 
          style={styles.content}
          contentContainerStyle={styles.errorScrollContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        >
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              Failed to load bookings. Please check your connection and try again.
            </Text>
            <Button 
              mode="contained" 
              onPress={onRefresh}
              style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
              loading={refreshing}
              disabled={refreshing}
            >
              {refreshing ? 'Retrying...' : 'Retry'}
            </Button>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  const filteredBookings = getFilteredBookings();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Header */}
        <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.title, { color: theme.colors.onSurface }]}>
            Bookings
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.onSurface }]}>
            Manage your event participation bookings
          </Text>
        </Surface>

        {/* Filter Tabs */}
        <Surface style={[styles.filterSection, { backgroundColor: theme.colors.surface }]}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
          <Chip 
              mode="outlined" 
              selected={filterType === 'all'} 
              style={styles.chip}
              onPress={() => setFilterType('all')}
            >
              All ({(incomingBookings.data?.length || 0) + (outgoingBookings.data?.length || 0)})
            </Chip>
            <Chip 
              mode="outlined" 
              selected={filterType === 'incoming'} 
              style={styles.chip}
              onPress={() => setFilterType('incoming')}
            >
              Incoming ({incomingBookings.data?.filter((item: any) => item?.event_booking?.booking_status === BookingStatus.PENDING_CONFIRMATION).length || 0})
            </Chip>
            <Chip 
              mode="outlined" 
              selected={filterType === 'sent'} 
              style={styles.chip}
              onPress={() => setFilterType('sent')}
            >
              Sent ({outgoingBookings.data?.length || 0})
            </Chip>
            <Chip 
              mode="outlined" 
              selected={filterType === 'approved'} 
              style={styles.chip}
              onPress={() => setFilterType('approved')}
            >
              Approved
            </Chip>
          </ScrollView>
        </Surface>

        {/* Bookings List */}
        <Surface style={[styles.requestsSection, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            {filterType === 'all' && `All Bookings (${filteredBookings.length})`}
            {filterType === 'incoming' && `Incoming Bookings (${filteredBookings.length})`}
            {filterType === 'sent' && `My Sent Bookings (${filteredBookings.length})`}
            {filterType === 'approved' && `Approved Bookings (${filteredBookings.length})`}
          </Text>
          
          {filteredBookings.length === 0 ? (
            <Surface style={[styles.emptySection, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.emptyText, { color: theme.colors.onSurface }]}>
                {filterType === 'all' && 'No bookings'}
                {filterType === 'incoming' && 'No incoming bookings'}
                {filterType === 'sent' && 'No sent bookings'}
                {filterType === 'approved' && 'No approved bookings'}
              </Text>
            </Surface>
          ) : (
            filteredBookings.map((augmentedBooking: any, index: number) => (
              <RequestCard
                key={`${augmentedBooking.event_booking?.id}-${index}`}
                augmentedBooking={augmentedBooking}
                filterType={filterType}
                onRespondToBooking={handleBookingResponse}
                onAcceptBooking={handleAcceptBooking}
                onRejectBooking={handleRejectBooking}
                onCancelBooking={handleCancelBooking}
                isResponding={
                  respondToBooking.isPending || 
                  acceptParticipation.isPending || 
                  rejectParticipation.isPending || 
                  cancelParticipation.isPending
                }
              />
            ))
          )}
        </Surface>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  filterSection: {
    padding: 16,
    marginBottom: 8,
  },
  chipsContainer: {
    flexDirection: 'row',
  },
  chip: {
    marginRight: 8,
  },
  requestsSection: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
  },

  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  loginSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorScrollContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    borderRadius: 8,
    marginTop: 8,
  },
  emptySection: {
    padding: 40,
    margin: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
}); 