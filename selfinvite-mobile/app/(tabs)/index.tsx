import React, { useState, useCallback, useMemo } from 'react';
import { 
  View, 
  StyleSheet, 
  FlatList, 
  ScrollView,
  RefreshControl, 
  ActivityIndicator,
  Alert,
  Share
} from 'react-native';
import { 
  Surface, 
  Text, 
  Searchbar, 
  Chip, 
  IconButton, 
  FAB,
  Badge,
  Button,
  Menu,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useSearchWithStore, useSearchSuggestions, useTrendingSearches } from '../../src/hooks/useSearch';
import { useAlgoliaSearchWithStore } from '../../src/hooks/useAlgoliaSearch';
import { useSearchStore, useSearchFilters, useSearchMode } from '../../src/stores/searchStore';
import { SearchEventCard, SearchFilterModal, EventMapView, useDistanceCalculation } from '../../src/components';
import type { Event } from '../../src/types/event';
import log from '@/common/logger';

export default function SearchScreen() {
  const theme = useTheme();
  const router = useRouter();
  
  // Search state
  const { 
    query, 
    setQuery, 
    addRecentQuery, 
    setFilterModalVisible, 
    setSearchMode, 
    searchMode,
    appliedFilters,
    getActiveFilterCount, 
    getFilterSummary 
  } = useSearchStore();
  
  // Local state
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [savedEvents, setSavedEvents] = useState<Set<string>>(new Set());
  const [currentSort, setCurrentSort] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'event_date',
    direction: 'asc'
  });
  
  // Distance calculation
  const { calculateDistance, formatDistance } = useDistanceCalculation();
  const userLocation = useSearchStore(state => state.userLocation);

  // Enhanced filters with location data
  const enhancedFilters = useMemo(() => {
    const baseFilters = { ...appliedFilters };
    
    // Add location-based filtering if user location is available
    if (userLocation) {
      baseFilters.coordinates = {
        lat: userLocation.latitude,
        lng: userLocation.longitude
      };
      // Set default radius if not already set
      if (!baseFilters.radius) {
        baseFilters.radius = 25; // 25km default radius
      }
    }
    
    return baseFilters;
  }, [appliedFilters, userLocation]);

  // Original search functionality
  const originalSearch = useSearchWithStore();
  
  // Algolia search functionality
  const algoliaSearch = useAlgoliaSearchWithStore(
    query,
    enhancedFilters,
    {
      enabled: true,
      useInfinite: true,
      sort: currentSort.field === 'distance' && userLocation 
        ? [{ field: 'distance', direction: currentSort.direction }]
        : [{ field: currentSort.field as any, direction: currentSort.direction }]
    }
  );

  // Use the appropriate search based on toggle
  const searchResults = algoliaSearch;
  
  // Extract search results
  const {
    events,
    totalCount,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    isError,
    error,
    fetchNextPage,
    refetch
  } = searchResults;
  
  // Handle performSearch for original search
  const performSearch = useCallback((searchQuery: string, filters: any) => {
    if ('performSearch' in originalSearch) {
      originalSearch.performSearch(searchQuery, filters);
    }
  }, [originalSearch]);

  // Search suggestions
  const { data: suggestions = [], isLoading: suggestionsLoading } = useSearchSuggestions(query);
  
  // Trending searches
  const { data: trending = [] } = useTrendingSearches();

  // Calculate distances for events
  const eventsWithDistance = useMemo(() => {
    if (!userLocation) return events;
    log.debug('userLocation', userLocation);
    log.debug('events', events);
    return events.map(event => {
      const distance = calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        event.latitude,
        event.longitude
      );
      return { ...event, distance };
    });
  }, [events, userLocation, calculateDistance]);

  // Handle search input
  const handleSearchChange = useCallback((text: string) => {
    setQuery(text);
    setShowSuggestions(text.length > 0);
  }, [setQuery]);

  const handleSearchSubmit = useCallback(() => {
    setShowSuggestions(false);
    log.info('Algolia search triggered with query:', query, 'and filters:', enhancedFilters);
  }, [query, appliedFilters, performSearch, enhancedFilters]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    performSearch(suggestion, appliedFilters);
    // For Algolia search, the search is automatically triggered when query changes
  }, [setQuery, performSearch, appliedFilters]);

  // Handle filter modal
  const handleFilterPress = useCallback(() => {
    setFilterModalVisible(true);
  }, [setFilterModalVisible]);

  const handleFilterModalClose = useCallback(() => {
    setFilterModalVisible(false);
  }, [setFilterModalVisible]);

  // Handle view mode toggle
  const handleViewModeToggle = useCallback(() => {
    setSearchMode(searchMode === 'list' ? 'map' : 'list');
  }, [searchMode, setSearchMode]);

  // Handle event actions
  const handleEventPress = useCallback((event: Event) => {
    router.push({
      pathname: '/event-details',
      params: { eventId: event.id }
    });
  }, [router]);

  const handleSaveEvent = useCallback((event: Event) => {
    setSavedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(event.id)) {
        newSet.delete(event.id);
      } else {
        newSet.add(event.id);
      }
      return newSet;
    });
  }, []);

  const handleShareEvent = useCallback(async (event: Event) => {
    try {
      await Share.share({
        message: `Check out this event: ${event.title} in ${event.city}`,
        url: `selfinvite://event/${event.id}`,
      });
    } catch (error) {
      console.error('Error sharing event:', error);
    }
  }, []);

  const handleHostPress = useCallback((hostId: string) => {
    router.push({
      pathname: '/profile',
      params: { userId: hostId }
    });
  }, [router]);

  // Handle infinite scroll
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Handle sorting
  // const handleSortChange = useCallback((field: string, direction: 'asc' | 'desc') => {
  //   setCurrentSort({ field, direction });
  //   setShowSortMenu(false);
    
  //   // Update sort options in store
  //   const { setSortOptions } = useSearchStore.getState();
  //   setSortOptions([{ field: field as any, direction }]);
    
  //   // Trigger new search with updated sort
  //   performSearch(query, activeFilters);
  // }, [query, activeFilters, performSearch]);

  // Sort options
  const sortOptions = [
    { field: 'event_date', direction: 'asc' as const, label: 'Date (Earliest)' },
    { field: 'event_date', direction: 'desc' as const, label: 'Date (Latest)' },
    { field: 'price_per_person', direction: 'asc' as const, label: 'Price (Low to High)' },
    { field: 'price_per_person', direction: 'desc' as const, label: 'Price (High to Low)' },
    { field: 'created_at', direction: 'desc' as const, label: 'Recently Added' },
  ];

  // Add distance sorting if user location is available
  if (userLocation) {
    sortOptions.push({ field: 'distance', direction: 'asc' as const, label: 'Distance (Nearest)' });
  }

  // Render event item
  const renderEventItem = useCallback(({ item }: { item: Event & { distance?: number } }) => (
    <SearchEventCard
      event={item}
      distance={item.distance || 0}
      onPress={handleEventPress}
      onSave={handleSaveEvent}
      onShare={handleShareEvent}
      onHostPress={handleHostPress}
      isSaved={savedEvents.has(item.id)}
      showDistance={!!userLocation}
    />
  ), [handleEventPress, handleSaveEvent, handleShareEvent, handleHostPress, savedEvents, userLocation]);

  // Render empty state
  const renderEmptyState = useCallback(() => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.emptyText, { color: theme.colors.onSurface }]}>
            Searching for events...
          </Text>
        </View>
      );
    }

    if (isError) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons 
            name="alert-circle" 
            size={64} 
            color={theme.colors.error} 
          />
          <Text style={[styles.emptyText, { color: theme.colors.error }]}>
            Failed to load events
          </Text>
          <Button mode="outlined" onPress={handleRefresh}>
            Try Again
          </Button>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <MaterialCommunityIcons 
          name="magnify" 
          size={64} 
          color={theme.colors.onSurface} 
        />
        <Text style={[styles.emptyText, { color: theme.colors.onSurface }]}>
          {query ? 'No events found for your search' : 'Start searching for amazing events!'}
        </Text>
        {!query && (
          <View style={styles.trendingContainer}>
            <Text style={[styles.trendingTitle, { color: theme.colors.onSurface }]}>
              Popular searches:
            </Text>
            <View style={styles.trendingChips}>
              {trending.slice(0, 5).map((trend, index) => (
                <Chip
                  key={index}
                  mode="outlined"
                  onPress={() => handleSuggestionSelect(trend)}
                  style={styles.trendingChip}
                >
                  {trend}
                </Chip>
              ))}
            </View>
          </View>
        )}
      </View>
    );
  }, [isLoading, isError, query, theme.colors, handleRefresh, trending]);

  // Render filter chips
  const renderFilterChips = useCallback(() => {
    const filterSummary = getFilterSummary();
    const activeFilterCount = getActiveFilterCount();

    if (activeFilterCount === 0) return null;

    return (
      <Surface style={[styles.filterChipsContainer, { backgroundColor: theme.colors.surface }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterChipsScroll}>
          {filterSummary.map((filter, index) => (
            <Chip
              key={index}
              mode="flat"
              compact
              style={[styles.filterChip, { backgroundColor: theme.colors.primary }]}
              textStyle={{ color: 'white' }}
            >
              {filter}
            </Chip>
          ))}
          <Chip
            mode="outlined"
            compact
            onPress={handleFilterPress}
            style={styles.filterChip}
            icon="plus"
          >
            Add Filter
          </Chip>
        </ScrollView>
      </Surface>
    );
  }, [getFilterSummary, getActiveFilterCount, theme.colors, handleFilterPress]);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerTop}>
          <View style={styles.headerLeft}>
            <Text style={[styles.title, { color: theme.colors.onSurface }]}>
              Discover Events
            </Text>
            <Text style={[styles.subtitle, { color: theme.colors.onSurface }]}>
              Find amazing food experiences near you
            </Text>
          </View>
          <View style={styles.headerRight}>
            
            {totalCount > 0 && (
              <>
                <Menu
                  visible={showSortMenu}
                  onDismiss={() => setShowSortMenu(false)}
                  anchor={
                    <IconButton
                      icon="sort"
                      onPress={() => setShowSortMenu(true)}
                      iconColor={theme.colors.primary}
                      size={20}
                    />
                  }
                >
                  {sortOptions.map((option, index) => {
                    const isSelected = currentSort.field === option.field && currentSort.direction === option.direction;
                    return (
                      <Menu.Item
                        key={index}
                        // onPress={() => handleSortChange(option.field, option.direction)}
                        title={option.label}
                        {...(isSelected && { leadingIcon: "check" })}
                      />
                    );
                  })}
                </Menu>
                <IconButton
                  icon={searchMode === 'list' ? 'map' : 'format-list-bulleted'}
                  onPress={handleViewModeToggle}
                  iconColor={theme.colors.primary}
                  size={20}
                />
              </>
            )}
          </View>
        </View>
        {totalCount > 0 && (
          <View style={styles.resultsInfo}>
            <Text style={[styles.resultsCount, { color: theme.colors.onSurface }]}>
              {totalCount} events found
            </Text>
          </View>
        )}
      </Surface>

      {/* Search Bar */}
      <Surface style={[styles.searchSection, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search events, locations, cuisines..."
            onChangeText={handleSearchChange}
            value={query}
            onSubmitEditing={handleSearchSubmit}
            onFocus={() => setShowSuggestions(query.length > 0)}
            style={styles.searchBar}
            right={() => (
              <IconButton
                icon="filter-variant"
                onPress={handleFilterPress}
                iconColor={theme.colors.primary}
              />
            )}
          />
          {getActiveFilterCount() > 0 && (
            <Badge style={[styles.filterBadge, { backgroundColor: theme.colors.primary }]}>
              {getActiveFilterCount()}
            </Badge>
          )}
        </View>

        {/* Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <Surface style={[styles.suggestionsContainer, { backgroundColor: theme.colors.surface }]} elevation={4}>
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                mode="text"
                onPress={() => handleSuggestionSelect(suggestion.text)}
                style={styles.suggestionItem}
                textColor={theme.colors.onSurface}
              >
                {suggestion.text}
              </Button>
            ))}
          </Surface>
        )}
      </Surface>


      {/* Filter Chips */}
      {renderFilterChips()}

      {/* Events List or Map View */}
      {searchMode === 'list' ? (
        <FlatList
          data={eventsWithDistance}
          renderItem={renderEventItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          // ListEmptyComponent={renderEmptyState}
          ListFooterComponent={
            isFetchingNextPage ? (
              <View style={styles.loadingFooter}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
              </View>
            ) : null
          }
        />
      ) : (
        <EventMapView
          events={eventsWithDistance}
          userLocation={userLocation}
          onEventPress={handleEventPress}
          onBackToList={handleViewModeToggle}
          style={styles.mapContainer}
        />
      )}

      {/* Filter Modal */}
      {/* <SearchFilterModal
        visible={useSearchStore(state => state.isFilterModalVisible)}
        onDismiss={handleFilterModalClose}
      /> */}

      {/* Map FAB */}
      {/* {searchMode === 'list' && (
        <FAB
          icon="map"
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          onPress={handleViewModeToggle}
        />
      )} */}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 4,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  resultsInfo: {
    marginTop: 4,
  },
  searchSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 4,
  },
  searchContainer: {
    position: 'relative',
  },
  searchBar: {
    elevation: 0,
  },
  filterBadge: {
    position: 'absolute',
    top: -8,
    right: 8,
    zIndex: 1,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    zIndex: 10,
    borderRadius: 8,
    maxHeight: 200,
  },
  suggestionItem: {
    justifyContent: 'flex-start',
  },
  filterChipsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 4,
  },
  filterChipsScroll: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '500',
  },
  listContainer: {
    paddingBottom: 80, // Space for FAB
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  trendingContainer: {
    alignItems: 'center',
  },
  trendingTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
  },
  trendingChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  trendingChip: {
    margin: 4,
  },
  loadingFooter: {
    padding: 20,
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  mapContainer: {
    flex: 1,
  },
}); 