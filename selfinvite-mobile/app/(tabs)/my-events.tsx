import React from 'react';
import { ScrollView, StyleSheet, ActivityIndicator, View, RefreshControl } from 'react-native';
import { Surface, Text, Chip } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { useRouter } from 'expo-router';
import EventCard from '../../components/EventCard';
import { useEventsByUser } from '../../src/hooks/useEvents';
import { getCurrentUserId, useEventsStore } from '../../src/stores';
import moment from 'moment';
import type { Event } from '../../src/types';
import log from '../../src/common/logger';

interface EventWithHostFlag extends Event {
  isHosted: boolean;
}

export default function EventsScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { filterType, setFilterType } = useEventsStore();
  
  // Check if user is logged in
  const currentUserId = getCurrentUserId();
  const isLoggedIn = !!currentUserId;
  
  // Fetch events using React Query only if user is logged in
  const { data: events = [], isLoading, error, refetch } = useEventsByUser();
  const [refreshing, setRefreshing] = React.useState(false);

  // Update Zustand store when data changes
  React.useEffect(() => {
    log.debug('Events:', events);
    if (events.length > 0) {
      useEventsStore.getState().setEvents(events);
    }
  }, [events]);

  // Handle pull-to-refresh
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      log.error('Error refreshing events:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const getFilteredEvents = (): EventWithHostFlag[] => {
    if (!isLoggedIn) return [];
    
    log.debug('Filter type:', filterType);
    log.debug('Events:', events);
    log.debug('Current user ID:', currentUserId);


    const allEvents: EventWithHostFlag[] = events.map(event => ({
      ...event,
      isHosted: event.host_user_id === currentUserId,
    }));

    switch (filterType) {
      case 'hosting':
        return allEvents.filter(event => event.isHosted && new Date(event.event_date) > new Date());
      case 'attending':
        return allEvents.filter(event => !event.isHosted && new Date(event.event_date) > new Date());
      case 'past':
        return allEvents.filter(event => new Date(event.event_date) < new Date());
      default:
        return allEvents;
    }
  };

  const formatEventDate = (date: string): string => {
    const eventDate = moment(date);
    const now = moment();
    
    if (eventDate.isSame(now, 'day')) {
      return 'Today, ' + eventDate.format('h:mm A');
    } else if (eventDate.isSame(now.add(1, 'day'), 'day')) {
      return 'Tomorrow, ' + eventDate.format('h:mm A');
    } else if (eventDate.isAfter(now)) {
      return eventDate.format('dddd, h:mm A');
    } else {
      return eventDate.fromNow();
    }
  };

  const getEventStatus = (event: EventWithHostFlag): string => {
    const isPast = new Date(event.event_date) < new Date();
    const isHosted = event.isHosted;
    
    if (isPast) {
      return '• Completed • 5⭐ rating';
    } else if (isHosted) {
      return `• Hosting • ${event.currentParticipants}/${event.max_participants} guests`;
    } else {
      return '• Attending • Confirmed';
    }
  };

  const getStatusColor = (event: EventWithHostFlag): string => {
    const isPast = new Date(event.event_date) < new Date();
    const isHosted = event.isHosted;
    
    if (isPast) {
      return '#666666';
    } else if (isHosted) {
      return theme.colors.primary;
    } else {
      return '#4A90E2';
    }
  };

  const handleEventPress = (event: EventWithHostFlag) => {
    router.push({
      pathname: '/event-details',
      params: { eventId: event.id }
    });
  };

  // Show login prompt if user is not authenticated
  if (!isLoggedIn) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loginContainer}>
          <Text style={[styles.loginTitle, { color: theme.colors.onSurface }]}>
            You need to login
          </Text>
          <Text style={[styles.loginSubtitle, { color: theme.colors.onSurface }]}>
            Please sign in to view your events
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading your events...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Failed to load events. Please try again.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const filteredEventsList = getFilteredEvents();
  const upcomingEvents = filteredEventsList.filter(event => new Date(event.event_date) > new Date());
  const pastEvents = filteredEventsList.filter(event => new Date(event.event_date) < new Date());

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Header */}
        <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.title, { color: theme.colors.onSurface }]}>
            My Events
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.onSurface }]}>
            Track your hosted and attended events
          </Text>
        </Surface>

        {/* Filter Tabs */}
        <Surface style={[styles.filterSection, { backgroundColor: theme.colors.surface }]}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipsContainer}>
            <Chip 
              mode="outlined" 
              selected={filterType === 'all'} 
              style={styles.chip}
              onPress={() => setFilterType('all')}
            >
              All Events
            </Chip>
            <Chip 
              mode="outlined" 
              selected={filterType === 'hosting'} 
              style={styles.chip}
              onPress={() => setFilterType('hosting')}
            >
              Hosting
            </Chip>
            <Chip 
              mode="outlined" 
              selected={filterType === 'attending'} 
              style={styles.chip}
              onPress={() => setFilterType('attending')}
            >
              Attending
            </Chip>
            <Chip 
              mode="outlined" 
              selected={filterType === 'past'} 
              style={styles.chip}
              onPress={() => setFilterType('past')}
            >
              Past Events
            </Chip>
          </ScrollView>
        </Surface>

        {/* Events List */}
        {upcomingEvents.length > 0 && (
          <Surface style={[styles.eventsSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Upcoming Events
            </Text>
            
            {upcomingEvents.map((event) => (
              <EventCard
                key={event.id}
                title={event.title}
                date={formatEventDate(event.event_date)}
                location={`📍 ${event.city}, ${event.street}`}
                status={getEventStatus(event)}
                statusColor={getStatusColor(event)}
                onPress={() => handleEventPress(event)}
              />
            ))}
          </Surface>
        )}

        {pastEvents.length > 0 && (
          <Surface style={[styles.eventsSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Recent Events
            </Text>
            
            {pastEvents.map((event) => (
              <EventCard
                key={event.id}
                title={event.title}
                date={formatEventDate(event.event_date)}
                location={`📍 ${event.city}, ${event.street}`}
                status={getEventStatus(event)}
                statusColor={getStatusColor(event)}
                onPress={() => handleEventPress(event)}
              />
            ))}
          </Surface>
        )}

        {filteredEventsList.length === 0 && (
          <Surface style={[styles.emptySection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.emptyText, { color: theme.colors.onSurface }]}>
              No events found for the selected filter.
            </Text>
          </Surface>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  filterSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 4,
  },
  chipsContainer: {
    flexDirection: 'row',
  },
  chip: {
    marginRight: 8,
  },
  eventsSection: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  loginSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  emptySection: {
    padding: 40,
    margin: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
}); 