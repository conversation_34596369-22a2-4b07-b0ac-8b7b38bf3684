import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, View, Image, TouchableOpacity } from 'react-native';
import { 
  Button, 
  TextInput, 
  Text, 
  useTheme, 
  HelperText, 
  Surface, 
  Avatar,
  IconButton,
  ActivityIndicator,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { userProfileApi } from '../../../src/api/userProfileApi';
import { useAuthStore } from '../../../src/stores/authStore';
import { showErrorDialog, showSuccessDialog } from '../../../src/stores';
import { CustomMultiSelect } from '../../../src/components/CustomMultiSelect';
import AddressSearchInput from '../../../src/components/Geocoding/AddressSearchInput';
import type { UserProfile, UserPreferences, UpdateProfileRequest, UpdatePreferencesRequest } from '../../../src/types';
import type { AddressSearchResult } from '../../../src/types/address';
import logger from '../../../src/common/logger';

// Dietary intolerance options
const intoleranceOptions = [
  { id: 'gluten', label: 'Gluten', value: 'gluten' },
  { id: 'dairy', label: 'Dairy', value: 'dairy' },
  { id: 'nuts', label: 'Nuts', value: 'nuts' },
  { id: 'shellfish', label: 'Shellfish', value: 'shellfish' },
  { id: 'eggs', label: 'Eggs', value: 'eggs' },
  { id: 'soy', label: 'Soy', value: 'soy' },
  { id: 'fish', label: 'Fish', value: 'fish' },
  { id: 'sesame', label: 'Sesame', value: 'sesame' },
  { id: 'sulfites', label: 'Sulfites', value: 'sulfites' },
  { id: 'mustard', label: 'Mustard', value: 'mustard' },
  { id: 'celery', label: 'Celery', value: 'celery' },
  { id: 'lupin', label: 'Lupin', value: 'lupin' },
];

// Dietary preference tags
const dietaryTags = [
  { id: 'vegetarian', label: 'Vegetarian', value: 'vegetarian' },
  { id: 'vegan', label: 'Vegan', value: 'vegan' },
  { id: 'pescatarian', label: 'Pescatarian', value: 'pescatarian' },
  { id: 'keto', label: 'Keto', value: 'keto' },
  { id: 'paleo', label: 'Paleo', value: 'paleo' },
  { id: 'halal', label: 'Halal', value: 'halal' },
  { id: 'kosher', label: 'Kosher', value: 'kosher' },
  { id: 'low-carb', label: 'Low Carb', value: 'low-carb' },
  { id: 'organic', label: 'Organic', value: 'organic' },
  { id: 'local', label: 'Local Produce', value: 'local' },
];

export default function EditProfileScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();

  // Form state
  const [profileData, setProfileData] = useState<Partial<UserProfile>>({});
  const [preferencesData, setPreferencesData] = useState<Partial<UserPreferences>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [avatarUri, setAvatarUri] = useState<string | null>(null);
  const [selectedIntolerances, setSelectedIntolerances] = useState<string[]>([]);
  const [selectedDietaryTags, setSelectedDietaryTags] = useState<string[]>([]);
  const [location, setLocation] = useState<{ city: string; country: string } | null>(null);

  // Load user profile on mount
  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    if (!isAuthenticated) {
      showErrorDialog('You must be logged in to edit your profile.', 'Authentication Required');
      router.back();
      return;
    }

    try {
      setLoading(true);
      
      // Load profile and preferences separately
      const [profile, preferences] = await Promise.all([
        userProfileApi.getMyProfile(),
        userProfileApi.getMyPreferences()
      ]);
      
      setProfileData(profile);
      setPreferencesData(preferences);
      
      // Set avatar if exists
      if (profile.avatar_url) {
        setAvatarUri(profile.avatar_url);
      }
      
      // Set location if exists (now flat structure)
      if (profile.city && profile.country) {
        setLocation({
          city: profile.city,
          country: profile.country
        });
      }
      
      // Load dietary data from preferences
      if (preferences.intolerances) {
        setSelectedIntolerances(preferences.intolerances);
      }
      
      if (preferences.preferences) {
        setSelectedDietaryTags(preferences.preferences);
      }
      
    } catch (error) {
      logger.error('Error loading profile:', error);
      showErrorDialog('Failed to load profile data.', 'Error');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UserProfile, value: any) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  const handleAvatarPress = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        showErrorDialog('Permission required to access photo library.', 'Permission Required');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setAvatarUri(result.assets[0].uri);
      }
    } catch (error) {
      logger.error('Error picking image:', error);
      showErrorDialog('Failed to select image.', 'Error');
    }
  };

  const handleLocationSelect = (result: AddressSearchResult) => {
    const city = result.context?.find(ctx => ctx.id.includes('place'))?.text || '';
    const country = result.context?.find(ctx => ctx.id.includes('country'))?.text || '';
    
    setLocation({ city, country });
    // Update profile data with flat structure
    handleInputChange('city', city);
    handleInputChange('country', country);
  };

  const handleSaveProfile = async () => {
    if (!isAuthenticated) {
      showErrorDialog('You must be logged in to save your profile.', 'Authentication Required');
      return;
    }

    try {
      setSaving(true);

      // Prepare profile update data
      const profileUpdateData: UpdateProfileRequest = {
        ...(profileData.username && { username: profileData.username }),
        ...(profileData.firstName && { firstName: profileData.firstName }),
        ...(profileData.lastName && { lastName: profileData.lastName }),
        ...(profileData.phone_number && { phone_number: profileData.phone_number }),
        ...(profileData.date_of_birth && { date_of_birth: profileData.date_of_birth }),
        ...(profileData.bio && { bio: profileData.bio }),
        ...(profileData.city && { city: profileData.city }),
        ...(profileData.country && { country: profileData.country }),
        ...(profileData.address && { address: profileData.address }),
        ...(profileData.postal_code && { postal_code: profileData.postal_code }),
      };

      // Handle avatar upload if changed
      if (avatarUri && avatarUri !== profileData.avatar_url) {
        profileUpdateData.avatar_url = avatarUri;
      }

      // Prepare preferences update data
      const preferencesUpdateData: UpdatePreferencesRequest = {
        user_id: user?.id || '',
        intolerances: selectedIntolerances,
        preferences: selectedDietaryTags,
      };

      // Update both profile and preferences
      const [updatedProfile, updatedPreferences] = await Promise.all([
        userProfileApi.updateMyProfile(profileUpdateData),
        userProfileApi.updateMyPreferences(preferencesUpdateData)
      ]);
      
      // Update local state with the response
      setProfileData(updatedProfile);
      setPreferencesData(updatedPreferences);

      showSuccessDialog('Profile updated successfully!', 'Success');
      router.back();
    } catch (error) {
      logger.error('Error saving profile:', error);
      showErrorDialog('Failed to save profile. Please try again.', 'Error');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading profile...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      <ScrollView style={styles.content} keyboardShouldPersistTaps="handled">

        {/* Avatar Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Profile Picture
          </Text>
          <View style={styles.avatarSection}>
            <TouchableOpacity onPress={handleAvatarPress} style={styles.avatarContainer}>
              {avatarUri ? (
                <Image source={{ uri: avatarUri }} style={styles.avatar} />
              ) : (
                <Avatar.Text 
                  size={100} 
                  label={profileData.firstName?.[0] || profileData.username?.[0] || 'U'} 
                  style={{ backgroundColor: theme.colors.primary }}
                />
              )}
              <View style={[styles.avatarOverlay, { backgroundColor: theme.colors.primary }]}>
                <IconButton
                  icon="camera"
                  size={20}
                  iconColor="#FFFFFF"
                />
              </View>
            </TouchableOpacity>
            <Text style={[styles.avatarHint, { color: theme.colors.onSurfaceVariant }]}>
              Tap to change profile picture
            </Text>
          </View>
        </Surface>

        {/* Basic Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Basic Information
          </Text>
          
          <TextInput
            label="First Name"
            value={profileData.firstName || ''}
            onChangeText={(text) => handleInputChange('firstName', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="Last Name"
            value={profileData.lastName || ''}
            onChangeText={(text) => handleInputChange('lastName', text)}
            style={styles.input}
            mode="outlined"
          />
          
          <TextInput
            label="Username"
            value={profileData.username || ''}
            onChangeText={(text) => handleInputChange('username', text)}
            style={styles.input}
            mode="outlined"
          />
          <HelperText type="info">
            This is how other users will see you
          </HelperText>
          
          <TextInput
            label="Phone Number"
            value={profileData.phone_number || ''}
            onChangeText={(text) => handleInputChange('phone_number', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="phone-pad"
          />
          
          <TextInput
            label="Date of Birth"
            value={profileData.date_of_birth || ''}
            onChangeText={(text) => handleInputChange('date_of_birth', text)}
            style={styles.input}
            mode="outlined"
            placeholder="YYYY-MM-DD"
          />
          <HelperText type="info">
            Format: YYYY-MM-DD
          </HelperText>
          
          <TextInput
            label="Address"
            value={profileData.address || ''}
            onChangeText={(text) => handleInputChange('address', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Street address"
          />
          
          <TextInput
            label="Postal Code"
            value={profileData.postal_code || ''}
            onChangeText={(text) => handleInputChange('postal_code', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Postal/ZIP code"
          />
        </Surface>

        {/* Location */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Location
          </Text>
          
          <AddressSearchInput
            onAddressSelect={handleLocationSelect}
            placeholder="Search for your city..."
            style={styles.input}
          />
          
          {location && (
            <View style={styles.locationDisplay}>
              <Text style={[styles.locationText, { color: theme.colors.onSurface }]}>
                📍 {location.city}, {location.country}
              </Text>
            </View>
          )}
        </Surface>

        {/* Bio */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            About Me
          </Text>
          
          <TextInput
            label="Bio"
            value={profileData.bio || ''}
            onChangeText={(text) => handleInputChange('bio', text)}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={4}
            placeholder="Tell others about yourself, your cooking style, or what you enjoy about food..."
          />
          <HelperText type="info">
            Share a bit about yourself and your culinary interests
          </HelperText>
        </Surface>

        {/* Dietary Intolerances */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Dietary Intolerances
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.onSurfaceVariant }]}>
            Select any foods you cannot eat due to allergies or intolerances
          </Text>
          
          <CustomMultiSelect
            items={intoleranceOptions}
            selectedItems={selectedIntolerances}
            onSelectedItemsChange={setSelectedIntolerances}
            selectText="Select intolerances"
            searchPlaceholder="Search intolerances..."
          />
        </Surface>

        {/* Dietary Preferences */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Dietary Preferences
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.onSurfaceVariant }]}>
            Select your dietary preferences and lifestyle choices
          </Text>
          
          <CustomMultiSelect
            items={dietaryTags}
            selectedItems={selectedDietaryTags}
            onSelectedItemsChange={setSelectedDietaryTags}
            selectText="Select dietary preferences"
            searchPlaceholder="Search preferences..."
          />
        </Surface>

        {/* Save Button */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Button
            mode="contained"
            onPress={handleSaveProfile}
            loading={saving}
            disabled={saving}
            style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
            labelStyle={{ color: '#FFFFFF' }}
          >
            {saving ? 'Saving...' : 'Save Profile'}
          </Button>
        </Surface>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    paddingVertical: 8,
    marginBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 48,
  },
  section: {
    marginBottom: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarHint: {
    fontSize: 14,
    textAlign: 'center',
  },
  input: {
    marginBottom: 16,
  },
  locationDisplay: {
    marginTop: 8,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  locationText: {
    fontSize: 14,
  },
  saveButton: {
    marginTop: 8,
    paddingVertical: 8,
  },
});
