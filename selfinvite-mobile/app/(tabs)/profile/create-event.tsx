import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, View, Share } from 'react-native';
import { Button, TextInput, Text, useTheme, HelperText, Portal, Dialog, Paragraph, List, Surface, Card, Divider } from 'react-native-paper';
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import * as Location from 'expo-location';
import { eventsApi } from '@/api/eventsApi';
import type { CreateEventRequest } from '@/types';
import { useRouter } from 'expo-router';
import { kitchenOptions } from '@/types/kitchenOptions';
import { beverageOptions } from '@/types/beverageOptions';
import { eventOptions } from '@/types/eventOptions';
import { locationOptions } from '@/types/locationOptions';
import { intoleranceOptions } from '@/types/intoleranceOptions';
import { CustomMultiSelect, GeocodingSelector } from '@/components';
import { showErrorDialog, showSuccessDialog, showConfirmationDialog } from '@/stores';
// TODO: look for a better radio button component
import RadioForm from 'react-native-simple-radio-button';
import { MediaPicker, type MediaItem } from '../../../src/components/MediaPicker';
import { useMediaUpload } from '../../../src/hooks/useMediaUpload';
import { useAuthStore } from '../../../src/stores/authStore';
import log from '@/common/logger';


export default function CreateEventScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();


  // State management aligned with original CreateEvent.js
  const [eventData, setEventData] = useState<Partial<CreateEventRequest>>(() => {
    const aWeekFromNow = new Date();
    aWeekFromNow.setDate(aWeekFromNow.getDate() + 7);

    const baseData: Partial<CreateEventRequest> = {
      title: __DEV__ ? `Test Dinner Party ${new Date().toISOString()}` : '',
      description: __DEV__ ? 'Join us for a delicious dinner and great conversation! This is a test event for development purposes.' : '',
      event_date: __DEV__ ? aWeekFromNow.toISOString() : '',
      duration_minutes: __DEV__ ? 120 : 120,
      price_per_person: __DEV__ ? 25 : 0,
      max_participants: __DEV__ ? 8 : 10,
      street: __DEV__ ? '123 Test Street' : '',
      city: __DEV__ ? 'Test City' : '',
      country: __DEV__ ? 'Test Country' : '',
      postal_code: __DEV__ ? '12345' : '',
      type_kitchens: __DEV__ ? ['__italian', '__mediterrean'] : [],
      type_beverages: __DEV__ ? ['__wine', '__beer'] : [],
      type_events: __DEV__ ? ['__dinner', '__brunch'] : [],
      type_locations: __DEV__ ? ['__restaurant', '__home'] : [],
      type_intolerances: __DEV__ ? ['__gluten', '__dairy'] : [],
    };

    if (__DEV__) {
      baseData.longitude = 13.4050;
      baseData.latitude = 52.5200;
    }

    return baseData;
  });

  // Additional state for address functionality
  const [address, setAddress] = useState<{ latitude: number; longitude: number; formattedAddress: string } | null>(null);
  const [showDate, setShowDate] = useState(false);
  const [loading, setLoading] = useState(false);
  const [addressSelected, setAddressSelected] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const { state, uploadMedia, clearUploads } = useMediaUpload();
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  const handleInputChange = (field: keyof CreateEventRequest, value: any) => {
    log.info(`handleInputChange: ${field} ${value}`);
    setEventData((prev: Partial<CreateEventRequest>) => ({ ...prev, [field]: value }));
  };

  const handleMediaSelected = (media: MediaItem[]) => {
    setSelectedMedia(media);
  };

  // Date picker functions
  const showDateHandler = () => setShowDate(true);
  const dateCancelHandler = () => setShowDate(false);
  const dateConfirmHandler = (date: Date) => {
    setEventData(prev => ({ ...prev, event_date: date.toISOString() }));
    setShowDate(false);
  };

  const handleCreateEvent = async () => {
    setLoading(true);
    try {
      // Check authentication first
      if (!isAuthenticated) {
        showErrorDialog('You must be logged in to create events with media.', 'Authentication Required');
        setLoading(false);
        return;
      }

      // Basic validation
      if (!eventData.title || !eventData.event_date || !eventData.street || !eventData.city) {
        showErrorDialog('Please fill all required fields.', 'Validation Error');
        setLoading(false);
        return;
      }

      // Create event first (without media)
      const eventDataWithoutMedia = { ...eventData };
      delete eventDataWithoutMedia.medias;
      
      const createdEvent = await eventsApi.createEvent(eventDataWithoutMedia as CreateEventRequest);
      
      // Upload media files if any are selected
      if (selectedMedia.length > 0) {
        try {
          const uris = selectedMedia.map(item => item.uri);
          const uploadResults = await uploadMedia(uris, createdEvent.id);
          
          // Convert upload results to MediaProperty format
          const mediaProperties = uploadResults.map(result => ({
            url: result.url,
            type: result.type as 'image' | 'video',
            caption: result.path.split('/').pop() || 'Media'
          }));

          // Update event with media URLs
          await eventsApi.updateEvent(createdEvent.id, {
            medias: mediaProperties
          });
        } catch (uploadError) {
          console.error('Media upload failed:', uploadError);
          showErrorDialog('Event created but media upload failed. You can add media later.', 'Partial Success');
        }
      }

      showSuccessDialog('Event created successfully!', 'Success');
      router.navigate('/(tabs)/my-events');
    } catch (e: any) {
      showErrorDialog(e.message || 'Failed to create event.', 'Error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.content}>
        {/* Header */}
        <Surface style={[styles.headerCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
            Create New Event
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.onSurfaceVariant }]}>
            Share your culinary experience with others
          </Text>
        </Surface>

        {/* Authentication Status */}
        {!isAuthenticated && (
          <Surface style={[styles.warningCard, { backgroundColor: theme.colors.errorContainer }]} elevation={1}>
            <View style={styles.warningContent}>
              <MaterialCommunityIcons 
                name="alert-circle" 
                size={24} 
                color={theme.colors.error} 
                style={styles.warningIcon}
              />
              <Text style={[styles.warningText, { color: theme.colors.onErrorContainer }]}>
                You must be logged in to upload media files
              </Text>
            </View>
          </Surface>
        )}

        {/* Basic Information Card */}
        <Card style={styles.card} elevation={2}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="information" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.cardIcon}
              />
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Basic Information
              </Text>
            </View>
            <Divider style={styles.cardDivider} />

            <TextInput
              label="Event Title *"
              value={eventData.title || ''}
              onChangeText={(text) => handleInputChange('title', text)}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="format-title" />}
            />

            {/* Collapsible Description Field */}
            <View style={styles.collapsibleContainer}>
              <View style={styles.collapsibleHeader}>
                <Text style={[styles.collapsibleLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Description {eventData.description && `(${eventData.description.length} chars)`}
                </Text>
                <Button
                  mode="text"
                  compact
                  onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                  style={styles.expandButton}
                  contentStyle={styles.expandButtonContent}
                >
                  <MaterialCommunityIcons 
                    name={isDescriptionExpanded ? "chevron-up" : "chevron-down"} 
                    size={20} 
                    color={theme.colors.primary} 
                  />
                </Button>
              </View>
              
              {isDescriptionExpanded && (
                <TextInput
                  label="Describe your event in detail..."
                  value={eventData.description || ''}
                  onChangeText={(text) => handleInputChange('description', text)}
                  mode="outlined"
                  multiline
                  numberOfLines={16}
                  style={[styles.input, styles.expandedInput]}
                  left={<TextInput.Icon icon="text" />}
                  placeholder="Tell guests what makes your event special. Include details about the menu, atmosphere, dietary accommodations, or any special requirements..."
                />
              )}
              
              {!isDescriptionExpanded && eventData.description && (
                <Surface style={[styles.descriptionPreview, { backgroundColor: '#F5F5F5' }]} elevation={0}>
                  <Text 
                    style={[styles.previewText, { color: theme.colors.onSurfaceVariant }]}
                    numberOfLines={2}
                  >
                    {eventData.description}
                  </Text>
                </Surface>
              )}
            </View>
          </Card.Content>
        </Card>
        
        {/* Date & Details Card */}
        <Card style={styles.card} elevation={2}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="calendar-clock" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.cardIcon}
              />
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Date & Details
              </Text>
            </View>
            <Divider style={styles.cardDivider} />

            <TextInput
              label="Event Date *"
              value={eventData.event_date ? new Date(eventData.event_date).toLocaleString() : ''}
              onPressIn={showDateHandler}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="calendar" />}
              right={<TextInput.Icon icon="chevron-down" onPress={showDateHandler} />}
              editable={false}
            />

            <View style={styles.inputRow}>
              <View style={styles.halfWidth}>
                <Text style={[styles.numericLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Duration (minutes)
                </Text>
                <Surface style={[styles.numericInputSurface, { backgroundColor: '#FAFAFA' }]} elevation={0}>
                  <MaterialCommunityIcons 
                    name="clock-outline" 
                    size={18} 
                    color={theme.colors.onSurfaceVariant}
                    style={styles.numericIcon}
                  />
                  <TextInput
                    value={String(eventData.duration_minutes) || ''}
                    onChangeText={(text) => handleInputChange('duration_minutes', parseInt(text, 10) || 30)}
                    keyboardType="numeric"
                    style={[styles.numericTextInput, { color: theme.colors.onSurface }]}
                    placeholder="30"
                  />
                  <View style={styles.numericControls}>
                    <Button
                      mode="text"
                      compact
                      onPress={() => handleInputChange('duration_minutes', Math.max(30, (eventData.duration_minutes || 30) + 15))}
                      style={styles.numericButton}
                      contentStyle={styles.numericButtonContent}
                    >
                      <MaterialCommunityIcons name="plus" size={16} color={theme.colors.primary} />
                    </Button>
                    <View style={styles.numericDivider} />
                    <Button
                      mode="text"
                      compact
                      onPress={() => handleInputChange('duration_minutes', Math.max(30, (eventData.duration_minutes || 30) - 15))}
                      style={styles.numericButton}
                      contentStyle={styles.numericButtonContent}
                    >
                      <MaterialCommunityIcons name="minus" size={16} color={theme.colors.primary} />
                    </Button>
                  </View>
                </Surface>
              </View>

              <View style={styles.halfWidth}>
                <Text style={[styles.numericLabel, { color: theme.colors.onSurfaceVariant }]}>
                  Price per Person (€)
                </Text>
                <Surface style={[styles.numericInputSurface, { backgroundColor: '#FAFAFA' }]} elevation={0}>
                  <MaterialCommunityIcons 
                    name="currency-eur" 
                    size={18} 
                    color={theme.colors.onSurfaceVariant}
                    style={styles.numericIcon}
                  />
                  <TextInput
                    value={String(eventData.price_per_person) || ''}
                    onChangeText={(text) => handleInputChange('price_per_person', parseInt(text, 10) || 5)}
                    keyboardType="numeric"
                    style={[styles.numericTextInput, { color: theme.colors.onSurface }]}
                    placeholder="10"
                  />
                  <View style={styles.numericControls}>
                    <Button
                      mode="text"
                      compact
                      onPress={() => handleInputChange('price_per_person', Math.max(10, (eventData.price_per_person || 10) + 5))}
                      style={styles.numericButton}
                      contentStyle={styles.numericButtonContent}
                    >
                      <MaterialCommunityIcons name="plus" size={16} color={theme.colors.primary} />
                    </Button>
                    <View style={styles.numericDivider} />
                    <Button
                      mode="text"
                      compact
                      onPress={() => handleInputChange('price_per_person', Math.max(10, (eventData.price_per_person || 10) - 5))}
                      style={styles.numericButton}
                      contentStyle={styles.numericButtonContent}
                    >
                      <MaterialCommunityIcons name="minus" size={16} color={theme.colors.primary} />
                    </Button>
                  </View>
                </Surface>
              </View>
            </View>

            <View>
              <Text style={[styles.numericLabel, { color: theme.colors.onSurfaceVariant }]}>
                Max Participants *
              </Text>
              <Surface style={[styles.numericInputSurface, { backgroundColor: '#FAFAFA' }]} elevation={0}>
                <MaterialCommunityIcons 
                  name="account-group" 
                  size={20} 
                  color={theme.colors.onSurfaceVariant}
                  style={styles.numericIcon}
                />
                <TextInput
                  value={String(eventData.max_participants) || ''}
                  onChangeText={(text) => handleInputChange('max_participants', parseInt(text, 10) || 0)}
                  keyboardType="numeric"
                  style={[styles.numericTextInput, { color: theme.colors.onSurface }]}
                  placeholder="1"
                />
                <View style={styles.numericControls}>
                  <Button
                    mode="text"
                    compact
                    onPress={() => handleInputChange('max_participants', Math.max(1, (eventData.max_participants || 0) + 1))}
                    style={styles.numericButton}
                    contentStyle={styles.numericButtonContent}
                  >
                    <MaterialCommunityIcons name="plus" size={16} color={theme.colors.primary} />
                  </Button>
                  <View style={styles.numericDivider} />
                  <Button
                    mode="text"
                    compact
                    onPress={() => handleInputChange('max_participants', Math.max(1, (eventData.max_participants || 0) - 1))}
                    style={styles.numericButton}
                    contentStyle={styles.numericButtonContent}
                  >
                    <MaterialCommunityIcons name="minus" size={16} color={theme.colors.primary} />
                  </Button>
                </View>
              </Surface>
            </View>
          </Card.Content>
        </Card>

        {/* Location Card */}
        <Card style={styles.card} elevation={2}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="map-marker" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.cardIcon}
              />
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Event Location
              </Text>
            </View>
            <Divider style={styles.cardDivider} />

            <View style={styles.geocodingContainer}>
              <GeocodingSelector
                onLocationSelect={(address) => {
                  setAddress(address);
                  setAddressSelected(true);
                  log.info('address:', address);
                  // Update event data with selected address
                  handleInputChange('street', address.street || '');
                  handleInputChange('city', address.city || '');
                  handleInputChange('country', address.country || '');
                  handleInputChange('postal_code', address.postalCode || '');
                  handleInputChange('latitude', address.latitude);
                  handleInputChange('longitude', address.longitude);
                }}
                mode="search"
                placeholder="Search for the event location..."
                showMapCard={true}
                style={styles.geocodingSelector}
              />
            </View>

            {addressSelected && (
              <View style={styles.addressFields}>
                <TextInput
                  label="Street *"
                  value={eventData.street || ''}
                  onChangeText={(text) => handleInputChange('street', text)}
                  mode="outlined"
                  style={styles.input}
                  left={<TextInput.Icon icon="road" />}
                />

                <View style={styles.inputRow}>
                  <TextInput
                    label="City *"
                    value={eventData.city || ''}
                    onChangeText={(text) => handleInputChange('city', text)}
                    mode="outlined"
                    style={[styles.input, styles.halfWidth]}
                    left={<TextInput.Icon icon="city" />}
                  />

                  <TextInput
                    label="Postal Code"
                    value={eventData.postal_code || ''}
                    onChangeText={(text) => handleInputChange('postal_code', text)}
                    mode="outlined"
                    style={[styles.input, styles.halfWidth]}
                    left={<TextInput.Icon icon="mailbox" />}
                  />
                </View>

                <TextInput
                  label="Country"
                  value={eventData.country || ''}
                  onChangeText={(text) => handleInputChange('country', text)}
                  mode="outlined"
                  style={styles.input}
                  left={<TextInput.Icon icon="flag" />}
                />
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Categories Card */}
        <Card style={styles.card} elevation={2}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="tag-multiple" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.cardIcon}
              />
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Event Categories
              </Text>
            </View>
            <Divider style={styles.cardDivider} />

            <View style={styles.multiSelectContainer}>
              <Text style={styles.multiSelectLabel}>
                <MaterialCommunityIcons name="chef-hat" size={16} color={theme.colors.primary} /> Kitchen Types
              </Text>
              <CustomMultiSelect
                items={kitchenOptions}
                selectedItems={eventData.type_kitchens || []}
                onSelectedItemsChange={(selectedItems) => handleInputChange('type_kitchens', selectedItems)}
                selectText="Select Kitchen Types"
                searchPlaceholder="Search kitchen types..."
              />
            </View>

            <View style={styles.multiSelectContainer}>
              <Text style={styles.multiSelectLabel}>
                <MaterialCommunityIcons name="glass-wine" size={16} color={theme.colors.primary} /> Beverage Types
              </Text>
              <CustomMultiSelect
                items={beverageOptions}
                selectedItems={eventData.type_beverages || []}
                onSelectedItemsChange={(selectedItems) => handleInputChange('type_beverages', selectedItems)}
                selectText="Select Beverage Types"
                searchPlaceholder="Search beverage types..."
              />
            </View>

            <View style={styles.multiSelectContainer}>
              <Text style={styles.multiSelectLabel}>
                <MaterialCommunityIcons name="calendar-star" size={16} color={theme.colors.primary} /> Event Types
              </Text>
              <CustomMultiSelect
                items={eventOptions}
                selectedItems={eventData.type_events || []}
                onSelectedItemsChange={(selectedItems) => handleInputChange('type_events', selectedItems)}
                selectText="Select Event Types"
                searchPlaceholder="Search event types..."
              />
            </View>

            <View style={styles.multiSelectContainer}>
              <Text style={styles.multiSelectLabel}>
                <MaterialCommunityIcons name="home-variant" size={16} color={theme.colors.primary} /> Location Types
              </Text>
              <CustomMultiSelect
                items={locationOptions}
                selectedItems={eventData.type_locations || []}
                onSelectedItemsChange={(selectedItems) => handleInputChange('type_locations', selectedItems)}
                selectText="Select Location Types"
                searchPlaceholder="Search location types..."
              />
            </View>

            <View style={styles.multiSelectContainer}>
              <Text style={styles.multiSelectLabel}>
                <MaterialCommunityIcons name="alert-circle" size={16} color={theme.colors.primary} /> Dietary Restrictions
              </Text>
              <CustomMultiSelect
                items={intoleranceOptions}
                selectedItems={eventData.type_intolerances || []}
                onSelectedItemsChange={(selectedItems) => handleInputChange('type_intolerances', selectedItems)}
                selectText="Select Dietary Restrictions"
                searchPlaceholder="Search dietary restrictions..."
              />
            </View>
          </Card.Content>
        </Card>

        {/* Media Card */}
        <Card style={styles.card} elevation={2}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons 
                name="camera" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.cardIcon}
              />
              <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
                Event Media
              </Text>
            </View>
            <Divider style={styles.cardDivider} />

            <MediaPicker
              onMediaSelected={handleMediaSelected}
              maxFiles={5}
              allowedTypes={['image', 'video']}
              placeholder="Add photos or videos to showcase your event"
              disabled={!isAuthenticated}
            />

            {/* Media Upload Status */}
            {selectedMedia.length > 0 && (
              <Surface style={[styles.mediaStatusContainer, { backgroundColor: theme.colors.primaryContainer }]} elevation={1}>
                <View style={styles.mediaStatusContent}>
                  <MaterialCommunityIcons 
                    name="file-multiple" 
                    size={20} 
                    color={theme.colors.onPrimaryContainer} 
                  />
                  <Text style={[styles.mediaStatusTitle, { color: theme.colors.onPrimaryContainer }]}>
                    {selectedMedia.length} media file{selectedMedia.length > 1 ? 's' : ''} selected
                  </Text>
                </View>
                {state.isUploading && (
                  <Text style={[styles.mediaStatusText, { color: theme.colors.onPrimaryContainer }]}>
                    Media will be uploaded after event creation
                  </Text>
                )}
              </Surface>
            )}
          </Card.Content>
        </Card>

        {/* Action Card */}
        <Card style={styles.card} elevation={2}>
          <Card.Content>
            <Button
              mode="contained"
              onPress={handleCreateEvent}
              loading={loading}
              disabled={loading}
              style={styles.createButton}
              contentStyle={styles.createButtonContent}
              labelStyle={styles.createButtonLabel}
              icon="plus-circle"
            >
              {loading ? 'Creating Event...' : 'Create Event'}
            </Button>
          </Card.Content>
        </Card>

        {/* Date Picker Modal */}
        <DateTimePickerModal
          mode="datetime"
          date={new Date()}
          minuteInterval={15}
          isVisible={showDate}
          minimumDate={new Date()}
          onConfirm={dateConfirmHandler}
          onCancel={dateCancelHandler}
        />

      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  
  // Header Card
  headerCard: {
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
  },

  // Warning Card
  warningCard: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  warningContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  warningIcon: {
    marginRight: 12,
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },

  // Card Styles
  card: {
    marginBottom: 20,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardIcon: {
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  cardDivider: {
    marginBottom: 20,
  },

  // Input Styles
  input: {
    marginBottom: 16,
    backgroundColor: '#FAFAFA',
  },
  
  // Collapsible Description Styles
  collapsibleContainer: {
    marginBottom: 16,
  },
  collapsibleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  collapsibleLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  expandButton: {
    minWidth: 40,
    height: 40,
  },
  expandButtonContent: {
    height: 40,
    width: 40,
  },
  expandedInput: {
    marginTop: 8,
  },
  descriptionPreview: {
    marginTop: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  previewText: {
    fontSize: 14,
    lineHeight: 20,
    fontStyle: 'italic',
  },
  inputRow: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },

  // Numeric Input Controls
  numericLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
    marginLeft: 4,
  },
  numericInputSurface: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 16,
    minHeight: 56,
  },
  numericIcon: {
    marginRight: 12,
  },
  numericTextInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
    backgroundColor: 'transparent',
  },
  numericControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  numericButton: {
    minWidth: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'transparent',
  },
  numericButtonContent: {
    height: 36,
    width: 36,
  },
  numericDivider: {
    width: 1,
    height: 20,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 4,
  },

  // Geocoding
  geocodingContainer: {
    marginBottom: 16,
  },
  geocodingSelector: {
    overflow: 'visible',
  },
  addressFields: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },

  // Multi-select
  multiSelectContainer: {
    marginBottom: 20,
  },
  multiSelectLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
    flexDirection: 'row',
    alignItems: 'center',
  },

  // Media Status
  mediaStatusContainer: {
    marginTop: 16,
    padding: 16,
    borderRadius: 12,
  },
  mediaStatusContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  mediaStatusTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  mediaStatusText: {
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'center',
  },

  // Create Button
  createButton: {
    marginTop: 8,
    borderRadius: 12,
  },
  createButtonContent: {
    paddingVertical: 8,
  },
  createButtonLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
}); 