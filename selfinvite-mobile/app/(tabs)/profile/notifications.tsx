import React, { useState } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Modal,
} from 'react-native';
import {
  Surface,
  Text,
  Button,
  List,
  Divider,
  Switch,
  Card,
  Title,
  Paragraph,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { useNotifications } from '../../../src/hooks/useNotifications';
import { NotificationSettings } from '../../../src/components/NotificationSettings';
import { NotificationTestButton } from '../../../src/components/NotificationTestButton';
import { useRouter } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { notificationsApi } from '../../../src/api/notificationsApi';
import logger from '../../../src/common/logger';
import { useDialogStore } from '../../../src/stores/dialogStore';

export default function NotificationManagementScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { showDialog } = useDialogStore();
  const {
    isInitialized,
    isEnabled,
    preferences,
    requestPermissions,
    clearBadgeCount,
    updatePreferences,
  } = useNotifications();

  const [showSettings, setShowSettings] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Get notification history
  const { data: notificationHistory, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['notificationHistory'],
    queryFn: () => notificationsApi.getNotificationHistory(1, 20),
    enabled: isInitialized,
  });

  const handleRequestPermissions = async () => {
    try {
      const granted = await requestPermissions();
      if (!granted) {
        showDialog({
          type: 'warning',
          title: 'Permissions Required',
          message: 'Please enable notifications in your device settings to receive important updates about your events and messages.',
          buttons: [
            {
              text: 'Cancel',
              mode: 'outlined',
              style: 'secondary',
            },
            {
              text: 'Open Settings',
              mode: 'contained',
              style: 'primary',
              onPress: () => {
                logger.info('User requested to open device settings');
              },
            },
          ],
          dismissible: true,
        });
      }
    } catch (error) {
      logger.error('Failed to request notification permissions:', error);
      showDialog({
        type: 'error',
        title: 'Error',
        message: 'Failed to request notification permissions',
        buttons: [
          {
            text: 'OK',
            mode: 'contained',
            style: 'primary',
          },
        ],
        dismissible: true,
      });
    }
  };

  const handleClearAllNotifications = async () => {
    try {
      await notificationsApi.clearAllNotifications();
      await clearBadgeCount();
      showDialog({
        type: 'success',
        title: 'Success',
        message: 'All notifications have been cleared',
        buttons: [
          {
            text: 'OK',
            mode: 'contained',
            style: 'primary',
          },
        ],
        dismissible: true,
      });
    } catch (error) {
      logger.error('Failed to clear notifications:', error);
      showDialog({
        type: 'error',
        title: 'Error',
        message: 'Failed to clear notifications',
        buttons: [
          {
            text: 'OK',
            mode: 'contained',
            style: 'primary',
          },
        ],
        dismissible: true,
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!notificationHistory?.data) return;
    
    try {
      const unreadIds = notificationHistory.data
        .filter(notification => !notification.readAt)
        .map(notification => notification.id);
      
      if (unreadIds.length > 0) {
        await notificationsApi.markMultipleAsRead(unreadIds);
        showDialog({
          type: 'success',
          title: 'Success',
          message: 'All notifications marked as read',
          buttons: [
            {
              text: 'OK',
              mode: 'contained',
              style: 'primary',
            },
          ],
          dismissible: true,
        });
      }
    } catch (error) {
      logger.error('Failed to mark notifications as read:', error);
      showDialog({
        type: 'error',
        title: 'Error',
        message: 'Failed to mark notifications as read',
        buttons: [
          {
            text: 'OK',
            mode: 'contained',
            style: 'primary',
          },
        ],
        dismissible: true,
      });
    }
  };

  const formatNotificationTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message':
        return 'message';
      case 'booking':
        return 'calendar-check';
      case 'payment':
        return 'credit-card';
      case 'event':
        return 'calendar';
      default:
        return 'bell';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'message':
        return '#2196F3';
      case 'booking':
        return '#4CAF50';
      case 'payment':
        return '#FF9800';
      case 'event':
        return '#9C27B0';
      default:
        return theme.colors.primary;
    }
  };

  if (!isInitialized) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Initializing notifications...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]} edges={['top']}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.headerContent}>
            <Text style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
              Notifications
            </Text>
          </View>
        </Surface>

        {/* Permission Status */}
        {!isEnabled && (
          <Card style={[styles.permissionCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <Title>Enable Notifications</Title>
              <Paragraph>
                Get notified about new messages, booking updates, and event reminders.
              </Paragraph>
              <Button
                mode="contained"
                onPress={handleRequestPermissions}
                style={styles.enableButton}
              >
                Enable Notifications
              </Button>
            </Card.Content>
          </Card>
        )}

        {/* Quick Actions */}
        {isEnabled && (
          <Surface style={[styles.quickActions, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                onPress={handleClearAllNotifications}
                style={styles.actionButton}
              >
                Clear All
              </Button>
            </View>
          </Surface>
        )}

        {/* Notification Settings */}
        <Surface style={[styles.settingsSection, { backgroundColor: theme.colors.surface }]}>
          <List.Item
            title="Notification Settings"
            description="Manage your notification preferences"
            left={(props) => <List.Icon {...props} icon="cog" color={theme.colors.primary} />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => setShowSettings(true)}
          />
        </Surface>

        {/* Test Notifications (Development Only) */}
        {__DEV__ && (
          <NotificationTestButton style={[styles.testSection, { backgroundColor: theme.colors.surface }]} />
        )}

        {/* Notification History */}
        {isEnabled && (
          <Surface style={[styles.historySection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Recent Notifications
            </Text>
            
            {isLoadingHistory ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
                <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
                  Loading notifications...
                </Text>
              </View>
            ) : notificationHistory?.data && notificationHistory.data.length > 0 ? (
              notificationHistory.data.map((notification, index) => (
                <View key={notification.id}>
                  <List.Item
                    title={notification.title}
                    description={notification.body}
                    left={(props) => (
                      <List.Icon
                        {...props}
                        icon={getNotificationIcon(notification.data.type)}
                        color={getNotificationColor(notification.data.type)}
                      />
                    )}
                    right={() => (
                      <View style={styles.notificationRight}>
                        <Text style={[styles.notificationTime, { color: theme.colors.onSurface }]}>
                          {formatNotificationTime(notification.sentAt)}
                        </Text>
                        {!notification.readAt && (
                          <View style={[styles.unreadDot, { backgroundColor: theme.colors.primary }]} />
                        )}
                      </View>
                    )}
                    onPress={() => {
                      // Handle notification tap - could navigate to relevant screen
                      logger.info('Notification tapped:', notification.id);
                    }}
                  />
                  {index < notificationHistory.data.length - 1 && <Divider />}
                </View>
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={[styles.emptyText, { color: theme.colors.onSurface }]}>
                  No notifications yet
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.colors.onSurface }]}>
                  You'll see your notifications here when they arrive
                </Text>
              </View>
            )}
          </Surface>
        )}

        {/* Notification Settings Modal */}
        <Modal
          visible={showSettings}
          animationType="slide"
          presentationStyle="pageSheet"
        >
          <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
                Notification Settings
              </Text>
              <Button onPress={() => setShowSettings(false)}>
                Done
              </Button>
            </View>
            <ScrollView style={styles.modalContent}>
              <NotificationSettings onClose={() => setShowSettings(false)} />
            </ScrollView>
          </SafeAreaView>
        </Modal>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    marginBottom: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  badge: {
    minWidth: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  permissionCard: {
    margin: 16,
    marginBottom: 8,
  },
  enableButton: {
    marginTop: 16,
  },
  quickActions: {
    padding: 16,
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  settingsSection: {
    marginBottom: 8,
  },
  testSection: {
    margin: 16,
    marginBottom: 8,
  },
  historySection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    padding: 16,
    paddingBottom: 8,
  },
  notificationRight: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  notificationTime: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 4,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
  },
});
