import React from 'react';
import { View, StyleSheet, FlatList, KeyboardAvoidingView, Platform } from 'react-native';
import { Surface, Text, TextInput, IconButton, Avatar, Appbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useConversation, useMessages, useSendMessage } from '../../src/hooks/useMessages';
import type { Message } from '../../src/types';
import log from '@/common/logger';
import { useEffect } from 'react';
import supabase from '../../lib/supabase';

export default function ChatScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { threadId } = useLocalSearchParams<{ threadId: string }>();
  const [messageText, setMessageText] = React.useState('');

  // Fetch conversation and messages data
  const { data: conversation, isLoading: conversationLoading } = useConversation(threadId || '');
  const { data: messagesData, fetchNextPage, hasNextPage, isFetchingNextPage } = useMessages(threadId || '');
  const sendMessage = useSendMessage();

  // Real-time updates for messages in this conversation
  useEffect(() => {
    if (!threadId) return;

    const channel = supabase
      .channel(`messages-${threadId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${threadId}`
        },
        (payload) => {
          log.info('New message in conversation:', payload);
          // Invalidate messages query to refetch
          // This will be handled by React Query's automatic refetching
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [threadId]);

  // Get all messages from all pages
  const allMessages = React.useMemo(() => {
    if (!messagesData?.pages) return [];
    return messagesData.pages.flatMap(page => page.data || []);
  }, [messagesData]);

  // Handle sending message
  const handleSendMessage = async () => {
    if (!messageText.trim() || !threadId) return;

    try {
      // TODO: Get actual receiver_id and event_booking_id from conversation context
      await sendMessage.mutateAsync({
        conversationId: threadId,
        message: { 
          body: messageText.trim(),
          event_booking_id: 'temp-event-booking-id', // TODO: Get from conversation
          receiver_id: 'temp-receiver-id' // TODO: Get from conversation
        }
      });
      setMessageText('');
    } catch (error) {
      log.error('Failed to send message:', error);
    }
  };

  // Handle load more messages
  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // Render message item
  const renderMessage = ({ item }: { item: Message }) => {
    const isOwnMessage = item.sender_id === 'current-user-id'; // TODO: Get actual current user ID
    
    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessage : styles.otherMessage
      ]}>
        {!isOwnMessage && (
          <Avatar.Text 
            size={32} 
            label={item.sender?.nickname.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'} 
            style={{ backgroundColor: theme.colors.primary, marginRight: 8 }} 
          />
        )}
        <Surface style={[
          styles.messageBubble,
          { backgroundColor: isOwnMessage ? theme.colors.primary : theme.colors.surface },
          isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble
        ]}>
          <Text style={[
            styles.messageText,
            { color: isOwnMessage ? theme.colors.onPrimary : theme.colors.onSurface }
          ]}>
            {item.body}
          </Text>
          <Text style={[
            styles.messageTime,
            { color: isOwnMessage ? theme.colors.onPrimary : theme.colors.onSurface }
          ]}>
            {new Date(item.sent_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </Surface>
      </View>
    );
  };

  if (conversationLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading conversation...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!conversation) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Conversation not found
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Get other participant name for header
  const otherParticipant = conversation.participants.find(p => p.id !== 'current-user-id') || conversation.participants[0];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Appbar.Header style={{ backgroundColor: theme.colors.surface }}>
        <Appbar.BackAction onPress={() => router.back()} />
        <Appbar.Content 
          title={otherParticipant?.nickname || 'Chat'} 
          subtitle="Conversation"
        />
      </Appbar.Header>

      {/* Messages List */}
      <FlatList
        data={allMessages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        inverted
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={
          isFetchingNextPage ? (
            <View style={styles.loadingMore}>
              <Text style={[styles.loadingMoreText, { color: theme.colors.onSurface }]}>
                Loading more messages...
              </Text>
            </View>
          ) : null
        }
      />

      {/* Message Input */}
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.inputContainer}
      >
        <Surface style={[styles.inputSurface, { backgroundColor: theme.colors.surface }]}>
          <TextInput
            value={messageText}
            onChangeText={setMessageText}
            placeholder="Type a message..."
            mode="outlined"
            style={styles.textInput}
            multiline
            maxLength={1000}
            onSubmitEditing={handleSendMessage}
          />
          <IconButton
            icon="send"
            mode="contained"
            onPress={handleSendMessage}
            disabled={!messageText.trim() || sendMessage.isPending}
            loading={sendMessage.isPending}
            style={styles.sendButton}
          />
        </Surface>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 8,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-end',
  },
  ownMessage: {
    justifyContent: 'flex-end',
  },
  otherMessage: {
    justifyContent: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  ownMessageBubble: {
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  loadingMore: {
    padding: 16,
    alignItems: 'center',
  },
  loadingMoreText: {
    fontSize: 14,
    opacity: 0.7,
  },
  inputContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  inputSurface: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: 24,
    elevation: 2,
  },
  textInput: {
    flex: 1,
    marginRight: 8,
    maxHeight: 100,
  },
  sendButton: {
    margin: 4,
  },
});
