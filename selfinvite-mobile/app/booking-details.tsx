import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Modal } from 'react-native';
import { Text, Surface, Button, Divider, Chip } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'react-native-paper';
import { useLocalSearchParams, router } from 'expo-router';
import { useIncomingEventBookings, useOutgoingEventBookings } from '../src/hooks/useEventBookings';
import moment from 'moment';
import log from '@/common/logger';
import { getStatusDisplay, isPaymentRequired, isPaymentRequiredAndNotCompleted, getPaymentStatusText, getPaymentStatusDisplay as getPaymentStatusDisplayUtil } from '../src/utils/statusUtils';
import StripePayment from '../src/components/StripePayment';

export default function BookingDetailsScreen() {
  const theme = useTheme();
  const { bookingId, eventId } = useLocalSearchParams<{ bookingId: string; eventId: string }>();
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Fetch booking data
  const { data: incomingBookings = { data: [] } } = useIncomingEventBookings();
  const { data: outgoingBookings = { data: [] } } = useOutgoingEventBookings();

  // Find the specific booking
  const allBookings = [...(incomingBookings.data || []), ...(outgoingBookings.data || [])];
  const booking = allBookings.find((b: any) => b.event_booking?.id === bookingId);

  // Determine if this is a sent booking (outgoing)
  const isSentBooking = outgoingBookings.data?.some((b: any) => b.event_booking?.id === bookingId);

  log.info('isSentBooking:', isSentBooking);
  log.info('Booking:', booking);
  log.info('Should show pay button:', isSentBooking && isPaymentRequired(booking?.event_booking?.booking_status || ''));
  log.info('Booking status:', booking?.event_booking?.booking_status);
  

  const handleGoBack = () => {
    router.back();
  };

  const handlePay = () => {
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    // Optionally refresh the booking data or show success message
    log.info('Payment completed successfully');
  };

  const handlePaymentError = (error: string) => {
    log.error('Payment error:', error);
    // Error is already handled in the StripePayment component
  };

  const handlePaymentCancel = () => {
    setShowPaymentModal(false);
  };

  // Calculate payment amount from event
  const getPaymentAmount = () => {
    if (!booking?.event_offer?.price_per_person) return 0;
    return (booking.event_offer.price_per_person * booking.event_booking.number_of_guests) / 100;
  };

  // Check if payment has been completed using PaymentLite data
  const isPaymentCompleted = () => {
    // First check if there's PaymentLite data with succeeded status
    if (booking?.payment?.payment_status === 'succeeded') {
      return true;
    }
    // Fallback to total_amount_paid field
    return (booking?.event_booking?.total_amount_paid || 0) > 0;
  };

  // Get payment status display using PaymentLite data when available
  const getPaymentStatusDisplay = () => {
    // If we have PaymentLite data, use it for more accurate status
    if (booking?.payment?.payment_status) {
      return getPaymentStatusDisplayUtil(booking.payment.payment_status, theme);
    }
    
    // Fallback to basic payment status
    if (isPaymentCompleted()) {
      return { text: '✅ Paid', color: theme.colors.primary };
    } else if (isPaymentRequired(booking?.event_booking?.booking_status || '')) {
      return { text: '💳 Payment Required', color: '#F57C00' };
    } else {
      return { text: '⏳ Pending', color: '#666666' };
    }
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return moment(dateString).format('MMMM Do YYYY, h:mm A');
  };



  if (!booking) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Surface style={[styles.content, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.title, { color: theme.colors.onSurface }]}>
            Booking Not Found
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.onSurface }]}>
            The booking you're looking for could not be found.
          </Text>
          <Button 
            mode="contained" 
            onPress={handleGoBack}
            style={[styles.backButton, { backgroundColor: theme.colors.primary }]}
          >
            Go Back
          </Button>
        </Surface>
      </SafeAreaView>
    );
  }

  const statusDisplay = getStatusDisplay(booking.event_booking?.booking_status, theme);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView}>
        <Surface style={[styles.content, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.title, { color: theme.colors.onSurface }]}>
            Booking Details
          </Text>
          
          <Divider style={styles.divider} />
          
          {/* Event Information */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Event Information
            </Text>
            <Text style={[styles.eventTitle, { color: theme.colors.primary }]}>
              {booking.event_offer?.title || 'Untitled Event'}
            </Text>
            <Text style={[styles.eventDescription, { color: theme.colors.onSurface }]}>
              {booking.event_offer?.description || 'No description available'}
            </Text>
          </View>

          <Divider style={styles.divider} />

          {/* Booking Status */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Booking Status
            </Text>
            <Chip 
              style={[styles.statusChip, { backgroundColor: statusDisplay.color + '20' }]}
              textStyle={{ color: statusDisplay.color }}
            >
              {statusDisplay.text}
            </Chip>
            {isSentBooking && (
              <Text style={[styles.statusNote, { color: theme.colors.onSurface }]}>
                This is a booking you sent to the host
              </Text>
            )}
            {!isSentBooking && (
              <Text style={[styles.statusNote, { color: theme.colors.onSurface }]}>
                This is a booking request from a guest
              </Text>
            )}
          </View>

          <Divider style={styles.divider} />

          {/* Booking Details */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Booking Details
            </Text>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                Requested:
              </Text>
              <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                {formatDate(booking.event_booking?.requestedAt)}
              </Text>
            </View>
            {booking.event_booking?.respondedAt && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                  Responded:
                </Text>
                <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                  {formatDate(booking.event_booking.respondedAt)}
                </Text>
              </View>
            )}
            {booking.event_booking?.message && (
              <View style={styles.messageSection}>
                <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                  Message:
                </Text>
                <Text style={[styles.messageText, { color: theme.colors.onSurface }]}>
                  "{booking.event_booking.message}"
                </Text>
              </View>
            )}
          </View>

          <Divider style={styles.divider} />

          {/* Technical Details */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Technical Details
            </Text>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                Booking ID:
              </Text>
              <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                {bookingId}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                Event ID:
              </Text>
              <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                {eventId}
              </Text>
            </View>
          </View>
          
          {/* Payment Summary Section for Sent Bookings */}
          {isSentBooking && (
            <View style={styles.paymentSummarySection}>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Payment Summary
              </Text>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Number of Guests:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
                  {booking.event_booking?.number_of_guests || 1}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Price per Person:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
                  €{((booking.event_offer?.price_per_person || 0) / 100).toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Total Amount:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.primary, fontWeight: 'bold' }]}>
                  €{getPaymentAmount().toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Payment Status:
                </Text>
                <Text style={[styles.summaryValue, { color: getPaymentStatusDisplay().color, fontWeight: 'bold' }]}>
                  {getPaymentStatusDisplay().text}
                </Text>
              </View>
              
              {isPaymentCompleted() && (
                <View style={styles.summaryRow}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                    Amount Paid:
                  </Text>
                  <Text style={[styles.summaryValue, { color: theme.colors.primary, fontWeight: 'bold' }]}>
                    €{((booking.event_booking?.total_amount_paid || 0) / 100).toFixed(2)}
                  </Text>
                </View>
              )}
              
              {booking.event_booking?.message && (
                <View style={styles.notesSection}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                    Notes to Host:
                  </Text>
                  <Text style={[styles.notesText, { color: theme.colors.onSurface }]}>
                    "{booking.event_booking.message}"
                  </Text>
                </View>
              )}
              
              {/* Pay Button for Confirmed Bookings - Only show if payment is required and not completed */}
              {isPaymentRequiredAndNotCompleted(
                booking.event_booking?.booking_status || '', 
                booking.payment?.payment_status
              ) && (
                <View style={styles.paymentSection}>
                  <Button 
                    mode="contained" 
                    onPress={handlePay}
                    style={[styles.payButton, { backgroundColor: theme.colors.primary }]}
                    icon="credit-card"
                  >
                    Pay Now
                  </Button>
                </View>
              )}
              
              {/* Payment Details Section - Show PaymentLite information when available */}
              {booking.payment && booking.payment.stripe_payment_id &&(
                <View style={styles.paymentDetailsSection}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                    Payment Details
                  </Text>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                      Payment ID:
                    </Text>
                    <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                      {booking.payment.id}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                      Stripe Payment ID:
                    </Text>
                    <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                      {booking.payment.stripe_payment_id}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                      Payment Method:
                    </Text>
                    <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                      {booking.payment.payment_method}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                      Created:
                    </Text>
                    <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                      {formatDate(booking.payment.created_at)}
                    </Text>
                  </View>
                  
                  {booking.payment.updated_at !== booking.payment.created_at && (
                    <View style={styles.detailRow}>
                      <Text style={[styles.detailLabel, { color: theme.colors.onSurface }]}>
                        Updated:
                      </Text>
                      <Text style={[styles.detailValue, { color: theme.colors.onSurface }]}>
                        {formatDate(booking.payment.updated_at)}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          )}
          
          {/* Booking Summary Section for Incoming Bookings (Host View) */}
          {!isSentBooking && (
            <View style={styles.paymentSummarySection}>
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Booking Summary
              </Text>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Guest Name:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
                  {booking.event_booking?.user?.nickname || 'Unknown Guest'}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Number of Guests:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.onSurface }]}>
                  {booking.event_booking?.number_of_guests || 1}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                  Total Revenue:
                </Text>
                <Text style={[styles.summaryValue, { color: theme.colors.primary, fontWeight: 'bold' }]}>
                  €{getPaymentAmount().toFixed(2)}
                </Text>
              </View>
              
              {booking.event_booking?.message && (
                <View style={styles.notesSection}>
                  <Text style={[styles.summaryLabel, { color: theme.colors.onSurface }]}>
                    Guest Message:
                  </Text>
                  <Text style={[styles.notesText, { color: theme.colors.onSurface }]}>
                    "{booking.event_booking.message}"
                  </Text>
                </View>
              )}
            </View>
          )}

          <Button 
            mode="outlined" 
            onPress={handleGoBack}
            style={[styles.backButton, { borderColor: theme.colors.primary }]}
            textColor={theme.colors.primary}
          >
            Go Back
          </Button>
        </Surface>
      </ScrollView>

      {/* Payment Modal */}
      <Modal
        visible={showPaymentModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handlePaymentCancel}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <StripePayment
            bookingId={bookingId}
            eventId={eventId}
            amount={getPaymentAmount()}
            currency="EUR"
            onSuccess={handlePaymentSuccess}
            onError={handlePaymentError}
            onCancel={handlePaymentCancel}
          />
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 20,
    margin: 16,
    borderRadius: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 32,
    textAlign: 'center',
  },
  divider: {
    marginVertical: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  eventTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  eventDescription: {
    fontSize: 14,
    opacity: 0.8,
    lineHeight: 20,
  },
  statusChip: {
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  statusNote: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 8,
    fontStyle: 'italic',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    opacity: 0.8,
    flex: 2,
    textAlign: 'right',
  },
  messageSection: {
    marginTop: 12,
  },
  messageText: {
    fontSize: 14,
    fontStyle: 'italic',
    opacity: 0.8,
    marginTop: 4,
    lineHeight: 20,
  },
  infoContainer: {
    marginBottom: 32,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.8,
  },
  payButton: {
    borderRadius: 8,
    marginTop: 16,
    marginBottom: 12,
  },
  paymentSection: {
    marginTop: 16,
    alignItems: 'center',
  },
  paymentAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  paymentSummarySection: {
    marginTop: 16,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    flex: 1,
    textAlign: 'right',
  },
  notesSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  notesText: {
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 4,
    lineHeight: 20,
  },
  paymentDetailsSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    borderRadius: 8,
    marginTop: 16,
  },
});
