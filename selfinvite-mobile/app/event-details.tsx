import React, { useState } from 'react';
import { ScrollView, StyleSheet, View, Alert, Share, Dimensions, Image } from 'react-native';
import { 
  Surface, 
  Text, 
  useTheme, 
  Button, 
  Chip, 
  IconButton, 
  Card,
  Avatar,
  Badge,
  Divider,
  ActivityIndicator,
  Dialog,
  Portal,
  SegmentedButtons,
  TextInput
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MapView, { Circle, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEvent } from '../src/hooks/useEvents';
import { useRequestParticipationEventBooking } from '../src/hooks/useEventBookings';
import { useAuthStore } from '../src/stores/authStore';
import { useDialogStore } from '../src/stores/dialogStore';
import { getKitchenOptions } from '../src/types/kitchenOptions';
import { getBeverageOptions } from '../src/types/beverageOptions';
import { getEventOptions } from '../src/types/eventOptions';
import { getLocationOptions } from '../src/types/locationOptions';
import { getIntoleranceOptions } from '../src/types/intoleranceOptions';
import moment from 'moment';
import { CreateEventBookingRequest } from '@/types/eventBooking';
import logger from '@/common/logger';

const { width: screenWidth } = Dimensions.get('window');

// Helper function to get option data by id
const getOptionData = (id: string, options: any[]) => {
  const option = options.find(opt => opt.id === id);
  return option || { id, label: id, value: id, icon: null };
};

// Helper function to format type arrays into readable text
const formatTypes = (types: string[] | undefined, options: any[]) => {
  if (!types || types.length === 0) return 'Not specified';
  return types.map(type => getOptionData(type, options).label).join(', ');
};

export default function EventDetailsScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { eventId } = useLocalSearchParams<{ eventId: string }>();
  const { user, isAuthenticated } = useAuthStore();
  const { showError, showSuccess } = useDialogStore();
  const requestParticipationBookingMutation = useRequestParticipationEventBooking();
  
  // Booking dialog state
  const [showBookingDialog, setShowBookingDialog] = useState(false);
  const [numberOfGuests, setNumberOfGuests] = useState(1);
  const [messageToHost, setMessageToHost] = useState('');
  
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  
  // Fetch event data
  const { data: event, isLoading, error, refetch } = useEvent(eventId!);

  if (!eventId) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Event ID not provided
          </Text>
          <Button mode="outlined" onPress={() => router.back()}>
            Go Back
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading event details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !event) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons 
            name="alert-circle" 
            size={64} 
            color={theme.colors.error} 
          />
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            Failed to load event details
          </Text>
          <Button mode="outlined" onPress={() => refetch()}>
            Try Again
          </Button>
          <Button mode="text" onPress={() => router.back()}>
            Go Back
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  const isHost = user?.id === event.host_user_id;
  const isPastEvent = new Date(event.event_date) < new Date();
  const eventDate = moment(event.event_date);

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out this event: ${event.title} in ${event.city}`,
        url: `selfinvite://event/${event.id}`,
      });
    } catch (error) {
      logger.error('Error sharing event:', error);
    }
  };

  const handleJoinEvent = () => {
    if (!isAuthenticated) {
      showError('You must be logged in to join events.', 'Authentication Required');
      return;
    }
    
    setShowBookingDialog(true);
  };

  const handleConfirmBooking = async () => {
    try {
      const bookingData: CreateEventBookingRequest = {
        event_offer_id: event.id,
        number_of_guests: numberOfGuests,
        notes_to_host: messageToHost.trim()
      };
      
      await requestParticipationBookingMutation.mutateAsync(bookingData);
      
      setShowBookingDialog(false);
      setMessageToHost('');
      setNumberOfGuests(1);
      
      showSuccess(
        'Your booking request has been sent to the host!',
        'Booking Request Sent'
      );
    } catch (error) {
      logger.error('Failed to create booking:', error);
      showError(
        'Failed to send booking request. Please try again.',
        'Booking Error'
      );
    }
  };

  const handleCancelBooking = () => {
    setShowBookingDialog(false);
    setMessageToHost('');
    setNumberOfGuests(1);
  };

  const handleEditEvent = () => {
    router.push(`/(tabs)/profile/create-event?eventId=${event.id}`);
  };

  const handleContactHost = () => {
    // TODO: Navigate to messaging screen
    router.push(`/chat/${event.host_user_id}`);
  };

  // Map overview configuration (blur precise position with a circle)
  const hasCoordinates = typeof event.latitude === 'number' && typeof event.longitude === 'number';
  const mapRegion: Region | undefined = hasCoordinates
    ? {
        latitude: event.latitude,
        longitude: event.longitude,
        latitudeDelta: 0.02,
        longitudeDelta: 0.02,
      }
    : undefined;
  const blurRadiusMeters = 400; // adjust to control how much the location is blurred

  const renderMediaCarousel = () => {
    if (!event.medias || event.medias.length === 0) {
      return (
        <Surface style={[styles.mediaContainer, { backgroundColor: theme.colors.surfaceVariant }]}>
          <View style={styles.placeholderImage}>
            <MaterialCommunityIcons 
              name="image-off" 
              size={64} 
              color={theme.colors.onSurfaceVariant} 
            />
            <Text style={[styles.placeholderText, { color: theme.colors.onSurfaceVariant }]}>
              No images available
            </Text>
          </View>
        </Surface>
      );
    }

    return (
      <View style={styles.mediaContainer}>
        <ScrollView 
          horizontal 
          pagingEnabled 
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
            setSelectedImageIndex(index);
          }}
        >
          {event.medias.map((media, index) => (
            <View key={index} style={styles.mediaItem}>
              {media.type === 'image' ? (
                <Image 
                  source={{ uri: media.url }} 
                  style={styles.eventImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={[styles.eventImage, { backgroundColor: theme.colors.surfaceVariant }]}>
                  <MaterialCommunityIcons 
                    name="play-circle" 
                    size={64} 
                    color={theme.colors.primary} 
                  />
                  <Text style={{ color: theme.colors.onSurfaceVariant }}>Video</Text>
                </View>
              )}
            </View>
          ))}
        </ScrollView>
        {event.medias.length > 1 && (
          <View style={styles.mediaIndicators}>
            {event.medias.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.mediaIndicator,
                  {
                    backgroundColor: index === selectedImageIndex 
                      ? theme.colors.primary 
                      : theme.colors.outline
                  }
                ]}
              />
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderTypeChips = (types: string[] | undefined, options: any[], fallbackIcon: string) => {
    if (!types || types.length === 0) return null;
    
    return (
      <View style={styles.chipContainer}>
        {types.map((type, index) => {
          const optionData = getOptionData(type, options);
          return (
            <Chip
              key={index}
              mode="outlined"
              icon={optionData.icon ? () => (
                <Image 
                  source={optionData.icon} 
                  style={styles.chipIcon} 
                  resizeMode="contain"
                />
              ) : fallbackIcon}
              style={styles.typeChip}
            >
              {optionData.label}
            </Chip>
          );
        })}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={() => router.back()}
          />
          <Text style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
            Event Details
          </Text>
          <View style={styles.headerActions}>
            <IconButton
              icon="share-variant"
              size={24}
              onPress={handleShare}
            />
            {isHost && (
              <IconButton
                icon="pencil"
                size={24}
                onPress={handleEditEvent}
              />
            )}
          </View>
        </View>
      </Surface>

      <ScrollView style={styles.content}>
        {/* Media Carousel */}
        {renderMediaCarousel()}

        {/* Event Status Badge */}
        <View style={styles.statusContainer}>
          <Badge 
            style={[
              styles.statusBadge, 
              { 
                backgroundColor: isPastEvent 
                  ? theme.colors.outline 
                  : event.status === 'published' 
                    ? theme.colors.primary 
                    : theme.colors.error 
              }
            ]}
          >
            {isPastEvent ? 'Completed' : event.status.toUpperCase()}
          </Badge>
        </View>

        {/* Event Title and Basic Info */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons 
              name="information" 
              size={24} 
              color={theme.colors.primary} 
              style={styles.sectionIcon}
            />
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Event Information
            </Text>
          </View>
          <Divider style={styles.sectionDivider} />
          
          <Text style={[styles.eventTitle, { color: theme.colors.onSurface }]}>
            {event.title}
          </Text>

          {/* Date and Time */}
          <View style={styles.infoRow}>
            <MaterialCommunityIcons 
              name="calendar-clock" 
              size={24} 
              color={theme.colors.primary} 
            />
            <View style={styles.infoContent}>
              <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
                Date & Time
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {eventDate.format('dddd, MMMM Do YYYY')}
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {eventDate.format('h:mm A')} 
                {event.duration_minutes && ` (${event.duration_minutes} min)`}
              </Text>
            </View>
          </View>

          {/* Location */}
          <View style={styles.infoRow}>
            <MaterialCommunityIcons 
              name="map-marker" 
              size={24} 
              color={theme.colors.primary} 
            />
            <View style={styles.infoContent}>
              <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
                Location
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {event.city}
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {event.country}
              </Text>
            </View>
          </View>

          {/* Price and Participants */}
          <View style={styles.infoRow}>
            <MaterialCommunityIcons 
              name="account-group" 
              size={24} 
              color={theme.colors.primary} 
            />
            <View style={styles.infoContent}>
              <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
                Participants & Price
              </Text>
              <Text style={[styles.infoValue, { color: theme.colors.onSurface }]}>
                {event.currentParticipants || 0} / {event.max_participants} participants
              </Text>
              {event.price_per_person !== undefined && (
                <Text style={[styles.infoValue, { color: theme.colors.primary }]}>
                  {event.price_per_person > 0 
                    ? `${event.currency || '€'}${((event.price_per_person || 0) / 100)} per person`
                    : 'Free event'
                  }
                </Text>
              )}
            </View>
          </View>
        </Surface>

        {/* Location Map Overview (approximate) */}
        {hasCoordinates && (
          <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="map" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Location Overview
              </Text>
            </View>
            <Divider style={styles.sectionDivider} />

            <View style={styles.mapOverviewContainer}>
              <MapView
                provider={PROVIDER_GOOGLE}
                style={styles.mapOverview}
                region={mapRegion as Region}
                scrollEnabled={false}
                zoomEnabled={false}
                rotateEnabled={false}
                pitchEnabled={false}
                toolbarEnabled={false}
                liteMode={false}
                showsUserLocation={false}
                showsMyLocationButton={false}
                pointerEvents="none"
              >
                <Circle
                  center={{ latitude: event.latitude, longitude: event.longitude }}
                  radius={blurRadiusMeters}
                  strokeWidth={1}
                  strokeColor={theme.colors.primary}
                  fillColor={`${theme.colors.primary}33`}
                />
              </MapView>
            </View>

            <Text style={[styles.mapDisclaimer, { color: theme.colors.onSurfaceVariant }]}>Approximate area to protect host privacy</Text>
          </Surface>
        )}

        {/* Description */}
        {event.description ? (
          <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="text" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                About this event
              </Text>
            </View>
            <Divider style={styles.sectionDivider} />
            <Text style={[styles.eventDescription, { color: theme.colors.onSurface }]}>
              {event.description}
            </Text>
          </Surface>
        ) : null}

        {/* Host Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons 
              name="account" 
              size={24} 
              color={theme.colors.primary} 
              style={styles.sectionIcon}
            />
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Host Information
            </Text>
          </View>
          <Divider style={styles.sectionDivider} />
          
          <View style={styles.hostContainer}>
            <Avatar.Text 
              size={48} 
              label={event.host.username.charAt(0).toUpperCase()} 
              style={{ backgroundColor: theme.colors.primary }}
            />
            <View style={styles.hostInfo}>
              <Text style={[styles.hostName, { color: theme.colors.onSurface }]}>
                {event.host.username}
              </Text>
              <Text style={[styles.hostLabel, { color: theme.colors.onSurfaceVariant }]}>
                Event Host
              </Text>
              
              {/* Host Reviews & Feedback */}
              <View style={styles.hostStatsContainer}>
                <View style={styles.hostStatItem}>
                  <MaterialCommunityIcons 
                    name="star" 
                    size={16} 
                    color={theme.colors.primary} 
                  />
                  <Text style={[styles.hostStatText, { color: theme.colors.onSurfaceVariant }]}>
                    {(event.host as any).averageRating ? `${(event.host as any).averageRating.toFixed(1)}` : 'N/A'} 
                    ({(event.host as any).totalReviews || 0} reviews)
                  </Text>
                </View>
                
                {(event.host as any).joinedAt && (
                  <View style={styles.hostStatItem}>
                    <MaterialCommunityIcons 
                      name="calendar-plus" 
                      size={16} 
                      color={theme.colors.onSurfaceVariant} 
                    />
                    <Text style={[styles.hostStatText, { color: theme.colors.onSurfaceVariant }]}>
                      Host since {moment((event.host as any).joinedAt).format('MMM YYYY')}
                    </Text>
                  </View>
                )}
              </View>
            </View>
            {!isHost && (
              <Button
                mode="outlined"
                compact
                onPress={handleContactHost}
                icon="message-text"
              >
                Message
              </Button>
            )}
          </View>
        </Surface>

        {/* Event Types */}
        {(event.type_kitchens || event.type_events || event.type_beverages || 
          event.type_locations || event.type_intolerances) && (
          <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="tag-multiple" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Event Categories
              </Text>
            </View>
            <Divider style={styles.sectionDivider} />

            {event.type_kitchens && event.type_kitchens.length > 0 && (
              <View style={styles.typeSection}>
                <View style={styles.typeLabelContainer}>
                  <MaterialCommunityIcons name="chef-hat" size={16} color={theme.colors.primary} />
                  <Text style={[styles.typeLabel, { color: theme.colors.onSurface }]}>
                    Kitchen Types
                  </Text>
                </View>
                {renderTypeChips(event.type_kitchens, getKitchenOptions(), 'chef-hat')}
              </View>
            )}

            {event.type_events && event.type_events.length > 0 && (
              <>
                {event.type_kitchens && event.type_kitchens.length > 0 && <Divider style={styles.categoryDivider} />}
                <View style={styles.typeSection}>
                  <View style={styles.typeLabelContainer}>
                    <MaterialCommunityIcons name="calendar-star" size={16} color={theme.colors.primary} />
                    <Text style={[styles.typeLabel, { color: theme.colors.onSurface }]}>
                      Event Types
                    </Text>
                  </View>
                  {renderTypeChips(event.type_events, getEventOptions(), 'calendar')}
                </View>
              </>
            )}

            {event.type_beverages && event.type_beverages.length > 0 && (
              <>
                {(event.type_kitchens && event.type_kitchens.length > 0) || (event.type_events && event.type_events.length > 0) ? <Divider style={styles.categoryDivider} /> : null}
                <View style={styles.typeSection}>
                  <View style={styles.typeLabelContainer}>
                    <MaterialCommunityIcons name="glass-wine" size={20} color={theme.colors.primary} />
                    <Text style={[styles.typeLabel, { color: theme.colors.onSurface }]}>
                      Beverages
                    </Text>
                  </View>
                  {renderTypeChips(event.type_beverages, getBeverageOptions(), 'glass-wine')}
                </View>
              </>
            )}

            {event.type_locations && event.type_locations.length > 0 && (
              <>
                {(event.type_kitchens && event.type_kitchens.length > 0) || (event.type_events && event.type_events.length > 0) || (event.type_beverages && event.type_beverages.length > 0) ? <Divider style={styles.categoryDivider} /> : null}
                <View style={styles.typeSection}>
                  <View style={styles.typeLabelContainer}>
                    <MaterialCommunityIcons name="home-variant" size={16} color={theme.colors.primary} />
                    <Text style={[styles.typeLabel, { color: theme.colors.onSurface }]}>
                      Location Types
                    </Text>
                  </View>
                  {renderTypeChips(event.type_locations, getLocationOptions(), 'map-marker')}
                </View>
              </>
            )}

            {event.type_intolerances && event.type_intolerances.length > 0 && (
              <>
                {(event.type_kitchens && event.type_kitchens.length > 0) || (event.type_events && event.type_events.length > 0) || (event.type_beverages && event.type_beverages.length > 0) || (event.type_locations && event.type_locations.length > 0) ? <Divider style={styles.categoryDivider} /> : null}
                <View style={styles.typeSection}>
                  <View style={styles.typeLabelContainer}>
                    <MaterialCommunityIcons name="alert-circle" size={16} color={theme.colors.primary} />
                    <Text style={[styles.typeLabel, { color: theme.colors.onSurface }]}>
                      Dietary Considerations
                    </Text>
                  </View>
                  {renderTypeChips(event.type_intolerances, getIntoleranceOptions(), 'food-off')}
                </View>
              </>
            )}
          </Surface>
        )}

        {/* Action Buttons */}
        {!isPastEvent && (
          <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons 
                name="cog" 
                size={24} 
                color={theme.colors.primary} 
                style={styles.sectionIcon}
              />
              <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Actions
              </Text>
            </View>
            <Divider style={styles.sectionDivider} />
            
            {isHost ? (
              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={handleEditEvent}
                  style={styles.actionButton}
                  icon="pencil"
                >
                  Edit Event
                </Button>
                <Button
                  mode="contained"
                  onPress={() => {
                    // TODO: Navigate to manage event screen
                    showSuccess('Manage event feature coming soon!', 'Info');
                  }}
                  style={styles.actionButton}
                  icon="cog"
                >
                  Manage
                </Button>
              </View>
            ) : (
              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={handleContactHost}
                  style={styles.actionButton}
                  icon="message-text"
                >
                  Contact Host
                </Button>
                <Button
                  mode="contained"
                  onPress={handleJoinEvent}
                  style={styles.actionButton}
                  icon="account-plus"
                  disabled={!isAuthenticated}
                >
                  {isAuthenticated ? 'Join Event' : 'Login to Join'}
                </Button>
              </View>
            )}
          </Surface>
        )}

        {/* Bottom spacing */}
        <View style={{ height: 40 }} />
      </ScrollView>
      
      {/* Booking Dialog */}
      <Portal>
        <Dialog 
          visible={showBookingDialog} 
          onDismiss={handleCancelBooking}
          style={styles.bookingDialog}
        >
          <Dialog.Title style={[styles.dialogTitle, { color: theme.colors.onSurface }]}>
            Book Event
          </Dialog.Title>
          
          <Dialog.Content style={styles.dialogContent}>
            <Text style={[styles.eventTitle, { color: theme.colors.onSurface }]}>
              {event?.title}
            </Text>
            
            <Text style={[styles.eventDate, { color: theme.colors.onSurface, opacity: 0.7 }]}>
              {eventDate.format('MMMM Do YYYY, h:mm A')}
            </Text>
            
            <Text style={[styles.eventLocation, { color: theme.colors.onSurface, opacity: 0.7 }]}>
              {event?.city}, {event?.country}
            </Text>
            
            <View style={styles.priceContainer}>
              <Text style={[styles.priceLabel, { color: theme.colors.onSurface }]}>
                Price per person:
              </Text>
              <Text style={[styles.priceValue, { color: theme.colors.primary, fontWeight: 'bold' }]}>
                €{((event?.price_per_person || 0) / 100).toFixed(2)}
              </Text>
            </View>
            
            <View style={styles.guestsContainer}>
              <Text style={[styles.guestsLabel, { color: theme.colors.onSurface }]}>
                Number of guests:
              </Text>
              <SegmentedButtons
                value={numberOfGuests.toString()}
                onValueChange={(value) => setNumberOfGuests(parseInt(value))}
                buttons={Array.from({ length: Math.min(2, event?.max_participants || 2) }, (_, i) => ({
                  value: (i + 1).toString(),
                  label: (i + 1).toString()
                }))}
                style={styles.guestsSelector}
              />
            </View>
            
            <View style={styles.totalContainer}>
              <Text style={[styles.totalLabel, { color: theme.colors.onSurface }]}>
                Total:
              </Text>
              <Text style={[styles.totalValue, { color: theme.colors.primary, fontWeight: 'bold' }]}>
                €{(((event?.price_per_person || 0) / 100) * numberOfGuests).toFixed(2)}
              </Text>
            </View>
            
            <TextInput
              label="Message to host (optional)"
              value={messageToHost}
              onChangeText={setMessageToHost}
              multiline
              numberOfLines={3}
              style={styles.messageInput}
              mode="outlined"
              placeholder="Let the host know about any dietary restrictions or special requests..."
            />
          </Dialog.Content>
          
          <Dialog.Actions style={styles.dialogActions}>
            <Button 
              mode="outlined" 
              onPress={handleCancelBooking}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
            <Button 
              mode="contained" 
              onPress={handleConfirmBooking}
              disabled={requestParticipationBookingMutation.isPending}
              style={styles.confirmButton}
            >
              {requestParticipationBookingMutation.isPending ? 'Sending...' : 'Send Request'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingVertical: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerActions: {
    flexDirection: 'row',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  mediaContainer: {
    height: 250,
    position: 'relative',
  },
  mediaItem: {
    width: screenWidth,
    height: 250,
  },
  eventImage: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    marginTop: 8,
    fontSize: 16,
  },
  mediaIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  mediaIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  statusContainer: {
    position: 'absolute',
    top: 260,
    right: 16,
    zIndex: 1,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    marginRight: 12,
    minHeight: 32,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionIcon: {
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  sectionDivider: {
    marginBottom: 16,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  eventDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
    opacity: 0.8,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    lineHeight: 20,
  },
  hostContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hostInfo: {
    flex: 1,
    marginLeft: 12,
  },
  hostName: {
    fontSize: 16,
    fontWeight: '600',
  },
  hostLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  hostStatsContainer: {
    marginTop: 8,
  },
  hostStatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  hostStatText: {
    fontSize: 12,
    marginLeft: 6,
  },
  typeSection: {
    marginBottom: 12,
    paddingVertical: 4,
  },
  typeLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    color: '#333',
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    rowGap: 6,
    columnGap: 6,
  },
  typeChip: {
    marginBottom: 2,
    borderRadius: 16,
    minHeight: 36,
  },
  chipIcon: {
    width: 16,
    height: 16,
  },
  categoryDivider: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  mapOverviewContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
  },
  mapOverview: {
    flex: 1,
  },
  mapDisclaimer: {
    marginTop: 8,
    fontSize: 12,
    textAlign: 'center',
  },
  // Booking Dialog Styles
  bookingDialog: {
    borderRadius: 16,
  },
  dialogTitle: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
  },
  dialogContent: {
    paddingVertical: 16,
  },
  eventDate: {
    fontSize: 14,
    marginBottom: 4,
  },
  eventLocation: {
    fontSize: 14,
    marginBottom: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 8,
  },
  priceLabel: {
    fontSize: 14,
  },
  priceValue: {
    fontSize: 16,
  },
  guestsContainer: {
    marginBottom: 16,
  },
  guestsLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  guestsSelector: {
    marginBottom: 8,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 12,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  totalValue: {
    fontSize: 18,
  },
  messageInput: {
    marginTop: 8,
  },
  dialogActions: {
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  cancelButton: {
    marginRight: 8,
  },
  confirmButton: {
    flex: 1,
  },
});
