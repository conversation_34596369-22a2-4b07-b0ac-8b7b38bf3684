import { StyleSheet } from 'react-native';
import { images } from './images';
import { colors } from './colors';
import { DefaultTheme } from 'react-native-paper';
import i18n from '@translations/index';

export const styles = StyleSheet.create({
  MainContainer: {
    flex: 1,
    paddingTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 32,
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  text: {
    marginHorizontal: 20,
    color: '#fff',
    fontSize: 20,
  },
  image: {
    width: 360,
    height: 360,
    resizeMode: 'contain',
  },
});

export const slides = [
  {
    key: 'k1',
    title: i18n.t('__slide_1_title'),
    text: i18n.t('__slide_1_text'),
    image: images._people_slide,
    titleStyle: styles.title,
    textStyle: styles.text,
    imageStyle: styles.image,
    backgroundColor: '#ef754b',
  },
  {
    key: 'k2',
    title: i18n.t('__slide_2_title'),
    text: i18n.t('__slide_2_text'),
    image: images._map_slide,
    titleStyle: styles.title,
    textStyle: styles.text,
    imageStyle: styles.image,
    backgroundColor: '#27aae1',
  },
  {
    key: 'k3',
    title: i18n.t('__slide_3_title'),
    text: i18n.t('__slide_3_text'),
    image: images._food_slide,
    titleStyle: styles.title,
    textStyle: styles.text,
    imageStyle: styles.image,
    backgroundColor: '#a8cf5a',
  },
  {
    key: 'k4',
    title: i18n.t('__slide_4_title'),
    text: i18n.t('__slide_4_text'),
    image: images._table_slide,
    titleStyle: styles.title,
    textStyle: styles.text,
    imageStyle: styles.image,
    backgroundColor: colors.primary,
  },
];

export const theme: ReactNativePaper.Theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: colors.background,
    primary: colors.primary,
    accent: colors.secondary,
    text: colors.text,
  },
  roundness: 20,
  fonts: {
    regular: {
      fontFamily: 'System',
      fontWeight: '400',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
  },
};
