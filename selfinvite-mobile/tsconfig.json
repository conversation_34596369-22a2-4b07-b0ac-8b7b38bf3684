{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@translations/*": ["./src/translations/*"], "@lib": ["./lib/*"], "@/*": ["./src/*"], "@assets/*": ["./assets/*"], "@types/*": ["./src/types/*"], "@components/*": ["./components/*"], "@/screens/*": ["./src/screens/*"], "@/hooks/*": ["./src/hooks/*"], "@/services/*": ["./src/services/*"], "@/stores/*": ["./src/stores/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["src/constants/*"], "@/navigation/*": ["./src/navigation/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}