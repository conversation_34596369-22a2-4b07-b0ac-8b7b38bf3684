import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import type {
  SearchFilters,
  FilterState,
  SearchHistoryItem,
  SavedSearch,
  SearchSortOptions,
} from '../types/search'
import { SEARCH_CONSTANTS } from '../types/search'

interface SearchState extends FilterState {
  // Search query and results
  query: string
  recentQueries: string[]
  
  // Location state
  userLocation: {
    latitude: number
    longitude: number
    city?: string
  } | null
  locationPermissionGranted: boolean
  
  // Filter state
  activeFilters: SearchFilters
  appliedFilters: SearchFilters
  isFilterModalVisible: boolean
  hasUnsavedChanges: boolean
  
  // UI state
  searchMode: 'list' | 'map'
  sortOptions: SearchSortOptions[]
  
  // History and saved searches
  searchHistory: SearchHistoryItem[]
  savedSearches: SavedSearch[]
  
  // Actions
  setQuery: (query: string) => void
  addRecentQuery: (query: string) => void
  clearRecentQueries: () => void
  
  // Location actions
  setUserLocation: (location: { latitude: number; longitude: number; city?: string } | null) => void
  setLocationPermission: (granted: boolean) => void
  
  // Filter actions
  setActiveFilters: (filters: Partial<SearchFilters>) => void
  updateActiveFilter: (key: keyof SearchFilters, value: any) => void
  applyFilters: () => void
  clearFilters: () => void
  resetToAppliedFilters: () => void
  setFilterModalVisible: (visible: boolean) => void
  
  // UI actions
  setSearchMode: (mode: 'list' | 'map') => void
  setSortOptions: (options: SearchSortOptions[]) => void
  
  // History actions
  addToHistory: (item: Omit<SearchHistoryItem, 'id' | 'timestamp'>) => void
  clearHistory: () => void
  removeFromHistory: (id: string) => void
  
  // Saved searches actions
  saveSearch: (search: Omit<SavedSearch, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateSavedSearch: (id: string, updates: Partial<SavedSearch>) => void
  deleteSavedSearch: (id: string) => void
  
  // Utility actions
  getActiveFilterCount: () => number
  hasActiveFilters: () => boolean
  getFilterSummary: () => string[]
}

const initialFilters: SearchFilters = {
  priceRange: [SEARCH_CONSTANTS.MIN_PRICE, SEARCH_CONSTANTS.MAX_PRICE],
  durationRange: [0, SEARCH_CONSTANTS.MAX_DURATION_MIN],
  radius: SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS,
  hasAvailableSpots: true,
  includeNSFW: false,
  status: ['published'],
}

export const useSearchStore = create<SearchState>()(
  persist(
    (set, get) => ({
      // Initial state
      query: '',
      recentQueries: [],
      userLocation: null,
      locationPermissionGranted: false,
      activeFilters: { ...initialFilters },
      appliedFilters: { ...initialFilters },
      isFilterModalVisible: false,
      hasUnsavedChanges: false,
      searchMode: 'list',
      sortOptions: [{ field: 'event_date', direction: 'asc' }],
      searchHistory: [],
      savedSearches: [],

      // Query actions
      setQuery: (query: string) => {
        set({ query })
      },

      addRecentQuery: (query: string) => {
        if (!query.trim()) return
        
        set((state) => {
          const filtered = state.recentQueries.filter(q => q !== query)
          return {
            recentQueries: [query, ...filtered].slice(0, 10) // Keep only 10 recent queries
          }
        })
      },

      clearRecentQueries: () => {
        set({ recentQueries: [] })
      },

      // Location actions
      setUserLocation: (location) => {
        set({ userLocation: location })
      },

      setLocationPermission: (granted: boolean) => {
        set({ locationPermissionGranted: granted })
      },

      // Filter actions
      setActiveFilters: (filters: Partial<SearchFilters>) => {
        set((state) => {
          const newActiveFilters = { ...state.activeFilters, ...filters }
          return {
            activeFilters: newActiveFilters,
            hasUnsavedChanges: JSON.stringify(newActiveFilters) !== JSON.stringify(state.appliedFilters)
          }
        })
      },

      updateActiveFilter: (key: keyof SearchFilters, value: any) => {
        set((state) => {
          const newActiveFilters = { ...state.activeFilters, [key]: value }
          return {
            activeFilters: newActiveFilters,
            hasUnsavedChanges: JSON.stringify(newActiveFilters) !== JSON.stringify(state.appliedFilters)
          }
        })
      },

      applyFilters: () => {
        set((state) => ({
          appliedFilters: { ...state.activeFilters },
          hasUnsavedChanges: false,
          isFilterModalVisible: false
        }))
      },

      clearFilters: () => {
        const clearedFilters = { ...initialFilters }
        set({
          activeFilters: clearedFilters,
          appliedFilters: clearedFilters,
          hasUnsavedChanges: false
        })
      },

      resetToAppliedFilters: () => {
        set((state) => ({
          activeFilters: { ...state.appliedFilters },
          hasUnsavedChanges: false
        }))
      },

      setFilterModalVisible: (visible: boolean) => {
        set({ isFilterModalVisible: visible })
        if (!visible) {
          // Reset to applied filters when closing modal
          const state = get()
          if (state.hasUnsavedChanges) {
            state.resetToAppliedFilters()
          }
        }
      },

      // UI actions
      setSearchMode: (mode: 'list' | 'map') => {
        set({ searchMode: mode })
      },

      setSortOptions: (options: SearchSortOptions[]) => {
        set({ sortOptions: options })
      },

      // History actions
      addToHistory: (item) => {
        const newItem: SearchHistoryItem = {
          ...item,
          id: Date.now().toString(),
          timestamp: new Date().toISOString()
        }

        set((state) => {
          const filtered = state.searchHistory.filter(
            h => h.query !== item.query || JSON.stringify(h.filters) !== JSON.stringify(item.filters)
          )
          return {
            searchHistory: [newItem, ...filtered].slice(0, SEARCH_CONSTANTS.MAX_SEARCH_HISTORY)
          }
        })
      },

      clearHistory: () => {
        set({ searchHistory: [] })
      },

      removeFromHistory: (id: string) => {
        set((state) => ({
          searchHistory: state.searchHistory.filter(item => item.id !== id)
        }))
      },

      // Saved searches actions
      saveSearch: (search) => {
        const newSearch: SavedSearch = {
          ...search,
          id: Date.now().toString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        set((state) => ({
          savedSearches: [...state.savedSearches, newSearch]
        }))
      },

      updateSavedSearch: (id: string, updates) => {
        set((state) => ({
          savedSearches: state.savedSearches.map(search =>
            search.id === id
              ? { ...search, ...updates, updatedAt: new Date().toISOString() }
              : search
          )
        }))
      },

      deleteSavedSearch: (id: string) => {
        set((state) => ({
          savedSearches: state.savedSearches.filter(search => search.id !== id)
        }))
      },

      // Utility actions
      getActiveFilterCount: () => {
        const state = get()
        const filters = state.activeFilters
        let count = 0

        // Count non-default filters
        if (filters.query && filters.query.trim()) count++
        if (filters.city) count++
        if (filters.kitchenTypes && filters.kitchenTypes.length > 0) count++
        if (filters.beverageTypes && filters.beverageTypes.length > 0) count++
        if (filters.eventTypes && filters.eventTypes.length > 0) count++
        if (filters.locationTypes && filters.locationTypes.length > 0) count++
        if (filters.intolerances && filters.intolerances.length > 0) count++
        if (filters.dateRange && (filters.dateRange[0] || filters.dateRange[1])) count++
        if (filters.priceRange && (
          filters.priceRange[0] !== SEARCH_CONSTANTS.MIN_PRICE || 
          filters.priceRange[1] !== SEARCH_CONSTANTS.MAX_PRICE
        )) count++
        if (filters.durationRange && (
          filters.durationRange[0] !== 0 || 
          filters.durationRange[1] !== SEARCH_CONSTANTS.MAX_DURATION_MIN
        )) count++
        if (filters.maxParticipants) count++
        if (filters.radius !== SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS) count++
        if (filters.includeNSFW === true) count++
        if (filters.isPrivate !== undefined) count++

        return count
      },

      hasActiveFilters: () => {
        return get().getActiveFilterCount() > 0
      },

      getFilterSummary: () => {
        const state = get()
        const filters = state.activeFilters
        const summary: string[] = []

        if (filters.city) summary.push(filters.city)
        if (filters.kitchenTypes && filters.kitchenTypes.length > 0) {
          summary.push(`${filters.kitchenTypes.length} cuisine${filters.kitchenTypes.length > 1 ? 's' : ''}`)
        }
        if (filters.priceRange && (
          filters.priceRange[0] !== SEARCH_CONSTANTS.MIN_PRICE || 
          filters.priceRange[1] !== SEARCH_CONSTANTS.MAX_PRICE
        )) {
          summary.push(`€${filters.priceRange[0]}-${filters.priceRange[1]}`)
        }
        if (filters.dateRange && (filters.dateRange[0] || filters.dateRange[1])) {
          summary.push('Date range')
        }
        if (filters.radius !== SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS) {
          summary.push(`${filters.radius}km radius`)
        }

        return summary
      }
    }),
    {
      name: 'search-store',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist certain fields
      partialize: (state) => ({
        recentQueries: state.recentQueries,
        userLocation: state.userLocation,
        locationPermissionGranted: state.locationPermissionGranted,
        appliedFilters: state.appliedFilters,
        searchMode: state.searchMode,
        sortOptions: state.sortOptions,
        searchHistory: state.searchHistory,
        savedSearches: state.savedSearches,
      }),
    }
  )
)

// Selector hooks for better performance
export const useSearchQuery = () => useSearchStore((state) => state.query)
export const useSearchFilters = () => useSearchStore((state) => ({
  active: state.activeFilters,
  applied: state.appliedFilters,
  hasUnsaved: state.hasUnsavedChanges
}))
export const useSearchMode = () => useSearchStore((state) => state.searchMode)
export const useUserLocation = () => useSearchStore((state) => state.userLocation)
export const useSearchHistory = () => useSearchStore((state) => state.searchHistory)
export const useSavedSearches = () => useSearchStore((state) => state.savedSearches)
