import { create } from 'zustand'
import type { Event } from '../types'

interface EventsState {
  // State
  events: Event[]
  selectedEvent: Event | null
  filterType: 'all' | 'hosting' | 'attending' | 'past'
  isLoading: boolean
  error: string | null

  // Actions
  setEvents: (events: Event[]) => void
  setSelectedEvent: (event: Event | null) => void
  setFilterType: (filterType: 'all' | 'hosting' | 'attending' | 'past') => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Computed
  filteredEvents: () => Event[]
  hostingEvents: () => Event[]
  attendingEvents: () => Event[]
  pastEvents: () => Event[]
  
  // Reset
  reset: () => void
}

export const useEventsStore = create<EventsState>((set, get) => ({
  // Initial state
  events: [],
  selectedEvent: null,
  filterType: 'all',
  isLoading: false,
  error: null,

  // Actions
  setEvents: (events) => set({ events }),
  setSelectedEvent: (event) => set({ selectedEvent: event }),
  setFilterType: (filterType) => set({ filterType }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),

  // Computed getters
  filteredEvents: () => {
    const { events, filterType } = get()
    
    switch (filterType) {
      case 'hosting':
        return events.filter(event => event.status === 'published' && new Date(event.date) > new Date())
      case 'attending':
        return events.filter(event => event.status === 'published' && new Date(event.date) > new Date())
      case 'past':
        return events.filter(event => new Date(event.date) < new Date())
      default:
        return events
    }
  },

  hostingEvents: () => {
    const { events } = get()
    return events.filter(event => event.status === 'published' && new Date(event.date) > new Date())
  },

  attendingEvents: () => {
    const { events } = get()
    return events.filter(event => event.status === 'published' && new Date(event.date) > new Date())
  },

  pastEvents: () => {
    const { events } = get()
    return events.filter(event => new Date(event.date) < new Date())
  },

  // Reset
  reset: () => set({
    events: [],
    selectedEvent: null,
    filterType: 'all',
    isLoading: false,
    error: null,
  }),
})) 