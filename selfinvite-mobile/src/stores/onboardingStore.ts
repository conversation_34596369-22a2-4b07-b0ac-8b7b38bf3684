import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface OnboardingState {
  hasCompletedOnboarding: boolean;
  isLoading: boolean;
  completeOnboarding: () => Promise<void>;
  resetOnboarding: () => Promise<void>;
  checkOnboardingStatus: () => Promise<boolean>;
}

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set, get) => ({
      hasCompletedOnboarding: false,
      isLoading: true,

      completeOnboarding: async () => {
        try {
          set({ hasCompletedOnboarding: true, isLoading: false });
        } catch (error) {
          console.error('Error completing onboarding:', error);
        }
      },

      resetOnboarding: async () => {
        try {
          set({ hasCompletedOnboarding: false, isLoading: false });
        } catch (error) {
          console.error('Error resetting onboarding:', error);
        }
      },

      checkOnboardingStatus: async () => {
        try {
          const { hasCompletedOnboarding } = get();
          set({ isLoading: false });
          return hasCompletedOnboarding;
        } catch (error) {
          console.error('Error checking onboarding status:', error);
          set({ isLoading: false });
          return false;
        }
      },
    }),
    {
      name: 'onboarding-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.isLoading = false;
        }
      },
    }
  )
);
