import { create } from 'zustand';
import { colors } from '../constants/theme';

export type DialogType = 'error' | 'warning' | 'success' | 'confirmation' | 'info';

// Helper function to get color for dialog type
const getDialogColor = (type: DialogType): string => {
  switch (type) {
    case 'error':
      return colors.error; // Red: #B3261E
    case 'warning':
      return colors.warning; // Orange: #F57C00
    case 'success':
      return colors.success; // Green: #2E7D32
    case 'confirmation':
      return colors.info; // Blue: #1976D2
    case 'info':
      return colors.info; // Blue: #1976D2
    default:
      return colors.info;
  }
};

export interface DialogButton {
  text: string;
  onPress?: () => void;
  mode?: 'text' | 'outlined' | 'contained' | 'elevated' | 'contained-tonal';
  style?: 'primary' | 'secondary' | 'destructive';
}

export interface DialogState {
  // State
  visible: boolean;
  type: DialogType;
  title: string;
  message: string;
  buttons: DialogButton[];
  dismissible: boolean;
  color: string;
  
  // Actions
  showDialog: (options: {
    type: DialogType;
    title?: string;
    message: string;
    buttons?: DialogButton[];
    dismissible?: boolean;
  }) => void;
  hideDialog: () => void;
  showError: (message: string, title?: string) => void;
  showSuccess: (message: string, title?: string) => void;
  showWarning: (message: string, title?: string) => void;
  showConfirmation: (options: {
    message: string;
    title?: string;
    onConfirm: () => void;
    onCancel?: () => void;
    confirmText?: string;
    cancelText?: string;
  }) => void;
}

export const useDialogStore = create<DialogState>((set, get) => ({
  // Initial state
  visible: false,
  type: 'info' as DialogType,
  title: '',
  message: '',
  buttons: [],
  dismissible: true,
  color: getDialogColor('info'),

  // Actions
  showDialog: (options) => {
    set({
      visible: true,
      type: options.type,
      title: options.title || '',
      message: options.message,
      buttons: options.buttons || [{ text: 'OK', mode: 'contained' }],
      dismissible: options.dismissible !== undefined ? options.dismissible : true,
      color: getDialogColor(options.type),
    });
  },

  hideDialog: () => {
    set({
      visible: false,
      title: '',
      message: '',
      buttons: [],
      color: getDialogColor('info'),
    });
  },

  showError: (message, title = 'Error') => {
    get().showDialog({
      type: 'error',
      title,
      message,
      buttons: [{ text: 'OK', mode: 'contained' }],
    });
  },

  showSuccess: (message, title = 'Success') => {
    get().showDialog({
      type: 'success',
      title,
      message,
      buttons: [{ text: 'OK', mode: 'contained' }],
    });
  },

  showWarning: (message, title = 'Warning') => {
    get().showDialog({
      type: 'warning',
      title,
      message,
      buttons: [{ text: 'OK', mode: 'contained' }],
    });
  },

  showConfirmation: (options) => {
    get().showDialog({
      type: 'confirmation',
      title: options.title || 'Confirm',
      message: options.message,
      buttons: [
        {
          text: options.cancelText || 'Cancel',
          mode: 'outlined',
          style: 'secondary',
          onPress: () => {
            get().hideDialog();
            options.onCancel?.();
          },
        },
        {
          text: options.confirmText || 'Confirm',
          mode: 'contained',
          style: 'primary',
          onPress: () => {
            get().hideDialog();
            options.onConfirm();
          },
        },
      ],
      dismissible: false,
    });
  },
}));

// Convenience functions for easy access
export const showErrorDialog = (message: string, title?: string) => {
  useDialogStore.getState().showError(message, title);
};

export const showSuccessDialog = (message: string, title?: string) => {
  useDialogStore.getState().showSuccess(message, title);
};

export const showWarningDialog = (message: string, title?: string) => {
  useDialogStore.getState().showWarning(message, title);
};

export const showConfirmationDialog = (options: {
  message: string;
  title?: string;
  onConfirm: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
}) => {
  useDialogStore.getState().showConfirmation(options);
};

export const hideDialog = () => {
  useDialogStore.getState().hideDialog();
}; 