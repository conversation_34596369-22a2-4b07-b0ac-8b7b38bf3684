import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text, ActivityIndicator, IconButton } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { useStripe, useConfirmPayment } from '@stripe/stripe-react-native';
import { useCreatePaymentIntent, useConfirmPayment as useConfirmPaymentMutation } from '../hooks/usePayments';
import { router } from 'expo-router';
import { showSuccessDialog, showErrorDialog } from '../stores/dialogStore';
import log from '@/common/logger';

interface StripePaymentProps {
  bookingId: string;
  eventId: string;
  amount: number;
  currency?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
}

export default function StripePayment({
  bookingId,
  eventId,
  amount,
  currency = 'EUR',
  onSuccess,
  onError,
  onCancel,
}: StripePaymentProps) {
  const theme = useTheme();
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const { confirmPayment } = useConfirmPayment();
  
  const [loading, setLoading] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState<any>(null);

  // Hooks for payment operations
  const createPaymentIntentMutation = useCreatePaymentIntent();
  const confirmPaymentMutation = useConfirmPaymentMutation();

  // Create payment intent when component mounts
  useEffect(() => {
    if (amount > 0) {
      handleCreatePaymentIntent();
    }
  }, [amount]);

  const handleCreatePaymentIntent = async () => {
    try {
      setLoading(true);
      
      const result = await createPaymentIntentMutation.mutateAsync({
        currency,
        bookingId,
      });

      setPaymentIntent(result);
      
      // Log the result to debug the structure
      log.info('Payment intent result:', result);
      
      // Check if we have a valid client secret
      const clientSecret = result.clientSecret;
      if (!clientSecret) {
        throw new Error('No client secret received from payment intent');
      }
      
      // Initialize payment sheet
      const { error } = await initPaymentSheet({
        merchantDisplayName: 'Selfinvite',
        paymentIntentClientSecret: clientSecret,
        defaultBillingDetails: {
          name: 'Selfinvite User',
        },
        appearance: {
          colors: {
            primary: theme.colors.primary,
          },
        },
        returnURL: 'selfinvite://payments/success',
      });

      if (error) {
        log.error('Error initializing payment sheet:', error);
        onError?.(error.message || 'Failed to initialize payment');
      }
    } catch (error: any) {
      log.error('Error creating payment intent:', error);
      onError?.(error.message || 'Failed to create payment intent');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    try {
      setLoading(true);

      if (!paymentIntent) {
        throw new Error('Payment intent not available');
      }

      // Present payment sheet
      const { error: presentError } = await presentPaymentSheet();

      if (presentError) {
        if (presentError.code === 'Canceled') {
          onCancel?.();
          return;
        }
        throw presentError;
      }

      // Payment was successful
      log.info('Payment successful!');
      onSuccess?.();
      
      // Show success dialog
      showSuccessDialog(
        'Your payment has been processed successfully!',
        'Payment Successful'
      );
      
      // Navigate back after a short delay
      setTimeout(() => {
        router.back();
      }, 1500);

    } catch (error: any) {
      log.error('Payment error:', error);
      onError?.(error.message || 'Payment failed');
      
      // Show error dialog
      showErrorDialog(
        error.message || 'There was an error processing your payment. Please try again.',
        'Payment Failed'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    handleCreatePaymentIntent();
  };

  if (loading && !paymentIntent) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
          Preparing payment...
        </Text>
      </View>
    );
  }

  if (createPaymentIntentMutation.isError) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.errorHeader}>
          <View style={styles.errorHeaderSpacer} />
          <IconButton
            icon="close"
            size={24}
            iconColor={theme.colors.outline}
            onPress={onCancel || (() => {})}
            style={styles.closeButton}
          />
        </View>
        
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          Failed to prepare payment
        </Text>
        <Text style={[styles.errorSubtext, { color: theme.colors.onSurface }]}>
          {createPaymentIntentMutation.error?.message || 'Please try again'}
        </Text>
        <Button
          mode="contained"
          onPress={handleRetry}
          style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
          loading={createPaymentIntentMutation.isPending}
        >
          Retry
        </Button>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Complete Payment
      </Text>
      
      <View style={styles.paymentDetails}>
        <Text style={[styles.amount, { color: theme.colors.primary }]}>
          €{(amount).toFixed(2)}
        </Text>
        <Text style={[styles.currency, { color: theme.colors.onSurface }]}>
          {currency}
        </Text>
      </View>

      <Text style={[styles.description, { color: theme.colors.onSurface }]}>
        Secure payment powered by Stripe
      </Text>

      <Button
        mode="contained"
        onPress={handlePayment}
        style={[styles.payButton, { backgroundColor: theme.colors.primary }]}
        loading={loading}
        disabled={loading || !paymentIntent}
        icon="credit-card"
      >
        {loading ? 'Processing...' : 'Pay Now'}
      </Button>

      <Button
        mode="outlined"
        onPress={onCancel || (() => {})}
        style={[styles.cancelButton, { borderColor: theme.colors.outline }]}
        textColor={theme.colors.outline}
        disabled={loading}
      >
        Cancel
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 300,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  paymentDetails: {
    alignItems: 'center',
    marginBottom: 20,
  },
  amount: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  currency: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  description: {
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'center',
    marginBottom: 30,
  },
  payButton: {
    width: '100%',
    borderRadius: 8,
    marginBottom: 12,
  },
  cancelButton: {
    width: '100%',
    borderRadius: 8,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.8,
  },
  retryButton: {
    borderRadius: 8,
  },
  errorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 15,
  },
  errorHeaderSpacer: {
    width: 40, // Adjust as needed for spacing
  },
  closeButton: {
    // Add any specific styles for the close button if needed
  },
});
