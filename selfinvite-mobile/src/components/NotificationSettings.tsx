import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Card, Title, Paragraph, Button, Divider } from 'react-native-paper';
import { useNotifications } from '../hooks/useNotifications';
import logger from '../common/logger';
import { useDialogStore } from '../stores/dialogStore';

interface NotificationSettingsProps {
  onClose?: () => void;
}

export function NotificationSettings({ onClose }: NotificationSettingsProps) {
  const { showDialog } = useDialogStore();
  const {
    preferences,
    updatePreferences,
    requestPermissions,
    isEnabled,
    isInitialized,
  } = useNotifications();

  const [isUpdating, setIsUpdating] = useState(false);

  const handleTogglePreference = async (key: keyof NonNullable<typeof preferences>, value: boolean) => {
    if (!preferences) return;

    setIsUpdating(true);
    try {
      await updatePreferences({ [key]: value });
      logger.info(`Updated notification preference: ${key} = ${value}`);
    } catch (error) {
      logger.error('Failed to update notification preference:', error);
      showDialog({
        type: 'error',
        title: 'Error',
        message: 'Failed to update notification settings',
        buttons: [
          {
            text: 'OK',
            mode: 'contained',
            style: 'primary',
          },
        ],
        dismissible: true,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRequestPermissions = async () => {
    try {
      const granted = await requestPermissions();
      if (!granted) {
        showDialog({
          type: 'warning',
          title: 'Permissions Required',
          message: 'Please enable notifications in your device settings to receive important updates about your events and messages.',
          buttons: [
            {
              text: 'Cancel',
              mode: 'outlined',
              style: 'secondary',
            },
            {
              text: 'Open Settings',
              mode: 'contained',
              style: 'primary',
              onPress: () => {
                // This would typically open device settings
                logger.info('User requested to open device settings');
              },
            },
          ],
          dismissible: true,
        });
      }
    } catch (error) {
      logger.error('Failed to request notification permissions:', error);
      showDialog({
        type: 'error',
        title: 'Error',
        message: 'Failed to request notification permissions',
        buttons: [
          {
            text: 'OK',
            mode: 'contained',
            style: 'primary',
          },
        ],
        dismissible: true,
      });
    }
  };

  if (!isInitialized) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <Title>Loading...</Title>
          <Paragraph>Initializing notification settings...</Paragraph>
        </Card.Content>
      </Card>
    );
  }

  if (!isEnabled) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <Title>Enable Notifications</Title>
          <Paragraph>
            Get notified about new messages, booking updates, and event reminders.
          </Paragraph>
          <Button
            mode="contained"
            onPress={handleRequestPermissions}
            style={styles.enableButton}
          >
            Enable Notifications
          </Button>
        </Card.Content>
      </Card>
    );
  }

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Title>Notification Settings</Title>
          <Paragraph>
            Choose which notifications you'd like to receive.
          </Paragraph>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Event Notifications</Title>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Messages</Text>
              <Text style={styles.settingDescription}>
                New messages from hosts and guests
              </Text>
            </View>
            <Switch
              value={preferences?.messages ?? true}
              onValueChange={(value) => handleTogglePreference('messages', value)}
              disabled={isUpdating}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Bookings</Text>
              <Text style={styles.settingDescription}>
                Booking requests, confirmations, and updates
              </Text>
            </View>
            <Switch
              value={preferences?.bookings ?? true}
              onValueChange={(value) => handleTogglePreference('bookings', value)}
              disabled={isUpdating}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Payments</Text>
              <Text style={styles.settingDescription}>
                Payment confirmations and refunds
              </Text>
            </View>
            <Switch
              value={preferences?.payments ?? true}
              onValueChange={(value) => handleTogglePreference('payments', value)}
              disabled={isUpdating}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Events</Text>
              <Text style={styles.settingDescription}>
                Event reminders and updates
              </Text>
            </View>
            <Switch
              value={preferences?.events ?? true}
              onValueChange={(value) => handleTogglePreference('events', value)}
              disabled={isUpdating}
            />
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Marketing</Title>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Promotional</Text>
              <Text style={styles.settingDescription}>
                Special offers and new features
              </Text>
            </View>
            <Switch
              value={preferences?.marketing ?? false}
              onValueChange={(value) => handleTogglePreference('marketing', value)}
              disabled={isUpdating}
            />
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.card}>
        <Card.Content>
          <Title>Quiet Hours</Title>
          <Paragraph>
            Set times when you don't want to receive non-urgent notifications.
          </Paragraph>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Enable Quiet Hours</Text>
              <Text style={styles.settingDescription}>
                {preferences?.quietHours?.enabled 
                  ? `${preferences.quietHours.start} - ${preferences.quietHours.end}`
                  : 'Disabled'
                }
              </Text>
            </View>
            <Switch
              value={preferences?.quietHours?.enabled ?? false}
              onValueChange={(value) => updatePreferences({
                quietHours: {
                  enabled: value,
                  start: preferences?.quietHours?.start || '22:00',
                  end: preferences?.quietHours?.end || '08:00',
                }
              })}
              disabled={isUpdating}
            />
          </View>

          {preferences?.quietHours?.enabled && (
            <TouchableOpacity
              style={styles.timeButton}
              onPress={() => {
                // This would open a time picker
                showDialog({
                  type: 'info',
                  title: 'Time Picker',
                  message: 'Time picker would open here',
                  buttons: [
                    {
                      text: 'OK',
                      mode: 'contained',
                      style: 'primary',
                    },
                  ],
                  dismissible: true,
                });
              }}
            >
              <Text style={styles.timeButtonText}>
                Change Time: {preferences.quietHours.start} - {preferences.quietHours.end}
              </Text>
            </TouchableOpacity>
          )}
        </Card.Content>
      </Card>

      {onClose && (
        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.closeButton}
        >
          Close
        </Button>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  enableButton: {
    marginTop: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  divider: {
    marginVertical: 8,
  },
  timeButton: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    alignItems: 'center',
  },
  timeButtonText: {
    fontSize: 16,
    color: '#333',
  },
  closeButton: {
    marginTop: 16,
  },
});
