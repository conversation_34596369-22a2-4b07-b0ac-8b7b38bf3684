import React, { useState, useCallback, useRef } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { useTheme } from 'react-native-paper';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import type { Event } from '../types/event';
import SelectedEventCard from './SelectedEventCard';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface EventMapViewProps {
  events: (Event & { distance?: number })[];
  userLocation?: {
    latitude: number;
    longitude: number;
  } | null;
  onEventPress: (event: Event) => void;
  onBackToList: () => void;
  style?: any;
}

export default function EventMapView({
  events,
  userLocation,
  onEventPress,
  onBackToList,
  style
}: EventMapViewProps) {
  const theme = useTheme();
  const mapRef = useRef<MapView>(null);
  const [selectedEventIndex, setSelectedEventIndex] = useState(0);
  const [mapRegion, setMapRegion] = useState({
    latitude: userLocation?.latitude || 41.3851, // Barcelona default
    longitude: userLocation?.longitude || 2.1734,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  // Get the currently selected event
  const selectedEvent = events.length > 0 ? events[selectedEventIndex] : null;

  // Calculate map region to fit all events
  const calculateMapRegion = useCallback(() => {
    if (events.length === 0) return mapRegion;

    const latitudes = events.map(event => event.latitude);
    const longitudes = events.map(event => event.longitude);

    const minLat = Math.min(...latitudes);
    const maxLat = Math.max(...latitudes);
    const minLng = Math.min(...longitudes);
    const maxLng = Math.max(...longitudes);

    const latDelta = (maxLat - minLat) * 1.2; // Add 20% padding
    const lngDelta = (maxLng - minLng) * 1.2;

    return {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.max(latDelta, 0.01), // Minimum zoom level
      longitudeDelta: Math.max(lngDelta, 0.01),
    };
  }, [events, mapRegion]);

  const handleMarkerPress = useCallback((event: Event) => {
    const eventIndex = events.findIndex(e => e.id === event.id);
    if (eventIndex !== -1) {
      setSelectedEventIndex(eventIndex);
    }
  }, [events]);

  const handleEventCardPress = useCallback((event: Event) => {
    onEventPress(event);
  }, [onEventPress]);

  const handleMapPress = useCallback(() => {
    // Keep the current selection when map is pressed
  }, []);

  const handlePreviousEvent = useCallback(() => {
    if (events.length > 0) {
      setSelectedEventIndex(prev => prev > 0 ? prev - 1 : events.length - 1);
    }
  }, [events.length]);

  const handleNextEvent = useCallback(() => {
    if (events.length > 0) {
      setSelectedEventIndex(prev => prev < events.length - 1 ? prev + 1 : 0);
    }
  }, [events.length]);

  // Center map on selected event when index changes
  const centerMapOnSelectedEvent = useCallback(() => {
    if (selectedEvent && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: selectedEvent.latitude,
        longitude: selectedEvent.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  }, [selectedEvent]);

  // Effect to center map when selected event changes
  React.useEffect(() => {
    centerMapOnSelectedEvent();
  }, [centerMapOnSelectedEvent]);

  const formatPrice = (price?: number) => {
    return price ? `€${price}` : 'Free';
  };

  return (
    <View style={[styles.container, style]}>
      {/* Map Header */}
      {/* <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={4}>
        <IconButton
          icon="arrow-left"
          onPress={onBackToList}
          iconColor={theme.colors.onSurface}
        />
        <Text style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
          {events.length} Events on Map
        </Text>
        <View style={styles.headerRight}>
          {userLocation && (
            <IconButton
              icon="crosshairs-gps"
              onPress={() => {
                setMapRegion({
                  latitude: userLocation.latitude,
                  longitude: userLocation.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                });
              }}
              iconColor={theme.colors.primary}
            />
          )}
        </View>
      </Surface> */}

      {/* Map View */}
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={mapRegion}
        onRegionChangeComplete={setMapRegion}
        onPress={handleMapPress}
        showsUserLocation={!!userLocation}
        showsMyLocationButton={false}
        showsCompass={true}
        showsScale={true}
      >
        {/* User Location Marker */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.latitude,
              longitude: userLocation.longitude,
            }}
            title="Your Location"
            description="You are here"
            pinColor="blue"
          />
        )}

        {/* Event Markers */}
        {events.map((event, index) => (
          <Marker
            key={event.id}
            coordinate={{
              latitude: event.latitude,
              longitude: event.longitude,
            }}
            title={event.title}
            description={`${formatPrice(event.price_per_person)} • ${event.city}`}
            onPress={() => handleMarkerPress(event)}
            pinColor={index === selectedEventIndex ? theme.colors.primary : theme.colors.secondary}
          />
        ))}
      </MapView>

      {/* Selected Event Card - Always visible when there are events */}
      {selectedEvent && (
        <SelectedEventCard
          event={selectedEvent}
          currentIndex={selectedEventIndex}
          totalEvents={events.length}
          onPrevious={handlePreviousEvent}
          onNext={handleNextEvent}
          onViewDetails={handleEventCardPress}
        />
      )}

    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
    zIndex: 1,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginLeft: 8,
  },
  headerRight: {
    flexDirection: 'row',
  },
  map: {
    flex: 1,
  },
  eventsBadge: {
    position: 'absolute',
    top: 80,
    right: 16,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  eventsBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
