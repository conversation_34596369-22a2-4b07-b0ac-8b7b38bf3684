import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { 
  Text, 
  Chip, 
  Portal, 
  Modal, 
  List, 
  Checkbox, 
  Button,
  useTheme,
  Searchbar
} from 'react-native-paper';
import { Image } from 'react-native';

interface MultiSelectItem {
  id: string;
  label: string;
  value: string;
  icon?: any;
}

interface CustomMultiSelectProps {
  items: MultiSelectItem[];
  selectedItems: string[];
  onSelectedItemsChange: (items: string[]) => void;
  selectText: string;
  searchPlaceholder?: string;
  maxHeight?: number;
}

export const CustomMultiSelect: React.FC<CustomMultiSelectProps> = ({
  items,
  selectedItems,
  onSelectedItemsChange,
  selectText,
  searchPlaceholder = "Search...",
  maxHeight = 400,
}) => {
  const theme = useTheme();
  const [visible, setVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredItems = items.filter(item =>
    item.label.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleItemPress = (itemId: string) => {
    const isSelected = selectedItems.includes(itemId);
    if (isSelected) {
      onSelectedItemsChange(selectedItems.filter(id => id !== itemId));
    } else {
      onSelectedItemsChange([...selectedItems, itemId]);
    }
  };

  const handleConfirm = () => {
    setVisible(false);
    setSearchQuery('');
  };

  const selectedItemsData = items.filter(item => selectedItems.includes(item.id));

  return (
    <View style={styles.container}>
      {/* Selected Items Display */}
      <View style={styles.selectedContainer}>
        {selectedItemsData.length > 0 ? (
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.chipContainer}>
              {selectedItemsData.map((item) => (
                <Chip
                  key={item.id}
                  mode="outlined"
                  onClose={() => handleItemPress(item.id)}
                  style={styles.chip}
                  icon={() => item.icon ? (
                    <Image 
                      source={item.icon} 
                      style={styles.chipIcon} 
                      resizeMode="contain"
                    />
                  ) : undefined}
                >
                  {item.label}
                </Chip>
              ))}
            </View>
          </ScrollView>
        ) : (
          <Text style={[styles.placeholder, { color: theme.colors.onSurfaceVariant }]}>
            {selectText}
          </Text>
        )}
      </View>

      {/* Select Button */}
      <Button
        mode="outlined"
        onPress={() => setVisible(true)}
        style={styles.selectButton}
      >
        {selectedItems.length > 0 ? `Edit Selection (${selectedItems.length})` : selectText}
      </Button>

      {/* Modal */}
      <Portal>
        <Modal
          visible={visible}
          onDismiss={() => setVisible(false)}
          contentContainerStyle={[
            styles.modal,
            { backgroundColor: theme.colors.surface, maxHeight }
          ]}
        >
          <View style={styles.modalHeader}>
            <Text variant="headlineSmall" style={styles.modalTitle}>
              {selectText}
            </Text>
            <Button onPress={() => setVisible(false)}>Close</Button>
          </View>

          <Searchbar
            placeholder={searchPlaceholder}
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchbar}
          />

          <ScrollView style={styles.listContainer}>
            {filteredItems.map((item) => (
              <List.Item
                key={item.id}
                title={item.label}
                left={() => (
                  <View style={styles.listItemLeft}>
                    {item.icon && (
                      <Image 
                        source={item.icon} 
                        style={styles.listIcon} 
                        resizeMode="contain"
                      />
                    )}
                    <Checkbox
                      status={selectedItems.includes(item.id) ? 'checked' : 'unchecked'}
                      onPress={() => handleItemPress(item.id)}
                    />
                  </View>
                )}
                onPress={() => handleItemPress(item.id)}
                style={styles.listItem}
              />
            ))}
          </ScrollView>

          <View style={styles.modalFooter}>
            <Button
              mode="contained"
              onPress={handleConfirm}
              style={styles.confirmButton}
            >
              Confirm ({selectedItems.length} selected)
            </Button>
          </View>
        </Modal>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  selectedContainer: {
    minHeight: 50,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginRight: 8,
    marginBottom: 4,
  },
  chipIcon: {
    width: 16,
    height: 16,
  },
  placeholder: {
    fontSize: 16,
    textAlign: 'center',
    paddingVertical: 12,
  },
  selectButton: {
    marginTop: 8,
  },
  modal: {
    margin: 20,
    borderRadius: 8,
    padding: 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    flex: 1,
  },
  searchbar: {
    marginBottom: 16,
  },
  listContainer: {
    maxHeight: 300,
  },
  listItem: {
    paddingVertical: 4,
  },
  listItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  listIcon: {
    width: 24,
    height: 24,
  },
  modalFooter: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  confirmButton: {
    width: '100%',
  },
});
