import React from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View } from 'react-native';
import { Surface, Text, Button } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { router } from 'expo-router';
import moment from 'moment';
import type { AugmentedEventBooking } from '../types';
import { getStatusText, getStatusStyle, getPaymentStatusText, getPaymentStatusDisplay, isPaymentRequiredAndNotCompleted } from '../utils/statusUtils';
import log from '@/common/logger';
import { BookingStatus } from '../types/eventBooking';

interface RequestCardProps {
  augmentedBooking: AugmentedEventBooking;
  filterType: 'incoming' | 'sent' | 'approved' | 'all';
  onRespondToBooking: (bookingId: string, status: BookingStatus, responseMessage?: string) => Promise<void>;
  onAcceptBooking?: (augmentedBooking: AugmentedEventBooking) => Promise<void>;
  onRejectBooking?: (augmentedBooking: AugmentedEventBooking) => Promise<void>;
  onCancelBooking?: (augmentedBooking: AugmentedEventBooking) => Promise<void>;
  isResponding: boolean;
}

export default function RequestCard({ 
  augmentedBooking, 
  filterType, 
  onRespondToBooking, 
  onAcceptBooking,
  onRejectBooking,
  onCancelBooking,
  isResponding 
}: RequestCardProps) {
  const theme = useTheme();

  // Format date
  const formatBookingDate = (dateString: string): string => {
    const bookingDate = moment(dateString);
    const now = moment();
    
    if (bookingDate.isSame(now, 'day')) {
      return 'Today, ' + bookingDate.format('h:mm A');
    } else if (bookingDate.isSame(now.subtract(1, 'day'), 'day')) {
      return 'Yesterday, ' + bookingDate.format('h:mm A');
    } else {
      return bookingDate.fromNow();
    }
  };



  // Handle navigation to detailed view
  const handleCardPress = () => {
    // Navigate to booking details screen
    log.info('Navigating to booking details screen', {
      bookingId: augmentedBooking.event_booking?.id,
      eventId: augmentedBooking.event_offer?.id
    });
    router.push({
      pathname: '/booking-details',
      params: { 
        bookingId: augmentedBooking.event_booking?.id,
        eventId: augmentedBooking.event_offer?.id 
      }
    });
  };

  // Handle booking response
  const handleAccept = () => {
    if (onAcceptBooking) {
      onAcceptBooking(augmentedBooking);
    } else {
      // Fallback to legacy method
      onRespondToBooking(augmentedBooking.event_booking?.id, BookingStatus.ACCEPTED);
    }
  };

  const handleDecline = () => {
    if (onRejectBooking) {
      onRejectBooking(augmentedBooking);
    } else {
      // Fallback to legacy method
      onRespondToBooking(augmentedBooking.event_booking?.id, BookingStatus.REJECTED);
    }
  };

  const handleCancel = () => {
    if (onCancelBooking) {
      onCancelBooking(augmentedBooking);
    }
  };

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <Surface style={[styles.requestCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.requestTitle, { color: theme.colors.onSurface }]}>
          {augmentedBooking.event_offer?.title || 'Event Title'}
        </Text>
        
        {filterType === 'incoming' ? (
          <>
            <Text style={[styles.requestUser, { color: theme.colors.primary }]}>
              👤 Guest wants to join
            </Text>
            <Text style={[styles.requestDetails, { color: theme.colors.onSurface }]}>
              Booked {formatBookingDate(augmentedBooking.event_booking?.requestedAt)}
            </Text>
            <Text style={[styles.requestDetails, { color: theme.colors.onSurface }]}>
              Guests: {augmentedBooking.event_booking?.number_of_guests || 1}
            </Text>
            {augmentedBooking.event_booking?.message && (
              <Text style={[styles.requestMessage, { color: theme.colors.onSurface }]}>
                "{augmentedBooking.event_booking.message}"
              </Text>
            )}
            <ScrollView horizontal style={styles.buttonContainer}>
              <Button 
                mode="contained" 
                style={[styles.acceptButton, { backgroundColor: theme.colors.primary }]}
                labelStyle={{ color: '#FFFFFF' }}
                onPress={handleAccept}
                loading={isResponding}
                disabled={isResponding}
              >
                Accept
              </Button>
              <Button 
                mode="outlined" 
                style={styles.declineButton}
                labelStyle={{ color: theme.colors.error }}
                onPress={handleDecline}
                loading={isResponding}
                disabled={isResponding}
              >
                Decline
              </Button>
            </ScrollView>
          </>
        ) : (
          <>
            <Text style={[styles.requestUser, { color: '#4A90E2' }]}>
              📤 Booking sent to event host
            </Text>
            <Text style={[styles.requestDetails, { color: theme.colors.onSurface }]}>
              Sent {formatBookingDate(augmentedBooking.event_booking?.requestedAt)}
            </Text>
            <Text style={[styles.requestDetails, { color: theme.colors.onSurface }]}>
              Guests: {augmentedBooking.event_booking?.number_of_guests || 1}
            </Text>
            <Text style={[styles.requestStatus, getStatusStyle(augmentedBooking.event_booking?.booking_status, theme)]}>
              {getStatusText(augmentedBooking.event_booking?.booking_status)}
            </Text>
            
            {/* Payment Status Section */}
            {augmentedBooking.payment && (
              <View style={styles.paymentSection}>
                <Text style={[styles.paymentLabel, { color: theme.colors.onSurface }]}>
                  Payment Status:
                </Text>
                <Text style={[styles.paymentStatus, { color: getPaymentStatusDisplay(augmentedBooking.payment.payment_status, theme).color }]}>
                  {getPaymentStatusText(augmentedBooking.payment.payment_status)}
                </Text>
                {augmentedBooking.payment.payment_amount > 0 && (
                  <Text style={[styles.paymentAmount, { color: theme.colors.onSurface }]}>
                    Amount: {augmentedBooking.payment.currency} {augmentedBooking.payment.payment_amount / 100}
                  </Text>
                )}
              </View>
            )}
            
            {/* Payment Required Indicator */}
            {isPaymentRequiredAndNotCompleted(
              augmentedBooking.event_booking?.booking_status, 
              augmentedBooking.payment?.payment_status
            ) && (
              <View style={styles.paymentRequiredSection}>
                <Text style={[styles.paymentRequiredText, { color: theme.colors.error }]}>
                  ⚠️ Payment Required
                </Text>
              </View>
            )}

            {/* Cancel Button for Pending Bookings */}
            {augmentedBooking.event_booking?.booking_status === BookingStatus.PENDING_CONFIRMATION && onCancelBooking && (
              <View style={styles.cancelSection}>
                <Button 
                  mode="outlined" 
                  style={[styles.cancelButton, { borderColor: theme.colors.error }]}
                  labelStyle={{ color: theme.colors.error }}
                  onPress={handleCancel}
                  loading={isResponding}
                  disabled={isResponding}
                  icon="cancel"
                >
                  Cancel Request
                </Button>
              </View>
            )}
          </>
        )}
      </Surface>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  requestCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  requestTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  requestUser: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  requestDetails: {
    fontSize: 12,
    marginBottom: 8,
    opacity: 0.7,
  },
  requestMessage: {
    fontSize: 14,
    marginBottom: 12,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  requestStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  acceptButton: {
    marginRight: 8,
    borderRadius: 8,
  },
  declineButton: {
    borderRadius: 8,
    borderColor: '#B3261E',
  },
  paymentSection: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  paymentLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  paymentStatus: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  paymentAmount: {
    fontSize: 12,
    opacity: 0.8,
  },
  paymentRequiredSection: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#FF9800',
  },
  paymentRequiredText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  cancelSection: {
    marginTop: 12,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  cancelButton: {
    borderRadius: 8,
  },
});
