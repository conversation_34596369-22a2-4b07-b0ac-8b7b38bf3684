import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Image, Dimensions, ScrollView } from 'react-native';
import { 
  Surface, 
  Text, 
  Avatar, 
  Chip, 
  IconButton, 
  Badge,
  Button
} from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { getKitchenOptions } from '../types/kitchenOptions';
import { getBeverageOptions } from '../types/beverageOptions';
import { getLocationOptions } from '../types/locationOptions';
import { getEventOptions } from '../types/eventOptions';
import type { Event } from '../types/event';
import { useAuthStore } from '../stores/authStore';
import { showSuccessDialog, showErrorDialog } from '../stores/dialogStore';

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = screenWidth - 32; // Account for padding

// Helper function to get option data by id
const getOptionData = (id: string, options: any[]) => {
  const option = options.find(opt => opt.id === id);
  return option || { id, label: id, value: id, icon: null };
};

interface SearchEventCardProps {
  event: Event;
  distance?: number; // in kilometers
  onPress?: (event: Event) => void;
  onSave?: (event: Event) => void;
  onShare?: (event: Event) => void;
  onHostPress?: (hostId: string) => void;
  isSaved?: boolean;
  showDistance?: boolean;
  showFullDetails?: boolean;
  highlighted?: {
    title?: string;
    description?: string;
  };
}

export default function SearchEventCard({
  event,
  distance,
  onPress,
  onSave,
  onShare,
  onHostPress,
  isSaved = false,
  showDistance = true,
  showFullDetails = true,
  highlighted
}: SearchEventCardProps) {
  const theme = useTheme();
  const router = useRouter();
  const { user } = useAuthStore();
  
  // Check if current user is the host
  const isCurrentUserHost = user?.id === event.host_user_id;

  // Helper functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return `Today, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow, ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  };

  // const getAvailabilityProgress = () => {
  //   return event.currentParticipants / event.max_participants;
  // };

  // const getAvailabilityColor = () => {
  //   const progress = getAvailabilityProgress();
  //   if (progress >= 1) return theme.colors.error;
  //   if (progress >= 0.8) return theme.colors.tertiary;
  //   return theme.colors.primary;
  // };

  const spotsLeft = event.max_participants - event.currentParticipants;

  const handleCardPress = () => {
    if (onPress) {
      onPress(event);
    } else {
      router.push({
        pathname: '/event-details',
        params: { eventId: event.id }
      });
    }
  };

  const handleHostPress = () => {
    if (onHostPress) {
      onHostPress(event.host_user_id);
    } else {
      router.push({
        pathname: '/profile',
        params: { userId: event.host_user_id }
      });
    }
  };

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <Surface style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={2}>
        {/* Event Image */}
        {event.medias && event.medias.length > 0 && (
          <View style={styles.imageContainer}>
            {event.medias[0]?.type === 'image' ? (
              <Image 
                source={{ uri: event.medias[0].url }} 
                style={styles.eventImage}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.eventImage, { backgroundColor: theme.colors.surfaceVariant }]}>
                <MaterialCommunityIcons 
                  name="play-circle" 
                  size={64} 
                  color={theme.colors.primary} 
                />
                <Text style={{ color: theme.colors.onSurfaceVariant }}>Video</Text>
              </View>
            )}
            {/* Save button overlay */}
            <View style={styles.imageOverlay}>
              <IconButton
                icon={isSaved ? "heart" : "heart-outline"}
                iconColor={isSaved ? theme.colors.error : "white"}
                size={24}
                onPress={() => onSave?.(event)}
                style={[styles.saveButton, { backgroundColor: 'rgba(0,0,0,0.3)' }]}
              />
            </View>
            
            {/* Distance badge */}
            {showDistance && distance !== undefined && (
              <View style={styles.distanceBadge}>
                <Badge style={{ backgroundColor: theme.colors.primary }}>
                  {distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`}
                </Badge>
              </View>
            )}
          </View>
        )}

        <View style={styles.content}>
          {/* Event Title */}
          <Text 
            style={[styles.title, { color: theme.colors.onSurface }]}
            numberOfLines={2}
          >
            {highlighted?.title || event.title}
          </Text>

          {/* Host Information */}
          <TouchableOpacity onPress={handleHostPress} style={styles.hostContainer}>
            {event.host?.avatar?.length && event.host?.avatar?.length > 0 ? (
              <Avatar.Image 
                size={28} 
                source={{ uri: event.host?.avatar }}
                style={{ backgroundColor: theme.colors.primary }}
              />
            ) : (
              <Avatar.Text 
                size={28} 
                label={event.host?.username?.charAt(0)?.toUpperCase()}
                style={{ backgroundColor: theme.colors.primary }}
              />
            )}
            <View style={styles.hostInfo}>
              <Text style={[styles.hostName, { color: theme.colors.onSurface }]}>
                {event.host?.username}
              </Text>
              <Text style={[styles.hostLabel, { color: theme.colors.onSurface, opacity: 0.7 }]}>
                Host
              </Text>
            </View>
          </TouchableOpacity>

          {/* Event Details */}
          <View style={styles.detailsContainer}>
            {/* Date and Time */}
            <View style={styles.detailRow}>
              <MaterialCommunityIcons 
                name="calendar" 
                size={16} 
                color={theme.colors.primary} 
              />
              <Text style={[styles.detailText, { color: theme.colors.onSurface }]}>
                {formatDate(event.event_date)}
              </Text>
            </View>

            {/* Location */}
            <View style={styles.detailRow}>
              <MaterialCommunityIcons 
                name="map-marker" 
                size={16} 
                color={theme.colors.primary} 
              />
              <Text 
                style={[styles.detailText, { color: theme.colors.onSurface }]}
                numberOfLines={1}
              >
                {event.city}, {event.country}
              </Text>
            </View>

            {/* Duration */}
            {event.duration_minutes && (
              <View style={styles.detailRow}>
                <MaterialCommunityIcons 
                  name="clock-outline" 
                  size={16} 
                  color={theme.colors.primary} 
                />
                <Text style={[styles.detailText, { color: theme.colors.onSurface }]}>
                  {formatDuration(event.duration_minutes)}
                </Text>
              </View>
            )}

            {/* Price */}
            {event.price_per_person && (
              <View style={styles.detailRow}>
                <MaterialCommunityIcons 
                  name="currency-eur" 
                  size={16} 
                  color={theme.colors.primary} 
                />
                <Text style={[styles.detailText, { color: theme.colors.primary, fontWeight: '600' }]}>
                  €{((event.price_per_person || 0) / 100)}/person
                </Text>
              </View>
            )}
          </View>

          {/* Tags/Categories */}
          {showFullDetails && (event.type_kitchens || event.type_events || event.type_beverages || event.type_locations) && (
            <View style={styles.tagsContainer}>
              {event.type_kitchens?.slice(0, 2).map((kitchen, index) => {
                const optionData = getOptionData(kitchen, getKitchenOptions());
                return (
                  <Chip 
                    key={`kitchen-${index}`}
                    mode="outlined"
                    compact
                    style={styles.tag}
                    textStyle={styles.tagText}
                    icon={optionData.icon ? () => (
                      <Image 
                        source={optionData.icon} 
                        style={styles.chipIcon} 
                        resizeMode="contain"
                      />
                    ) : "chef-hat"}
                  >
                    {optionData.label}
                  </Chip>
                );
              })}
              {event.type_events?.slice(0, 1).map((eventType, index) => {
                const optionData = getOptionData(eventType, getEventOptions());
                return (
                  <Chip 
                    key={`event-${index}`}
                    mode="outlined"
                    compact
                    style={styles.tag}
                    textStyle={styles.tagText}
                    icon={optionData.icon ? () => (
                      <Image 
                        source={optionData.icon} 
                        style={styles.chipIcon} 
                        resizeMode="contain"
                      />
                    ) : "calendar-star"}
                  >
                    {optionData.label}
                  </Chip>
                );
              })}
              {event.type_beverages?.slice(0, 1).map((beverage, index) => {
                const optionData = getOptionData(beverage, getBeverageOptions());
                return (
                  <Chip 
                    key={`beverage-${index}`}
                    mode="outlined"
                    compact
                    style={styles.tag}
                    textStyle={styles.tagText}
                    icon={optionData.icon ? () => (
                      <Image 
                        source={optionData.icon} 
                        style={styles.chipIcon} 
                        resizeMode="contain"
                      />
                    ) : "glass-wine"}
                  >
                    {optionData.label}
                  </Chip>
                );
              })}
              {event.type_locations?.slice(0, 1).map((location, index) => {
                const optionData = getOptionData(location, getLocationOptions());
                return (
                  <Chip 
                    key={`location-${index}`}
                    mode="outlined"
                    compact
                    style={styles.tag}
                    textStyle={styles.tagText}
                    icon={optionData.icon ? () => (
                      <Image 
                        source={optionData.icon} 
                        style={styles.chipIcon} 
                        resizeMode="contain"
                      />
                    ) : "map-marker"}
                  >
                    {optionData.label}
                  </Chip>
                );
              })}
            </View>
          )}

          {/* Availability Progress */}
          {/* <View style={styles.availabilityContainer}>
            <View style={styles.availabilityHeader}>
              <Text style={[styles.availabilityText, { color: theme.colors.onSurface }]}>
                {spotsLeft > 0 ? `${spotsLeft} spots left` : 'Fully booked'}
              </Text>
              <Text style={[styles.participantsText, { color: theme.colors.onSurface, opacity: 0.7 }]}>
                {event.currentParticipants}/{event.max_participants}
              </Text>
            </View>
            <ProgressBar 
              progress={getAvailabilityProgress()}
              color={getAvailabilityColor()}
              style={styles.progressBar}
            />
          </View> */}

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <View style={styles.leftActions}>
              {spotsLeft > 0 && (
                <Text style={[styles.statusText, { color: theme.colors.primary }]}>
                  Available
                </Text>
              )}
              {spotsLeft === 0 && (
                <Text style={[styles.statusText, { color: theme.colors.error }]}>
                  Full
                </Text>
              )}
            </View>
            
            <View style={styles.rightActions}>
              <IconButton
                icon="share-variant"
                size={20}
                onPress={() => onShare?.(event)}
                iconColor={theme.colors.onSurface}
              />
            </View>
          </View>
        </View>
      </Surface>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
    width: cardWidth,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  eventImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  saveButton: {
    margin: 0,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 20,
  },
  distanceBadge: {
    position: 'absolute',
    top: 16,
    left: 16,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
    lineHeight: 22,
  },
  hostContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 6,
    paddingHorizontal: 10,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 10,
  },
  hostInfo: {
    marginLeft: 10,
    flex: 1,
  },
  hostName: {
    fontSize: 14,
    fontWeight: '600',
  },
  hostLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  detailsContainer: {
    marginBottom: 12,
    paddingVertical: 6,
    paddingHorizontal: 10,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: 10,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  detailText: {
    fontSize: 13,
    marginLeft: 8,
    flex: 1,
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
    opacity: 0.8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 6,
  },
  tag: {
    marginBottom: 2,
    height: 28,
    borderRadius: 14,
  },
  tagText: {
    fontSize: 11,
    fontWeight: '500',
  },
  chipIcon: {
    width: 14,
    height: 14,
  },
  availabilityContainer: {
    marginBottom: 16,
  },
  availabilityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  availabilityText: {
    fontSize: 14,
    fontWeight: '500',
  },
  participantsText: {
    fontSize: 12,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 6,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.08)',
  },
  leftActions: {
    flex: 1,
  },
  rightActions: {
    flexDirection: 'row',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '700',
  },
});
