import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { 
  Button, 
  Surface, 
  Text, 
  IconButton, 
  ActivityIndicator,
  Chip
} from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import * as Location from 'expo-location';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useSearchStore } from '../stores/searchStore';

interface LocationServiceProps {
  onLocationUpdate?: (location: { latitude: number; longitude: number; city?: string }) => void;
  showCurrentLocation?: boolean;
  style?: any;
}

export default function LocationService({ 
  onLocationUpdate, 
  showCurrentLocation = true,
  style 
}: LocationServiceProps) {
  const theme = useTheme();
  const { 
    userLocation, 
    locationPermissionGranted, 
    setUserLocation, 
    setLocationPermission 
  } = useSearchStore();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check location permission on mount
  useEffect(() => {
    checkLocationPermission();
  }, []);

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      const granted = status === 'granted';
      setLocationPermission(granted);
      
      if (granted && !userLocation) {
        // If we have permission but no location, get current location
        getCurrentLocation();
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
      setError('Failed to check location permission');
    }
  };

  const requestLocationPermission = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setLocationPermission(granted);

      if (granted) {
        await getCurrentLocation();
      } else {
        setError('Location permission denied');
        Alert.alert(
          'Location Permission Required',
          'To find events near you, please enable location access in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Location.requestForegroundPermissionsAsync() }
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setError('Failed to request location permission');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check if location services are enabled
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        Alert.alert(
          'Location Services Disabled',
          'Please enable location services in your device settings to find events near you.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get current position with high accuracy
      const { coords } = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // Use cached location if less than 10 seconds old
      });

      const { latitude, longitude } = coords;

      // Reverse geocode to get city name
      let city: string | undefined;
      try {
        const [address] = await Location.reverseGeocodeAsync({
          latitude,
          longitude,
        });
        city = address?.city || address?.subregion || address?.region;
      } catch (geocodeError) {
        console.warn('Reverse geocoding failed:', geocodeError);
        // Continue without city name
      }

      const locationData = { latitude, longitude, city };
      
      // Update store
      setUserLocation(locationData);
      
      // Notify parent component
      onLocationUpdate?.(locationData);

    } catch (error) {
      console.error('Error getting current location:', error);
      let errorMessage = 'Failed to get your location';
      
      if (error instanceof Error) {
        if (error.message.includes('timeout')) {
          errorMessage = 'Location request timed out. Please try again.';
        } else if (error.message.includes('denied')) {
          errorMessage = 'Location access was denied.';
        }
      }
      
      setError(errorMessage);
      Alert.alert('Location Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLocation = () => {
    setUserLocation(null);
    setError(null);
    onLocationUpdate?.(null as any);
  };

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    }
    return `${(meters / 1000).toFixed(1)}km`;
  };

  if (!showCurrentLocation) {
    return null;
  }

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surface }, style]}>
      {/* Current Location Display */}
      {userLocation && (
        <View style={styles.locationInfo}>
          <MaterialCommunityIcons 
            name="map-marker" 
            size={20} 
            color={theme.colors.primary} 
          />
          <Text style={[styles.locationText, { color: theme.colors.onSurface }]}>
            {userLocation.city || 'Current Location'}
          </Text>
          <IconButton
            icon="close"
            size={16}
            onPress={clearLocation}
            iconColor={theme.colors.onSurface}
            style={styles.clearButton}
          />
        </View>
      )}

      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons 
            name="alert-circle" 
            size={16} 
            color={theme.colors.error} 
          />
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {error}
          </Text>
        </View>
      )}

      {/* Location Actions */}
      <View style={styles.actions}>
        {!locationPermissionGranted ? (
          <Button
            mode="outlined"
            onPress={requestLocationPermission}
            disabled={isLoading}
            icon="map-marker"
            style={styles.button}
          >
            Enable Location
          </Button>
        ) : !userLocation ? (
          <Button
            mode="outlined"
            onPress={getCurrentLocation}
            disabled={isLoading}
            icon={isLoading ? undefined : "crosshairs-gps"}
            style={styles.button}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={theme.colors.primary} />
            ) : (
              'Get Current Location'
            )}
          </Button>
        ) : (
          <Button
            mode="text"
            onPress={getCurrentLocation}
            disabled={isLoading}
            icon={isLoading ? undefined : "refresh"}
            textColor={theme.colors.primary}
            compact
          >
            {isLoading ? (
              <ActivityIndicator size="small" color={theme.colors.primary} />
            ) : (
              'Update Location'
            )}
          </Button>
        )}
      </View>
    </Surface>
  );
}

// Utility hook for calculating distance between two points
export const useDistanceCalculation = () => {
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
  };

  const formatDistance = (distanceKm: number): string => {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m`;
    }
    return `${distanceKm.toFixed(1)}km`;
  };

  return { calculateDistance, formatDistance };
};

// Location permission checker component
export const LocationPermissionChecker: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ children, fallback }) => {
  const { locationPermissionGranted } = useSearchStore();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        const { status } = await Location.getForegroundPermissionsAsync();
        // This will be handled by the store
      } finally {
        setIsChecking(false);
      }
    };

    checkPermission();
  }, []);

  if (isChecking) {
    return <ActivityIndicator size="small" />;
  }

  if (!locationPermissionGranted && fallback) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    borderRadius: 8,
    marginVertical: 4,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  clearButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  errorText: {
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  button: {
    minWidth: 120,
  },
});
