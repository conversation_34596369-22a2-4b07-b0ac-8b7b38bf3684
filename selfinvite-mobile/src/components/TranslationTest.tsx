import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useI18n } from '../hooks';
import { getKitchenOptions } from '../types/kitchenOptions';
import { getLocationOptions } from '../types/locationOptions';
import { getEventOptions } from '../types/eventOptions';
import { getBeverageOptions } from '../types/beverageOptions';
import { getIntoleranceOptions } from '../types/intoleranceOptions';

/**
 * Test component to verify i18n translations work correctly
 * This component can be temporarily added to any screen for testing
 */
export const TranslationTest: React.FC = () => {
  const { t, currentLocale } = useI18n();
  const kitchenOptions = getKitchenOptions();
  const locationOptions = getLocationOptions();
  const eventOptions = getEventOptions();
  const beverageOptions = getBeverageOptions();
  const intoleranceOptions = getIntoleranceOptions();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Translation Test</Text>
      <Text style={styles.subtitle}>Current Locale: {currentLocale}</Text>
      
      <Text style={styles.sectionTitle}>Sample Translations:</Text>
      <Text>Home: {t('__home')}</Text>
      <Text>Search: {t('__search')}</Text>
      <Text>Events: {t('__events')}</Text>
      
      <Text style={styles.sectionTitle}>Kitchen Options (first 3):</Text>
      {kitchenOptions.slice(0, 3).map((option) => (
        <Text key={option.id} style={styles.option}>
          {option.label}
        </Text>
      ))}
      
      <Text style={styles.sectionTitle}>Location Options (first 3):</Text>
      {locationOptions.slice(0, 3).map((option) => (
        <Text key={option.id} style={styles.option}>
          {option.label}
        </Text>
      ))}
      
      <Text style={styles.sectionTitle}>Event Options (first 3):</Text>
      {eventOptions.slice(0, 3).map((option) => (
        <Text key={option.id} style={styles.option}>
          {option.label}
        </Text>
      ))}
      
      <Text style={styles.sectionTitle}>Beverage Options (first 3):</Text>
      {beverageOptions.slice(0, 3).map((option) => (
        <Text key={option.id} style={styles.option}>
          {option.label}
        </Text>
      ))}
      
      <Text style={styles.sectionTitle}>Intolerance Options (first 3):</Text>
      {intoleranceOptions.slice(0, 3).map((option) => (
        <Text key={option.id} style={styles.option}>
          {option.label}
        </Text>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#f5f5f5',
    margin: 16,
    borderRadius: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  option: {
    fontSize: 14,
    marginBottom: 4,
    paddingLeft: 8,
  },
});

export default TranslationTest;
