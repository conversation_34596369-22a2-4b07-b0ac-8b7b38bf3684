import React from 'react';
import { StatusBar, Platform } from 'react-native';
import { useTheme } from 'react-native-paper';
import { useFocusEffect } from '@react-navigation/native';

interface AppStatusBarProps {
  backgroundColor?: string;
  barStyle?: 'default' | 'light-content' | 'dark-content';
  translucent?: boolean;
  animated?: boolean;
}

/**
 * AppStatusBar component that provides consistent status bar styling
 * across the entire app with support for theme-aware colors
 */
export const AppStatusBar: React.FC<AppStatusBarProps> = ({
  backgroundColor,
  barStyle,
  translucent = false,
  animated = true,
}) => {
  const theme = useTheme();

  // Use primary color as default background if not specified
  const statusBarBg = backgroundColor || theme.colors.primary;
  
  // Auto-determine bar style based on background color if not specified
  const statusBarStyle = barStyle || (theme.dark ? 'light-content' : 'light-content');

  // Update status bar when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      StatusBar.setBarStyle(statusBarStyle, animated);
      
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor(statusBarBg, animated);
        StatusBar.setTranslucent(translucent);
      }
    }, [statusBarBg, statusBarStyle, translucent, animated])
  );

  return (
    <StatusBar
      barStyle={statusBarStyle}
      backgroundColor={statusBarBg}
      translucent={translucent}
      animated={animated}
    />
  );
};

/**
 * Primary-colored status bar for main app screens
 */
export const PrimaryStatusBar: React.FC<Omit<AppStatusBarProps, 'backgroundColor' | 'barStyle'>> = (props) => {
  const theme = useTheme();
  
  return (
    <AppStatusBar
      backgroundColor={theme.colors.primary}
      barStyle="light-content"
      {...props}
    />
  );
};

/**
 * Surface-colored status bar for modal/secondary screens
 */
export const SurfaceStatusBar: React.FC<Omit<AppStatusBarProps, 'backgroundColor' | 'barStyle'>> = (props) => {
  const theme = useTheme();
  
  return (
    <AppStatusBar
      backgroundColor={theme.colors.surface}
      barStyle={theme.dark ? 'light-content' : 'dark-content'}
      {...props}
    />
  );
};

/**
 * Transparent status bar for full-screen content
 */
export const TransparentStatusBar: React.FC<Omit<AppStatusBarProps, 'backgroundColor' | 'translucent'>> = (props) => {
  return (
    <AppStatusBar
      backgroundColor="transparent"
      translucent={true}
      {...props}
    />
  );
};

export default AppStatusBar;
