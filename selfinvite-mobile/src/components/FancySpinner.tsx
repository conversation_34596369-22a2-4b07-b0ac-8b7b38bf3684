import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useTheme } from 'react-native-paper';

interface FancySpinnerProps {
  size?: number;
  color?: string;
  style?: any;
}

// 1. Pulsing Dots Spinner
export const PulsingDotsSpinner: React.FC<FancySpinnerProps> = ({ 
  size = 40, 
  color, 
  style 
}) => {
  const theme = useTheme();
  const animatedValues = useRef([
    new Animated.Value(0.3),
    new Animated.Value(0.3),
    new Animated.Value(0.3),
  ]).current;

  useEffect(() => {
    const createAnimation = (index: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValues[index], {
            toValue: 1,
            duration: 600,
            delay: index * 200,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
          Animated.timing(animatedValues[index], {
            toValue: 0.3,
            duration: 600,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
        ])
      );
    };

    const animations = animatedValues.map((_, index) => createAnimation(index));
    Animated.parallel(animations).start();
  }, []);

  const dotColor = color || theme.colors.primary;

  return (
    <View style={[styles.container, style]}>
      {animatedValues.map((animatedValue, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              width: size / 3,
              height: size / 3,
              backgroundColor: dotColor,
              opacity: animatedValue,
              transform: [
                {
                  scale: animatedValue.interpolate({
                    inputRange: [0.3, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// 2. Rotating Ring Spinner
export const RotatingRingSpinner: React.FC<FancySpinnerProps> = ({ 
  size = 40, 
  color, 
  style 
}) => {
  const theme = useTheme();
  const rotateValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  const rotation = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const ringColor = color || theme.colors.primary;

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.ring,
          {
            width: size,
            height: size,
            borderColor: ringColor,
            borderTopColor: 'transparent',
            transform: [{ rotate: rotation }],
          },
        ]}
      />
    </View>
  );
};

// 3. Bouncing Balls Spinner
export const BouncingBallsSpinner: React.FC<FancySpinnerProps> = ({ 
  size = 40, 
  color, 
  style 
}) => {
  const theme = useTheme();
  const animatedValues = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  useEffect(() => {
    const createBounceAnimation = (index: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValues[index], {
            toValue: -size / 2,
            duration: 400,
            delay: index * 150,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
          Animated.timing(animatedValues[index], {
            toValue: 0,
            duration: 400,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
        ])
      );
    };

    const animations = animatedValues.map((_, index) => createBounceAnimation(index));
    Animated.parallel(animations).start();
  }, [size]);

  const ballColor = color || theme.colors.primary;

  return (
    <View style={[styles.container, style]}>
      {animatedValues.map((animatedValue, index) => (
        <Animated.View
          key={index}
          style={[
            styles.ball,
            {
              width: size / 4,
              height: size / 4,
              backgroundColor: ballColor,
              transform: [{ translateY: animatedValue }],
            },
          ]}
        />
      ))}
    </View>
  );
};

// 4. Wave Spinner
export const WaveSpinner: React.FC<FancySpinnerProps> = ({ 
  size = 40, 
  color, 
  style 
}) => {
  const theme = useTheme();
  const animatedValues = useRef([
    new Animated.Value(0.3),
    new Animated.Value(0.3),
    new Animated.Value(0.3),
    new Animated.Value(0.3),
    new Animated.Value(0.3),
  ]).current;

  useEffect(() => {
    const createWaveAnimation = (index: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValues[index], {
            toValue: 1,
            duration: 400,
            delay: index * 100,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
          Animated.timing(animatedValues[index], {
            toValue: 0.3,
            duration: 400,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
        ])
      );
    };

    const animations = animatedValues.map((_, index) => createWaveAnimation(index));
    Animated.parallel(animations).start();
  }, []);

  const waveColor = color || theme.colors.primary;

  return (
    <View style={[styles.container, style]}>
      {animatedValues.map((animatedValue, index) => (
        <Animated.View
          key={index}
          style={[
            styles.waveBar,
            {
              width: size / 8,
              height: size,
              backgroundColor: waveColor,
              opacity: animatedValue,
              transform: [
                {
                  scaleY: animatedValue.interpolate({
                    inputRange: [0.3, 1],
                    outputRange: [0.5, 1.5],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// 5. Spinning Dots Spinner
export const SpinningDotsSpinner: React.FC<FancySpinnerProps> = ({ 
  size = 40, 
  color, 
  style 
}) => {
  const theme = useTheme();
  const rotateValue = useRef(new Animated.Value(0)).current;
  const scaleValues = useRef([
    new Animated.Value(0.5),
    new Animated.Value(0.5),
    new Animated.Value(0.5),
  ]).current;

  useEffect(() => {
    // Rotation animation
    Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 1200,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();

    // Scale animation for dots
    const createScaleAnimation = (index: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.timing(scaleValues[index], {
            toValue: 1,
            duration: 400,
            delay: index * 200,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
          Animated.timing(scaleValues[index], {
            toValue: 0.5,
            duration: 400,
            easing: Easing.bezier(0.4, 0.0, 0.2, 1),
            useNativeDriver: true,
          }),
        ])
      );
    };

    const scaleAnimations = scaleValues.map((_, index) => createScaleAnimation(index));
    Animated.parallel(scaleAnimations).start();
  }, []);

  const rotation = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const dotColor = color || theme.colors.primary;

  return (
    <View style={[styles.container, style]}>
      <Animated.View
        style={[
          styles.spinningContainer,
          {
            width: size,
            height: size,
            transform: [{ rotate: rotation }],
          },
        ]}
      >
        {scaleValues.map((scaleValue, index) => {
          const angle = (index * 120) * (Math.PI / 180);
          const radius = size / 3;
          const x = Math.cos(angle) * radius;
          const y = Math.sin(angle) * radius;

          return (
            <Animated.View
              key={index}
              style={[
                styles.spinningDot,
                {
                  width: size / 6,
                  height: size / 6,
                  backgroundColor: dotColor,
                  left: size / 2 + x - size / 12,
                  top: size / 2 + y - size / 12,
                  transform: [{ scale: scaleValue }],
                },
              ]}
            />
          );
        })}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: 2,
  },
  ring: {
    borderRadius: 50,
    borderWidth: 3,
  },
  ball: {
    borderRadius: 50,
    marginHorizontal: 2,
  },
  waveBar: {
    borderRadius: 2,
    marginHorizontal: 1,
  },
  spinningContainer: {
    position: 'relative',
  },
  spinningDot: {
    position: 'absolute',
    borderRadius: 50,
  },
});
