import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text, Chip, Divider, useTheme, IconButton } from 'react-native-paper';
import { getCurrentEnv, getCurrentConfig } from '../utils/env';
import * as Clipboard from 'expo-clipboard';

interface ExpoEnvironmentSwitcherProps {
  style?: any;
}

export function ExpoEnvironmentSwitcher({ style }: ExpoEnvironmentSwitcherProps) {
  const theme = useTheme();
  
  // Get current environment from Constants
  const currentEnv = getCurrentEnv();
  const currentConfig = getCurrentConfig();
  const [copiedKey, setCopiedKey] = useState<string | null>(null);

  const handleCopy = (value: any, key: string) => {
    const valueToCopy = typeof value === 'boolean' ? value.toString() : value || '';
    Clipboard.setStringAsync(valueToCopy);
    setCopiedKey(key);
    setTimeout(() => setCopied<PERSON>ey(null), 2000); // Reset icon after 2 seconds
  };
  
  // Environment display info
  const getEnvInfo = (env: string) => {
    switch (env) {
      case 'development':
        return { label: 'Development', color: '#4CAF50', description: 'Local development environment' };
      case 'preview':
        return { label: 'Preview', color: '#FF9800', description: 'Preview/testing environment' };
      case 'production':
        return { label: 'Production', color: '#F44336', description: 'Live production environment' };
      default:
        return { label: env, color: '#9E9E9E', description: 'Unknown environment' };
    }
  };
  
  const envInfo = getEnvInfo(currentEnv);

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surfaceVariant }, style]}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Expo Configuration
      </Text>
      
      <View style={styles.currentEnvContainer}>
        <Text style={[styles.currentEnvLabel, { color: theme.colors.onSurface }]}>
          Current Environment:
        </Text>
        <Chip 
          mode="outlined" 
          style={[styles.currentEnvChip, { borderColor: envInfo.color }]}
          textStyle={{ color: envInfo.color }}
        >
          {envInfo.label}
        </Chip>
      </View>

      <Text style={[styles.description, { color: theme.colors.onSurface }]}>
        {envInfo.description}
      </Text>

      <Divider style={styles.divider} />

      <View style={styles.configInfo}>
        <Text style={[styles.configTitle, { color: theme.colors.onSurface }]}>
          Current Configuration:
        </Text>
        
        {Object.entries(currentConfig).map(([key, value]) => {
          const displayValue = (val: any) => {
            if (typeof val === 'string' && val.length > 30) {
              return `${val.substring(val.length - 30, val.length - 1)}...`;
            }
            if (typeof val === 'boolean') {
              return val.toString();
            }
            return val || 'Not set';
          };

          const formatKey = (k: string) => {
            const result = k.replace(/([A-Z])/g, ' $1');
            return (result.charAt(0).toUpperCase() + result.slice(1)).replace(/_/g, ' ');
          };

          return (
            <View style={styles.configRow} key={key}>
              <Text style={[styles.configLabel, { color: theme.colors.onSurface }]} numberOfLines={1}>
                {formatKey(key)}:
              </Text>
              <View style={styles.valueContainer}>
                <Text style={[styles.configValue, { color: theme.colors.onSurface }]} numberOfLines={1}>
                  {displayValue(value)}
                </Text>
                <IconButton
                  icon={copiedKey === key ? 'check' : 'content-copy'}
                  size={16}
                  onPress={() => handleCopy(value, key)}
                  style={styles.copyButton}
                  iconColor={copiedKey === key ? theme.colors.primary : theme.colors.onSurfaceVariant}
                />
              </View>
            </View>
          );
        })}
      </View>

      <View style={styles.infoSection}>
        <Text style={[styles.infoTitle, { color: theme.colors.onSurface }]}>
          How it works:
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Configuration is defined in app.config.js
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Environment-specific configs are loaded at build time
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Current environment: {currentEnv}
        </Text>
        <Text style={[styles.infoText, { color: theme.colors.onSurface }]}>
          • Changes require a new build to take effect
        </Text>
      </View>
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  currentEnvContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentEnvLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  currentEnvChip: {
    marginLeft: 8,
  },
  description: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  configInfo: {
    marginBottom: 16,
  },
  configTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  configRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  configLabel: {
    fontSize: 12,
    fontWeight: '500',
    flex: 0.45,
  },
  valueContainer: {
    flex: 0.55,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  configValue: {
    fontSize: 11,
    opacity: 0.8,
    textAlign: 'right',
    flexShrink: 1,
  },
  copyButton: {
    marginLeft: 4,
    marginVertical: -4,
  },
  infoSection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 11,
    opacity: 0.7,
    marginBottom: 4,
  },
});

export default ExpoEnvironmentSwitcher;
