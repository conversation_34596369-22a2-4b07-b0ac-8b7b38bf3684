import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text, IconButton, Chip, MD3Elevation } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import type { Event } from '../types/event';

interface SelectedEventCardProps {
  event: Event & { distance?: number };
  currentIndex: number;
  totalEvents: number;
  onPrevious: () => void;
  onNext: () => void;
  onViewDetails: (event: Event) => void;
  style?: any;
}

export default function SelectedEventCard({
  event,
  currentIndex,
  totalEvents,
  onPrevious,
  onNext,
  onViewDetails,
  style
}: SelectedEventCardProps) {
  const theme = useTheme();

  const formatDistance = (distance?: number) => {
    if (!distance) return '';
    if (distance < 1) {
      return `${Math.round(distance * 1000)}m`;
    }
    return `${distance.toFixed(1)}km`;
  };

  const formatPrice = (price?: number) => {
    return price ? `€${price}` : 'Free';
  };

  return (
    <Surface style={[styles.eventCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.outline }, style]} elevation={12 as MD3Elevation}>
      {/* Header with title and counter */}
      <View style={styles.eventCardHeader}>
        <View style={styles.eventCardTitleContainer}>
          <Text style={[styles.eventTitle, { color: theme.colors.onSurface }]} numberOfLines={2}>
            {event.title}
          </Text>
          <View style={styles.eventCounterContainer}>
            <Chip 
              mode="outlined" 
              compact 
              style={[
                styles.eventCounterChip, 
                { 
                  borderColor: theme.colors.outline,
                  backgroundColor: theme.colors.surfaceVariant,
                  borderWidth: 1,
                }
              ]}
              textStyle={[
                styles.eventCounterText, 
                { 
                  color: theme.colors.onSurfaceVariant,
                  fontWeight: '600',
                }
              ]}
            >
              {currentIndex + 1} of {totalEvents}
            </Chip>
          </View>
        </View>
      </View>
      
      {/* Event details in a more compact layout */}
      <View style={styles.eventDetails}>
        <View style={styles.eventDetailsRow}>
          <View style={styles.eventDetailItem}>
            <MaterialCommunityIcons 
              name="map-marker" 
              size={14} 
              color={theme.colors.primary} 
            />
            <Text style={[styles.eventDetailText, { color: theme.colors.onSurface }]} numberOfLines={1}>
              {event.city}, {event.country}
            </Text>
          </View>
          
          <View style={styles.eventDetailItem}>
            <MaterialCommunityIcons 
              name="calendar" 
              size={14} 
              color={theme.colors.primary} 
            />
            <Text style={[styles.eventDetailText, { color: theme.colors.onSurface }]} numberOfLines={1}>
              {new Date(event.event_date).toLocaleDateString()}
            </Text>
          </View>
        </View>
        
        <View style={styles.eventDetailsRow}>
          <View style={styles.eventDetailItem}>
            <MaterialCommunityIcons 
              name="currency-eur" 
              size={14} 
              color={theme.colors.primary} 
            />
            <Text style={[styles.eventDetailText, { color: theme.colors.primary, fontWeight: '600' }]} numberOfLines={1}>
              {formatPrice(event.price_per_person)}/person
            </Text>
          </View>
          
          {event.distance && (
            <View style={styles.eventDetailItem}>
              <MaterialCommunityIcons 
                name="map-marker-distance" 
                size={14} 
                color={theme.colors.primary} 
              />
              <Text style={[styles.eventDetailText, { color: theme.colors.onSurface }]} numberOfLines={1}>
                {formatDistance(event.distance)} away
              </Text>
            </View>
          )}
        </View>
      </View>
      
      {/* Actions with improved layout */}
      <View style={styles.eventCardActions}>
        <View style={styles.navigationControls}>
          <IconButton
            icon="chevron-left"
            size={20}
            onPress={onPrevious}
            iconColor={theme.colors.primary}
            style={[styles.navButton, { backgroundColor: theme.colors.surfaceVariant }]}
          />
          <IconButton
            icon="chevron-right"
            size={20}
            onPress={onNext}
            iconColor={theme.colors.primary}
            style={[styles.navButton, { backgroundColor: theme.colors.surfaceVariant }]}
          />
        </View>
        <Chip
          mode="outlined"
          onPress={() => onViewDetails(event)}
          style={[styles.viewEventChip, { backgroundColor: theme.colors.primary, borderColor: theme.colors.primary }]}
          textStyle={{ color: theme.colors.onPrimary }}
        >
          View Details
        </Chip>
      </View>
    </Surface>
  );
}

const styles = StyleSheet.create({
  eventCard: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    borderRadius: 16,
    padding: 16,
    maxHeight: 180,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 12,
  },
  eventCardHeader: {
    marginBottom: 12,
  },
  eventCardTitleContainer: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    lineHeight: 22,
  },
  eventCounterContainer: {
    alignSelf: 'flex-start',
  },
  eventCounterChip: {
    height: 28,
    minWidth: 60,
  },
  eventCounterText: {
    fontSize: 12,
    fontWeight: '600',
  },
  eventDetails: {
    marginBottom: 16,
  },
  eventDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  eventDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  eventDetailText: {
    fontSize: 13,
    marginLeft: 6,
    flex: 1,
  },
  eventCardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  navigationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  navButton: {
    margin: 0,
    width: 32,
    height: 32,
  },
  viewEventChip: {
    height: 36,
    borderRadius: 18,
  },
});
