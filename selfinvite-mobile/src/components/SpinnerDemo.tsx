import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Surface, Text, Button, Card } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import {
  PulsingDotsSpinner,
  RotatingRingSpinner,
  BouncingBallsSpinner,
  WaveSpinner,
  SpinningDotsSpinner,
} from './FancySpinner';

type SpinnerType = 'pulsing' | 'ring' | 'bouncing' | 'wave' | 'spinning';

export default function SpinnerDemo() {
  const theme = useTheme();
  const [selectedSpinner, setSelectedSpinner] = useState<SpinnerType>('pulsing');
  const [isLoading, setIsLoading] = useState(false);

  const spinnerOptions = [
    { type: 'pulsing' as const, name: 'Pulsing Dots', description: 'Three dots that pulse in sequence' },
    { type: 'ring' as const, name: 'Rotating Ring', description: 'A smooth rotating ring animation' },
    { type: 'bouncing' as const, name: 'Bouncing Balls', description: 'Three balls bouncing up and down' },
    { type: 'wave' as const, name: 'Wave Bars', description: 'Five bars creating a wave effect' },
    { type: 'spinning' as const, name: 'Spinning Dots', description: 'Three dots rotating in a circle' },
  ];

  const renderSpinner = () => {
    const spinnerProps = { size: 60, color: theme.colors.primary };
    
    switch (selectedSpinner) {
      case 'pulsing':
        return <PulsingDotsSpinner {...spinnerProps} />;
      case 'ring':
        return <RotatingRingSpinner {...spinnerProps} />;
      case 'bouncing':
        return <BouncingBallsSpinner {...spinnerProps} />;
      case 'wave':
        return <WaveSpinner {...spinnerProps} />;
      case 'spinning':
        return <SpinningDotsSpinner {...spinnerProps} />;
      default:
        return <PulsingDotsSpinner {...spinnerProps} />;
    }
  };

  const simulateLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.title, { color: theme.colors.onSurface }]}>
          Fancy Spinner Demo
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.onSurface }]}>
          Choose your favorite loading animation
        </Text>
      </Surface>

      {/* Live Preview */}
      <Card style={[styles.previewCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text style={[styles.previewTitle, { color: theme.colors.onSurface }]}>
            Live Preview
          </Text>
          <View style={styles.previewContainer}>
            {renderSpinner()}
          </View>
          <Text style={[styles.previewDescription, { color: theme.colors.onSurface }]}>
            {spinnerOptions.find(opt => opt.type === selectedSpinner)?.description}
          </Text>
        </Card.Content>
      </Card>

      {/* Spinner Options */}
      <Surface style={[styles.optionsSection, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Choose Spinner Type
        </Text>
        
        {spinnerOptions.map((option) => (
          <TouchableOpacity
            key={option.type}
            onPress={() => setSelectedSpinner(option.type)}
            style={[
              styles.optionItem,
              {
                backgroundColor: selectedSpinner === option.type 
                  ? theme.colors.primaryContainer 
                  : theme.colors.surface,
                borderColor: selectedSpinner === option.type 
                  ? theme.colors.primary 
                  : theme.colors.outline,
              }
            ]}
          >
            <View style={styles.optionContent}>
              <Text style={[
                styles.optionName,
                { 
                  color: selectedSpinner === option.type 
                    ? theme.colors.onPrimaryContainer 
                    : theme.colors.onSurface 
                }
              ]}>
                {option.name}
              </Text>
              <Text style={[
                styles.optionDescription,
                { 
                  color: selectedSpinner === option.type 
                    ? theme.colors.onPrimaryContainer 
                    : theme.colors.onSurface,
                  opacity: 0.7
                }
              ]}>
                {option.description}
              </Text>
            </View>
            <View style={styles.optionPreview}>
              {(() => {
                const spinnerProps = { size: 30, color: theme.colors.primary };
                switch (option.type) {
                  case 'pulsing':
                    return <PulsingDotsSpinner {...spinnerProps} />;
                  case 'ring':
                    return <RotatingRingSpinner {...spinnerProps} />;
                  case 'bouncing':
                    return <BouncingBallsSpinner {...spinnerProps} />;
                  case 'wave':
                    return <WaveSpinner {...spinnerProps} />;
                  case 'spinning':
                    return <SpinningDotsSpinner {...spinnerProps} />;
                  default:
                    return null;
                }
              })()}
            </View>
          </TouchableOpacity>
        ))}
      </Surface>

      {/* Test Loading State */}
      <Card style={[styles.testCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text style={[styles.testTitle, { color: theme.colors.onSurface }]}>
            Test Loading State
          </Text>
          <Text style={[styles.testDescription, { color: theme.colors.onSurface }]}>
            Simulate a 3-second loading experience
          </Text>
          <Button
            mode="contained"
            onPress={simulateLoading}
            disabled={isLoading}
            style={styles.testButton}
          >
            {isLoading ? 'Loading...' : 'Start Loading Test'}
          </Button>
          
          {isLoading && (
            <View style={styles.loadingTestContainer}>
              <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
                Loading your events...
              </Text>
              {renderSpinner()}
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Usage Instructions */}
      <Card style={[styles.usageCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <Text style={[styles.usageTitle, { color: theme.colors.onSurface }]}>
            How to Use
          </Text>
          <Text style={[styles.usageText, { color: theme.colors.onSurface }]}>
            1. Import the spinner component you want{'\n'}
            2. Use it in your loading states{'\n'}
            3. Customize size and color props{'\n'}
            4. Replace ActivityIndicator with your chosen spinner
          </Text>
          
          <Text style={[styles.codeExample, { color: theme.colors.primary }]}>
            {`<${selectedSpinner === 'pulsing' ? 'PulsingDotsSpinner' : 
              selectedSpinner === 'ring' ? 'RotatingRingSpinner' :
              selectedSpinner === 'bouncing' ? 'BouncingBallsSpinner' :
              selectedSpinner === 'wave' ? 'WaveSpinner' :
              'SpinningDotsSpinner'} 
  size={60} 
  color={theme.colors.primary} 
/>`}
          </Text>
        </Card.Content>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  previewCard: {
    margin: 16,
    marginBottom: 8,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  previewContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  previewDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 16,
    opacity: 0.7,
  },
  optionsSection: {
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    borderWidth: 1,
  },
  optionContent: {
    flex: 1,
  },
  optionName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
  },
  optionPreview: {
    marginLeft: 16,
  },
  testCard: {
    margin: 16,
    marginBottom: 8,
  },
  testTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  testDescription: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 16,
  },
  testButton: {
    marginBottom: 16,
  },
  loadingTestContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 16,
    marginBottom: 16,
  },
  usageCard: {
    margin: 16,
    marginBottom: 32,
  },
  usageTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  usageText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  codeExample: {
    fontSize: 12,
    fontFamily: 'monospace',
    backgroundColor: 'rgba(0,0,0,0.05)',
    padding: 12,
    borderRadius: 8,
  },
});
