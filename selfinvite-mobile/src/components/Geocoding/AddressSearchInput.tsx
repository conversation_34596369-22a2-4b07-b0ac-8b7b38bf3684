import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { 
  Searchbar, 
  Text, 
  ActivityIndicator, 
  Surface,
  Divider,
  IconButton
} from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { geocodingService } from '../../services/geocodingService';
import type { AddressSearchResult } from '../../types/address';
import logger from '@/common/logger';

interface AddressSearchInputProps {
  onAddressSelect: (result: AddressSearchResult) => void;
  placeholder?: string;
  style?: any;
  disabled?: boolean;
}

/**
 * IMPORTANT: For the dropdown to appear correctly, ensure parent containers have:
 * - overflow: 'visible' (not 'hidden')
 * - No clipping that would cut off the dropdown
 * - If inside a ScrollView, consider using keyboardShouldPersistTaps="handled"
 */

export default function AddressSearchInput({
  onAddressSelect,
  placeholder = 'Search for an address...',
  style,
  disabled = false
}: AddressSearchInputProps) {
  const theme = useTheme();
  const searchbarRef = useRef<any>(null);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<AddressSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const isSelectingRef = useRef(false);

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setShowResults(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const searchResults = await geocodingService.searchAddresses(searchQuery, 5);
        setResults(searchResults);
        setShowResults(true);
      } catch (err) {
        logger.error('Address search error:', err);
        setError(err instanceof Error ? err.message : 'Search failed');
        setResults([]);
        setShowResults(false);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );

  // Trigger search when query changes (but not when selecting from results)
  useEffect(() => {
    if (!isSelectingRef.current) {
      debouncedSearch(query);
    }
    isSelectingRef.current = false; // Reset the flag
  }, [query, debouncedSearch]);

  const handleAddressSelect = (result: AddressSearchResult) => {
    onAddressSelect(result);
    isSelectingRef.current = true; // Prevent search from triggering
    setQuery(result.placeName);
    setShowResults(false);
    setResults([]);
    // Blur the searchbar to hide keyboard and ensure dropdown closes
    if (searchbarRef.current) {
      searchbarRef.current.blur();
    }
  };

  const handleClear = () => {
    setQuery('');
    setResults([]);
    setShowResults(false);
    setError(null);
  };

  const renderSearchResult = ({ item, index }: { item: AddressSearchResult; index: number }) => (
    <TouchableOpacity
      style={[styles.resultItem, { borderBottomColor: theme.colors.outline }]}
      onPress={() => handleAddressSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.resultContent}>
        <MaterialCommunityIcons
          name="map-marker"
          size={20}
          color={theme.colors.primary}
          style={styles.resultIcon}
        />
        <View style={styles.resultTextContainer}>
          <Text 
            style={[styles.resultTitle, { color: theme.colors.onSurface }]}
            numberOfLines={1}
          >
            {item.placeName}
          </Text>
          {item.context && item.context.length > 0 && (
            <Text 
              style={[styles.resultSubtitle, { color: theme.colors.onSurfaceVariant }]}
              numberOfLines={1}
            >
              {item.context.map(ctx => ctx.text).join(', ')}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <View style={styles.emptyState}>
          <ActivityIndicator size="small" color={theme.colors.primary} />
          <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
            Searching...
          </Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.emptyState}>
          <MaterialCommunityIcons
            name="alert-circle"
            size={20}
            color={theme.colors.error}
          />
          <Text style={[styles.emptyText, { color: theme.colors.error }]}>
            {error}
          </Text>
        </View>
      );
    }

    if (query.trim() && results.length === 0) {
      return (
        <View style={styles.emptyState}>
          <MaterialCommunityIcons
            name="map-marker-off"
            size={20}
            color={theme.colors.onSurfaceVariant}
          />
          <Text style={[styles.emptyText, { color: theme.colors.onSurfaceVariant }]}>
            No addresses found
          </Text>
        </View>
      );
    }

    return null;
  };

  return (
    <View style={[styles.container, style, { overflow: 'visible' }]}>
      <Searchbar
        ref={searchbarRef}
        placeholder={placeholder}
        value={query}
        onChangeText={setQuery}
        onClearIconPress={handleClear}
        loading={isLoading}
        editable={!disabled}
        style={[styles.searchbar, { backgroundColor: theme.colors.surface }]}
        inputStyle={{ color: theme.colors.onSurface }}
        iconColor={theme.colors.onSurfaceVariant}
        {...(query && { clearIcon: 'close' })}
      />

      {/* Search Results */}
      {showResults && (
        <Surface 
          style={[
            styles.resultsContainer, 
            { 
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.outline,
              zIndex: 10000,
              elevation: 10
            }
          ]}
          elevation={5}
        >
          {results.length > 0 ? (
            <ScrollView
              showsVerticalScrollIndicator={false}
              style={styles.resultsList}
              nestedScrollEnabled={true}
            >
              {results.map((item, index) => (
                <View key={item.id}>
                  {renderSearchResult({ item, index })}
                  {index < results.length - 1 && (
                    <Divider style={{ marginHorizontal: 16 }} />
                  )}
                </View>
              ))}
            </ScrollView>
          ) : (
            renderEmptyState()
          )}
        </Surface>
      )}
    </View>
  );
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 9999,
  },
  searchbar: {
    elevation: 0,
    zIndex: 9999,
  },
  resultsContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    maxHeight: 200,
    borderWidth: 1,
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    zIndex: 10000,
    elevation: 10,
  },
  resultsList: {
    maxHeight: 200,
  },
  resultItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  resultContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultIcon: {
    marginRight: 12,
  },
  resultTextContainer: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  resultSubtitle: {
    fontSize: 12,
  },
  emptyState: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  emptyText: {
    marginLeft: 8,
    fontSize: 14,
  },
});
