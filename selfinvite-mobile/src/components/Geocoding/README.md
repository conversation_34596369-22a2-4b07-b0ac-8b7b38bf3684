# Geocoding Components

A comprehensive location selection system with Mapbox Geocoding API and React Native Maps integration for the Selfinvite mobile app. **Expo Go compatible!**

## Features

- **Current Location Detection**: Get user's current location with permission handling
- **Address Search**: Real-time address search with autocomplete using Mapbox Geocoding API via HTTP requests
- **Interactive Map**: React Native Maps (Google Maps) for location visualization and fine-tuning
- **Location Validation**: Coordinate validation and error handling
- **Expo Go Compatible**: Uses HTTP requests for geocoding and react-native-maps for display
- **Generous Free Tier**: 100,000 free geocoding requests monthly from Mapbox
- **Responsive Design**: Works on different screen sizes with proper theming

## Components

### GeocodingSelector
Main component that provides a unified interface for location selection.

```tsx
import { GeocodingSelector } from '../src/components';

<GeocodingSelector
  onLocationSelect={(address) => console.log(address)}
  mode="current" // or "search"
  placeholder="Search for an address..."
  showMapCard={true}
/>
```

### AddressSearchInput
Standalone address search input with autocomplete.

```tsx
import { AddressSearchInput } from '../src/components';

<AddressSearchInput
  onAddressSelect={(result) => console.log(result)}
  placeholder="Search for an address..."
/>
```

### LocationMapCard
Interactive map card for displaying and fine-tuning selected locations.

```tsx
import { LocationMapCard } from '../src/components';

<LocationMapCard
  address={selectedAddress}
  onLocationConfirm={(address) => console.log(address)}
  onLocationChange={(address) => console.log(address)}
  height={250}
/>
```

## Setup

### 1. Install Dependencies

The required dependencies are already in `package.json` (`react-native-maps` is already included):

```bash
yarn install
```

### 2. Configure Mapbox Geocoding API

1. Get a Mapbox access token from [mapbox.com](https://www.mapbox.com/) (100k free requests/month)
2. Add it to your `.env` file:

```env
EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.your_mapbox_access_token
```

### 3. Configure Google Maps (for react-native-maps)

The app already uses `react-native-maps` with Google Maps provider. No additional setup needed for Expo Go.

**Note**: This implementation is **Expo Go compatible** because:
- Uses HTTP requests for Mapbox Geocoding API (no native SDK required)
- Uses `react-native-maps` which works in Expo Go
- No additional native dependencies needed

## Important: Parent Container Setup

**For the address dropdown to display correctly**, ensure parent containers have proper overflow settings:

```tsx
// ✅ Good - Parent ScrollView with proper settings
<ScrollView 
  keyboardShouldPersistTaps="handled"
  style={{ overflow: 'visible' }}
>
  <GeocodingSelector onLocationSelect={handleLocationSelect} />
</ScrollView>

// ✅ Good - Parent View with proper overflow
<View style={{ overflow: 'visible' }}>
  <GeocodingSelector onLocationSelect={handleLocationSelect} />
</View>

// ❌ Bad - Will clip the dropdown
<View style={{ overflow: 'hidden' }}>
  <GeocodingSelector onLocationSelect={handleLocationSelect} />
</View>
```

## Usage Examples

### Basic Location Selection

```tsx
import React, { useState } from 'react';
import { GeocodingSelector } from '../src/components';
import type { GeocodedAddress } from '../src/types/address';

export default function MyComponent() {
  const [selectedLocation, setSelectedLocation] = useState<GeocodedAddress | null>(null);

  const handleLocationSelect = (address: GeocodedAddress) => {
    setSelectedLocation(address);
    console.log('Selected location:', address);
  };

  return (
    <GeocodingSelector
      onLocationSelect={handleLocationSelect}
      mode="search"
      placeholder="Enter event location..."
      showMapCard={true}
    />
  );
}
```

### Integration with Event Creation

```tsx
import React, { useState } from 'react';
import { GeocodingSelector } from '../src/components';
import type { GeocodedAddress } from '../src/types/address';

export default function CreateEventScreen() {
  const [eventLocation, setEventLocation] = useState<GeocodedAddress | null>(null);

  const handleLocationSelect = (address: GeocodedAddress) => {
    setEventLocation(address);
    // Update your event form with the selected location
    updateEventForm({
      latitude: address.latitude,
      longitude: address.longitude,
      address: address.formattedAddress,
      city: address.city,
      country: address.country
    });
  };

  return (
    <View>
      <Text>Event Location</Text>
      <GeocodingSelector
        onLocationSelect={handleLocationSelect}
        initialLocation={eventLocation}
        mode="search"
        placeholder="Where is your event taking place?"
        showMapCard={true}
      />
    </View>
  );
}
```

### Custom Search Input

```tsx
import React, { useState } from 'react';
import { AddressSearchInput } from '../src/components';
import type { AddressSearchResult } from '../src/types/address';

export default function CustomSearchScreen() {
  const [searchResults, setSearchResults] = useState<AddressSearchResult[]>([]);

  const handleAddressSelect = (result: AddressSearchResult) => {
    setSearchResults(prev => [...prev, result]);
  };

  return (
    <AddressSearchInput
      onAddressSelect={handleAddressSelect}
      placeholder="Search for restaurants, cafes, or any address..."
    />
  );
}
```

## API Reference

### GeocodingSelector Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `onLocationSelect` | `(address: GeocodedAddress) => void` | Required | Callback when location is selected |
| `initialLocation` | `GeocodedAddress` | `undefined` | Initial location to display |
| `mode` | `'current' \| 'search'` | `'current'` | Default selection mode |
| `placeholder` | `string` | `'Search for an address...'` | Placeholder text for search input |
| `showMapCard` | `boolean` | `true` | Whether to show the map card |
| `style` | `any` | `undefined` | Custom styles |

### GeocodedAddress Interface

```typescript
interface GeocodedAddress {
  latitude: number;
  longitude: number;
  formattedAddress: string;
  city?: string;
  country?: string;
  postalCode?: string;
  street?: string;
  placeName?: string;
  confidence?: number;
  context?: Array<{
    id: string;
    text: string;
  }>;
}
```

### AddressSearchResult Interface

```typescript
interface AddressSearchResult {
  id: string;
  placeName: string;
  formattedAddress: string;
  coordinates: [number, number]; // [longitude, latitude]
  context?: Array<{
    id: string;
    text: string;
  }>;
  bbox?: [number, number, number, number];
}
```

## Services

### GeocodingService

The `geocodingService` provides methods for:

- `searchAddresses(query: string, limit?: number)`: Search for addresses
- `reverseGeocode(latitude: number, longitude: number)`: Get address from coordinates
- `getCurrentLocation()`: Get user's current location
- `isValidCoordinates(lat: number, lng: number)`: Validate coordinates
- `calculateDistance(lat1, lon1, lat2, lon2)`: Calculate distance between points

```tsx
import { geocodingService } from '../src/services/geocodingService';

// Search for addresses
const results = await geocodingService.searchAddresses('Paris, France');

// Get current location
const location = await geocodingService.getCurrentLocation();

// Reverse geocode
const address = await geocodingService.reverseGeocode(48.8566, 2.3522);
```

## Error Handling

The components include comprehensive error handling for:

- Network connectivity issues
- Invalid API responses
- Location permission denials
- Invalid coordinates
- Mapbox API errors

Error messages are user-friendly and provide actionable guidance.

## Theming

All components support React Native Paper theming and will automatically adapt to light/dark mode changes.

## Performance Considerations

- **Debounced Search**: Address search is debounced to prevent excessive API calls
- **Cached Results**: Search results are cached to improve performance
- **Lazy Loading**: Map components are loaded only when needed
- **Memory Management**: Proper cleanup of event listeners and timers

## Testing

To test the components, you can use the demo screen:

```bash
# Navigate to the demo screen in your app
# or run the demo component directly
```

The demo screen (`app/geocoding-demo.tsx`) provides a comprehensive testing interface for all geocoding features.

## Troubleshooting

### Common Issues

1. **Geocoding not working**: Ensure your Mapbox access token is correctly set in environment variables
2. **Location permission denied**: Check device location settings and app permissions
3. **Search not working**: Verify Mapbox API key has geocoding permissions and check network connectivity
4. **Map not displaying**: Ensure Google Maps is working in your region and check react-native-maps setup
5. **Dropdown appears behind other components**: Ensure parent containers have `overflow: 'visible'` and consider adding `keyboardShouldPersistTaps="handled"` to parent ScrollViews

### Debug Mode

Enable debug logging by setting the log level in your logger configuration:

```tsx
import logger from '@/common/logger';

// The geocoding service will log detailed information about HTTP requests
// Check network tab in developer tools for API request details
```

## Contributing

When adding new features or fixing bugs:

1. Follow the existing code style and patterns
2. Add proper TypeScript types
3. Include error handling
4. Update this documentation
5. Test on both iOS and Android
6. Ensure accessibility compliance
