import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { 
  Surface, 
  Text, 
  Button, 
  SegmentedButtons,
  ActivityIndicator,
  Divider
} from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AddressSearchInput from './AddressSearchInput';
import LocationMapCard from './LocationMapCard';
import { geocodingService } from '../../services/geocodingService';
import type { 
  GeocodedAddress, 
  AddressSearchResult, 
  LocationSelectionMode,
  GeocodingSelectorProps 
} from '../../types/address';
import logger from '@/common/logger';
import log from '@/common/logger';

export default function GeocodingSelector({
  onLocationSelect,
  initialLocation,
  mode = 'current',
  placeholder = 'Search for an address...',
  showMapCard = true,
  style
}: GeocodingSelectorProps) {
  const theme = useTheme();
  const [selectionMode, setSelectionMode] = useState<LocationSelectionMode>(mode);
  const [selectedAddress, setSelectedAddress] = useState<GeocodedAddress | null>(initialLocation || null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle mode change
  const handleModeChange = (newMode: string) => {
    setSelectionMode(newMode as LocationSelectionMode);
    setSelectedAddress(null);
    setError(null);
  };

  // Handle current location selection
  const handleCurrentLocation = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const location = await geocodingService.getCurrentLocation();
      log.info('handleCurrentLocation location:', location);
      setSelectedAddress(location);
    } catch (err) {
      logger.error('Error getting current location:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to get current location';
      setError(errorMessage);
      Alert.alert('Location Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle address search selection
  const handleAddressSelect = (result: AddressSearchResult) => {
    const address: GeocodedAddress = {
      latitude: result.coordinates[1],
      longitude: result.coordinates[0],
      formattedAddress: result.formattedAddress,
      placeName: result.placeName,
      ...(result.street && { street: result.street }),
      ...(result.city && { city: result.city }),
      ...(result.country && { country: result.country }),
      ...(result.postalCode && { postalCode: result.postalCode }),
      ...(result.context && { context: result.context })
    };
    setSelectedAddress(address);
    setError(null);
  };

  // Handle location confirmation
  const handleLocationConfirm = (address: GeocodedAddress) => {
    onLocationSelect(address);
  };

  // Handle location change on map
  const handleLocationChange = (address: GeocodedAddress) => {
    setSelectedAddress(address);
  };

  // Auto-get current location if mode is 'current' and no initial location
  useEffect(() => {
    if (selectionMode === 'current' && !selectedAddress && !initialLocation) {
      handleCurrentLocation();
    }
  }, [selectionMode]);

  const renderModeSelector = () => (
    <SegmentedButtons
      value={selectionMode}
      onValueChange={handleModeChange}
      buttons={[
        {
          value: 'current',
          label: 'Current Location',
          icon: 'crosshairs-gps',
        },
        {
          value: 'search',
          label: 'Search Address',
          icon: 'magnify',
        },
      ]}
      style={styles.modeSelector}
    />
  );

  const renderCurrentLocationMode = () => (
    <View style={styles.currentLocationContainer}>
      {!selectedAddress ? (
        <View style={styles.currentLocationActions}>
          <Button
            mode="contained"
            onPress={handleCurrentLocation}
            loading={isLoading}
            disabled={isLoading}
            icon="crosshairs-gps"
            style={styles.currentLocationButton}
          >
            {isLoading ? 'Getting Location...' : 'Get Current Location'}
          </Button>
          
          {error && (
            <View style={styles.errorContainer}>
              <MaterialCommunityIcons
                name="alert-circle"
                size={16}
                color={theme.colors.error}
              />
              <Text style={[styles.errorText, { color: theme.colors.error }]}>
                {error}
              </Text>
            </View>
          )}
        </View>
      ) : null}
    </View>
  );

  const renderSearchMode = () => (
    <View style={styles.searchContainer}>
      <AddressSearchInput
        onAddressSelect={handleAddressSelect}
        placeholder={placeholder}
        style={styles.searchInput}
      />
    </View>
  );

  const renderMapCard = () => {
    if (!selectedAddress || !showMapCard) {
      return null;
    }

    return (
      <LocationMapCard
        address={selectedAddress}
        onLocationConfirm={handleLocationConfirm}
        onLocationChange={handleLocationChange}
        style={styles.mapCard}
        height={500}
      />
    );
  };

  const renderConfirmButton = () => {
    if (!selectedAddress || showMapCard) {
      return null;
    }

    return (
      <Button
        mode="contained"
        onPress={() => handleLocationConfirm(selectedAddress)}
        style={styles.confirmButton}
        icon="check"
      >
        Use This Location
      </Button>
    );
  };

  return (
    <Surface style={[styles.container, style, { overflow: 'visible' }]} elevation={1}>
      {/* <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Select Location
      </Text> */}

      {renderModeSelector()}

      <Divider style={styles.divider} />

      {selectionMode === 'current' ? renderCurrentLocationMode() : renderSearchMode()}

      {renderMapCard()}
      {renderConfirmButton()}
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  modeSelector: {
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  currentLocationContainer: {
    minHeight: 60,
    justifyContent: 'center',
  },
  currentLocationActions: {
    alignItems: 'center',
  },
  currentLocationButton: {
    marginBottom: 12,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  errorText: {
    marginLeft: 8,
    fontSize: 14,
    textAlign: 'center',
  },
  selectedLocationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedLocationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 12,
  },
  selectedLocationText: {
    flex: 1,
    fontSize: 14,
    marginLeft: 8,
  },
  searchContainer: {
    minHeight: 60,
    overflow: 'visible',
    zIndex: 9998,
  },
  searchInput: {
    marginBottom: 8,
  },
  mapCard: {
    marginTop: 8,
  },
  confirmButton: {
    marginTop: 16,
    alignSelf: 'center',
  },
});
