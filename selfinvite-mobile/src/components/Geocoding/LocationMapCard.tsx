import React, { useState, useRef, useEffect, useMemo } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { 
  Surface, 
  Text, 
  Button, 
  IconButton,
  ActivityIndicator
} from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import MapView, { Marker, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import type { GeocodedAddress } from '../../types/address';
import logger from '@/common/logger';
import log from '@/common/logger';

const { width: screenWidth } = Dimensions.get('window');

interface LocationMapCardProps {
  address: GeocodedAddress;
  onLocationConfirm: (address: GeocodedAddress) => void;
  onLocationChange?: (address: GeocodedAddress) => void;
  style?: any;
  height?: number;
  showConfirmButton?: boolean;
}

export default function LocationMapCard({
  address,
  onLocationConfirm,
  onLocationChange,
  style,
  height = 200,
  showConfirmButton = true
}: LocationMapCardProps) {
  const theme = useTheme();
  const [isMapReady, setIsMapReady] = useState(true); // react-native-maps doesn't need initialization
  const [mapRegion, setMapRegion] = useState<Region>({
    latitude: address.latitude,
    longitude: address.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [selectedCoordinates, setSelectedCoordinates] = useState({
    latitude: address.latitude,
    longitude: address.longitude,
  });
  const mapRef = useRef<MapView>(null);

  // Update region when address changes
  useEffect(() => {
    setMapRegion({
      latitude: address.latitude,
      longitude: address.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    });
    setSelectedCoordinates({
      latitude: address.latitude,
      longitude: address.longitude,
    });
  }, [address]);

  const handleMapPress = (event: any) => {
    const { coordinate } = event.nativeEvent;
    if (coordinate) {
      const { latitude, longitude } = coordinate;
      setSelectedCoordinates({ latitude, longitude });
      
      // Update the address with new coordinates
      const updatedAddress: GeocodedAddress = {
        ...address,
        latitude,
        longitude
      };
      
      onLocationChange?.(updatedAddress);
    }
  };

  const handleConfirmLocation = () => {
    log.info('handleConfirmLocation selectedCoordinates:', selectedCoordinates);
    log.info('handleConfirmLocation address:', address);
    const confirmedAddress: GeocodedAddress = {
      ...address,
      latitude: selectedCoordinates.latitude,
      longitude: selectedCoordinates.longitude
    };
    onLocationConfirm(confirmedAddress);
  };

  // Memoize the formatted address to prevent unnecessary recalculations
  const formattedAddress = useMemo(() => {
    if (address.formattedAddress) {
      return address.formattedAddress;
    }
    
    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.country) parts.push(address.country);
    
    return parts.length > 0 ? parts.join(', ') : 'Selected Location';
  }, [address.formattedAddress, address.street, address.city, address.country]);

  if (!isMapReady) {
    return (
      <Surface style={[styles.container, { height }, style]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
            Loading map...
          </Text>
        </View>
      </Surface>
    );
  }

  return (
    <Surface 
      style={[styles.container, { height }, style]}
      elevation={2}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.addressContainer}>
          <MaterialCommunityIcons
            name="map-marker"
            size={20}
            color={theme.colors.primary}
          />
          <Text 
            style={[styles.addressText, { color: theme.colors.onSurface }]}
            numberOfLines={2}
          >
            {formattedAddress}
          </Text>
        </View>
      </View>

      {/* Map */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={mapRegion}
          onPress={handleMapPress}
          onRegionChangeComplete={setMapRegion}
          showsUserLocation={false}
          showsMyLocationButton={false}
          showsCompass={true}
          showsScale={true}
        >
          {/* Selected location marker */}
          <Marker
            coordinate={selectedCoordinates}
            title="Selected Location"
            description={formattedAddress}
            pinColor={theme.colors.primary}
          >
            <View style={[styles.marker, { backgroundColor: theme.colors.primary }]}>
              <MaterialCommunityIcons
                name="map-marker"
                size={32}
                color="white"
              />
            </View>
          </Marker>
        </MapView>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={[styles.instructionText, { color: theme.colors.onSurfaceVariant }]}>
          Tap on the map to adjust the location
        </Text>
        
        {showConfirmButton && (
          <Button
            mode="contained"
            onPress={handleConfirmLocation}
            style={styles.confirmButton}
            icon="check"
          >
            Use This Location
          </Button>
        )}
      </View>
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  addressText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    lineHeight: 20,
  },
  coordinatesText: {
    fontSize: 12,
    marginLeft: 28,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  marker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  instructionText: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 12,
  },
  confirmButton: {
    alignSelf: 'center',
  },
});
