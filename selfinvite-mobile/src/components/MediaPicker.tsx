import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 60) / 3;

interface MediaPickerProps {
  onMediaSelected?: (media: MediaItem[]) => void;
  maxFiles?: number;
  allowedTypes?: ('image' | 'video')[];
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export interface MediaItem {
  uri: string;
  type: 'image' | 'video';
  name: string;
  size?: number;
}

export const MediaPicker: React.FC<MediaPickerProps> = ({
  onMediaSelected,
  maxFiles = 5,
  allowedTypes = ['image', 'video'],
  className,
  placeholder = 'Add photos or videos',
  disabled = false,
}) => {
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);

  const requestPermissions = useCallback(async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to select media.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  }, []);

  const selectMedia = useCallback(async () => {
    if (selectedMedia.length >= maxFiles) {
      Alert.alert('Maximum Files Reached', `You can only select up to ${maxFiles} files.`);
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: allowedTypes.includes('video') 
          ? ['images', 'videos']
          : ['images'],
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets) {
        const newMediaItems: MediaItem[] = result.assets.map(asset => ({
          uri: asset.uri,
          type: asset.type === 'video' ? 'video' : 'image',
          name: asset.fileName || `media_${Date.now()}`,
          size: asset.fileSize || 0,
        }));

        const updatedMedia = [...selectedMedia, ...newMediaItems];
        setSelectedMedia(updatedMedia);
        onMediaSelected?.(updatedMedia);
      }
    } catch (error) {
      console.error('Error selecting media:', error);
      Alert.alert('Error', 'Failed to select media. Please try again.');
    }
  }, [selectedMedia.length, maxFiles, allowedTypes, requestPermissions, onMediaSelected]);

  const removeMedia = useCallback((index: number) => {
    const updatedMedia = selectedMedia.filter((_, i) => i !== index);
    setSelectedMedia(updatedMedia);
    onMediaSelected?.(updatedMedia);
  }, [selectedMedia, onMediaSelected]);

  const renderMediaItem = useCallback((item: MediaItem, index: number) => {
    return (
      <View key={`${item.uri}-${index}`} style={styles.mediaItem}>
        <View style={styles.mediaContainer}>
          {item.type === 'image' ? (
            <Image source={{ uri: item.uri }} style={styles.mediaPreview} />
          ) : (
            <View style={styles.videoPreview}>
              <Ionicons name="videocam" size={24} color="#666" />
              <Text style={styles.videoText}>Video</Text>
            </View>
          )}
          
          {/* Remove Button */}
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeMedia(index)}
          >
            <Ionicons name="close-circle" size={20} color="#ff4444" />
          </TouchableOpacity>
        </View>
      </View>
    );
  }, [removeMedia]);

  return (
    <View style={[styles.container, className as any]}>
      <View style={styles.header}>
        <Text style={styles.title}>Media</Text>
        <Text style={styles.subtitle}>
          {selectedMedia.length}/{maxFiles} selected
        </Text>
      </View>

      <View style={styles.mediaGrid}>
        {/* Add Media Button */}
        <TouchableOpacity
          style={[styles.mediaItem, styles.addButton]}
          onPress={selectMedia}
          disabled={selectedMedia.length >= maxFiles || disabled}
        >
          <Ionicons 
            name="add" 
            size={32} 
            color={selectedMedia.length >= maxFiles || disabled ? "#ccc" : "#007AFF"} 
          />
          <Text style={[
            styles.addButtonText, 
            (selectedMedia.length >= maxFiles || disabled) && styles.addButtonTextDisabled
          ]}>
            {placeholder}
          </Text>
        </TouchableOpacity>

        {/* Selected Media Items */}
        {selectedMedia.map(renderMediaItem)}
      </View>

      {selectedMedia.length === 0 && (
        <Text style={styles.helpText}>
          {disabled 
            ? 'Please log in to add photos or videos to your event'
            : 'Tap the + button to add photos or videos to your event'
          }
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  mediaGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  mediaItem: {
    width: ITEM_WIDTH,
    alignItems: 'center',
  },
  mediaContainer: {
    width: ITEM_WIDTH,
    height: ITEM_WIDTH,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#f5f5f5',
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
  },
  videoPreview: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  videoText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  addButton: {
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
    height: ITEM_WIDTH,
  },
  addButtonText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    textAlign: 'center',
    paddingHorizontal: 8,
  },
  addButtonTextDisabled: {
    color: '#ccc',
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 2,
  },
  helpText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    fontStyle: 'italic',
  },
});

export default MediaPicker;
