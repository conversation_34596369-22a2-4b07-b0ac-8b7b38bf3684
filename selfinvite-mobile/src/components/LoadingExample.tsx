import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { BouncingBallsSpinner, PulsingDotsSpinner, RotatingRingSpinner, SpinningDotsSpinner, WaveSpinner } from './FancySpinner';

interface LoadingExampleProps {
  message?: string;
  size?: number;
}

export default function LoadingExample({ 
  message = "Loading your events...", 
  size = 60 
}: LoadingExampleProps) {
  const theme = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Surface style={[styles.loadingContainer, { backgroundColor: theme.colors.surface }]}>
        {/* <PulsingDotsSpinner size={size} color={theme.colors.primary} /> */}
        <Text style={[styles.loadingText, { color: theme.colors.onSurface }]}>
          {message}
        </Text>
        {/* <RotatingRingSpinner size={size} color={theme.colors.primary} /> */}
        {/* <BouncingBallsSpinner size={size} color={theme.colors.primary} /> */}
        {/* <WaveSpinner size={size} color={theme.colors.primary} /> */}
        <SpinningDotsSpinner size={size} color={theme.colors.primary} />
      </Surface>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    padding: 40,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 4,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});
