import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Surface, Text, TextInput, Button, ActivityIndicator, useTheme, List, Divider } from 'react-native-paper';
import { useAuthStore } from '../stores/authStore';
import { showErrorDialog } from '../stores';
import logger from '../common/logger';
import { getApiBaseUrl } from '../utils/env';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface RequestHistoryItem {
  method: 'GET' | 'POST';
  url: string;
  params: string;
  timestamp: number;
}

const HISTORY_STORAGE_KEY = '@api_tester_history';
const MAX_HISTORY_ITEMS = 10;

export const ApiTester: React.FC = () => {
  const theme = useTheme();
  const { session } = useAuthStore();
  const [url, setUrl] = useState('/api/v1/user/profile');
  const [params, setParams] = useState('{}');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState<RequestHistoryItem[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  const handleRequest = async (method: 'GET' | 'POST') => {
    setLoading(true);
    setResponse('');

    try {
      const fullUrl = `${getApiBaseUrl()}${url}`;
      const newHistoryItem: RequestHistoryItem = { method, url, params, timestamp: Date.now() };
      updateHistory(newHistoryItem);

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
      }

      const options: RequestInit = {
        method,
        headers,
      };

      if (method === 'POST') {
        try {
          JSON.parse(params); // Validate JSON
          options.body = params;
        } catch (e) {
          showErrorDialog('Invalid JSON in parameters.', 'Input Error');
          setLoading(false);
          return;
        }
      }

      logger.info(`Making ${method} request to ${fullUrl}`);
      const res = await fetch(fullUrl, options);
      const responseData = await res.json();

      setResponse(JSON.stringify(responseData, null, 2));
    } catch (error: any) {
      logger.error('API Test Error:', error);
      setResponse(`Error: ${error.message}`);
      showErrorDialog(error.message, 'API Request Failed');
    } finally {
      setLoading(false);
    }
  };

  // Load history from storage on mount
  useEffect(() => {
    const loadHistory = async () => {
      try {
        const storedHistory = await AsyncStorage.getItem(HISTORY_STORAGE_KEY);
        if (storedHistory) {
          setHistory(JSON.parse(storedHistory));
        }
      } catch (e) {
        logger.error('Failed to load API tester history', e);
      }
    };
    loadHistory();
  }, []);

  const updateHistory = async (newHistoryItem: RequestHistoryItem) => {
    const updatedHistory = [
      newHistoryItem,
      ...history.filter(item => !(item.method === newHistoryItem.method && item.url === newHistoryItem.url && item.params === newHistoryItem.params))
    ].slice(0, MAX_HISTORY_ITEMS);
    
    setHistory(updatedHistory);
    try {
      await AsyncStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(updatedHistory));
    } catch (e) {
      logger.error('Failed to save API tester history', e);
    }
  };

  const handleSelectHistoryItem = (item: RequestHistoryItem) => {
    setUrl(item.url);
    setParams(item.params);
    setShowHistory(false);
  };

  const handleClearHistory = async () => {
    setHistory([]);
    await AsyncStorage.removeItem(HISTORY_STORAGE_KEY);
  };

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Backend API Tester
      </Text>
      
      <TextInput
        label="Endpoint URL (e.g., /api/v1/users/me)"
        value={url}
        onChangeText={setUrl}
        mode="outlined"
        style={styles.input}
        autoCapitalize="none"
        autoCorrect={false}
      />

      <TextInput
        label="POST Parameters (JSON)"
        value={params}
        onChangeText={setParams}
        mode="outlined"
        style={styles.input}
        multiline
        numberOfLines={4}
        autoCapitalize="none"
        autoCorrect={false}
      />

      <View style={styles.buttonContainer}>
        <Button mode="contained" onPress={() => handleRequest('GET')} disabled={loading} style={styles.button}>GET</Button>
        <Button mode="contained" onPress={() => handleRequest('POST')} disabled={loading} style={styles.button}>POST</Button>
      </View>

      {loading && <ActivityIndicator animating={true} color={theme.colors.primary} style={styles.loader} />}

      <Divider style={styles.divider} />

      <List.Accordion
        title="Request History"
        left={props => <List.Icon {...props} icon="history" />}
        expanded={showHistory}
        onPress={() => setShowHistory(!showHistory)}
        style={styles.accordion}
      >
        {history.length > 0 ? (
          history.map((item, index) => (
            <List.Item
              key={`${item.timestamp}-${index}`}
              title={`${item.method} ${item.url}`}
              description={`Ran at ${new Date(item.timestamp).toLocaleTimeString()}`}
              onPress={() => handleSelectHistoryItem(item)}
              left={props => <List.Icon {...props} icon={item.method === 'GET' ? 'arrow-down-bold-circle-outline' : 'arrow-up-bold-circle-outline'} />}
            />
          ))
        ) : (
          <Text style={styles.noHistoryText}>No recent requests.</Text>
        )}
        {history.length > 0 && (
          <Button mode="text" onPress={handleClearHistory} style={styles.clearButton}>
            Clear History
          </Button>
        )}
      </List.Accordion>


      {response && (
        <View style={styles.responseContainer}>
          <Text style={[styles.responseTitle, { color: theme.colors.onSurface }]}>Response:</Text>
          <ScrollView style={styles.responseScroll}>
            <Text style={[styles.responseText, { color: theme.colors.onSurfaceVariant }]}>{response}</Text>
          </ScrollView>
        </View>
      )}
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: { padding: 16, borderRadius: 12, marginVertical: 8 },
  title: { fontSize: 16, fontWeight: '600', marginBottom: 16 },
  input: { marginBottom: 12 },
  buttonContainer: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 16 },
  button: { flex: 1, marginHorizontal: 4 },
  loader: { marginVertical: 16 },
  divider: { marginVertical: 8 },
  accordion: { paddingHorizontal: 0, marginHorizontal: -16 },
  noHistoryText: { padding: 16, fontStyle: 'italic', opacity: 0.7, textAlign: 'center' },
  clearButton: { alignSelf: 'center', marginTop: 8 },
  responseContainer: { marginTop: 16, padding: 12, backgroundColor: 'rgba(0,0,0,0.05)', borderRadius: 8, maxHeight: 300 },
  responseTitle: { fontSize: 14, fontWeight: '600', marginBottom: 8 },
  responseScroll: {},
  responseText: { fontFamily: 'monospace', fontSize: 12 },
});

export default ApiTester;