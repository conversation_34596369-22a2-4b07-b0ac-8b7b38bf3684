import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Alert, Modal } from 'react-native';
import { Button, Surface, Text, IconButton, useTheme } from 'react-native-paper';
import * as ImagePicker from 'expo-image-picker';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { MediaPicker, MediaItem } from './MediaPicker';

interface CameraMediaPickerProps {
  onMediaSelected: (media: MediaItem[]) => void;
  maxFiles?: number;
  allowedTypes?: ('image' | 'video')[];
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export const CameraMediaPicker: React.FC<CameraMediaPickerProps> = ({
  onMediaSelected,
  maxFiles = 5,
  allowedTypes = ['image', 'video'],
  className,
  placeholder = 'Add photos or videos',
  disabled = false,
}) => {
  const theme = useTheme();
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const [showCamera, setShowCamera] = useState(false);
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();

  const requestMediaPermissions = useCallback(async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to select media.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  }, []);

  const requestCameraPermissions = useCallback(async () => {
    if (!permission) {
      const result = await requestPermission();
      return result.granted;
    }
    return permission.granted;
  }, [permission, requestPermission]);

  const selectFromLibrary = useCallback(async () => {
    if (selectedMedia.length >= maxFiles) {
      Alert.alert('Maximum Files Reached', `You can only select up to ${maxFiles} files.`);
      return;
    }

    const hasPermission = await requestMediaPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: allowedTypes.includes('video') 
          ? ImagePicker.MediaTypeOptions.All 
          : ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets) {
        const newMediaItems: MediaItem[] = result.assets.map(asset => ({
          uri: asset.uri,
          type: asset.type === 'video' ? 'video' : 'image',
          name: asset.fileName || `media_${Date.now()}`,
          size: asset.fileSize || 0,
        }));

        const updatedMedia = [...selectedMedia, ...newMediaItems];
        setSelectedMedia(updatedMedia);
        onMediaSelected?.(updatedMedia);
      }
    } catch (error) {
      console.error('Error selecting media:', error);
      Alert.alert('Error', 'Failed to select media. Please try again.');
    }
  }, [selectedMedia.length, maxFiles, allowedTypes, requestMediaPermissions, onMediaSelected]);

  const takePhoto = useCallback(async () => {
    const hasPermission = await requestCameraPermissions();
    if (!hasPermission) {
      Alert.alert(
        'Permission Required',
        'Please grant camera permissions to take photos.',
        [{ text: 'OK' }]
      );
      return;
    }

    setShowCamera(true);
  }, [requestCameraPermissions]);

  const handleCameraCapture = useCallback(async (cameraRef: any) => {
    if (!cameraRef) return;

    try {
      const photo = await cameraRef.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo) {
        const newMediaItem: MediaItem = {
          uri: photo.uri,
          type: 'image',
          name: `camera_${Date.now()}.jpg`,
          size: 0, // Camera doesn't provide size info
        };

        const updatedMedia = [...selectedMedia, newMediaItem];
        setSelectedMedia(updatedMedia);
        onMediaSelected?.(updatedMedia);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setShowCamera(false);
    }
  }, [selectedMedia, onMediaSelected]);

  const removeMedia = useCallback((index: number) => {
    const updatedMedia = selectedMedia.filter((_, i) => i !== index);
    setSelectedMedia(updatedMedia);
    onMediaSelected?.(updatedMedia);
  }, [selectedMedia, onMediaSelected]);

  const showMediaOptions = useCallback(() => {
    Alert.alert(
      'Add Media',
      'Choose how you want to add media',
      [
        { text: 'Camera', onPress: takePhoto },
        { text: 'Photo Library', onPress: selectFromLibrary },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, [takePhoto, selectFromLibrary]);

  return (
    <View style={[styles.container, className]}>
      <MediaPicker
        onMediaSelected={onMediaSelected}
        maxFiles={maxFiles}
        allowedTypes={allowedTypes}
        placeholder={placeholder}
        disabled={disabled}
      />
      
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={showMediaOptions}
          disabled={disabled || selectedMedia.length >= maxFiles}
          icon="camera-plus"
          style={styles.actionButton}
        >
          Add Media
        </Button>
      </View>

      {/* Camera Modal */}
      <Modal
        visible={showCamera}
        animationType="slide"
        onRequestClose={() => setShowCamera(false)}
      >
        <View style={styles.cameraContainer}>
          <CameraView
            style={styles.camera}
            facing={cameraType}
            ref={(ref) => {
              if (ref) {
                // Store camera ref for capture
                (ref as any).cameraRef = ref;
              }
            }}
          >
            <View style={styles.cameraControls}>
              <IconButton
                icon="close"
                iconColor="white"
                size={30}
                onPress={() => setShowCamera(false)}
                style={styles.closeButton}
              />
              
              <View style={styles.cameraActions}>
                <IconButton
                  icon="camera-flip"
                  iconColor="white"
                  size={30}
                  onPress={() => setCameraType(cameraType === 'back' ? 'front' : 'back')}
                />
                
                <IconButton
                  icon="camera"
                  iconColor="white"
                  size={50}
                  onPress={() => handleCameraCapture((CameraView as any).cameraRef)}
                  style={styles.captureButton}
                />
              </View>
            </View>
          </CameraView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  cameraControls: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  closeButton: {
    alignSelf: 'flex-start',
  },
  cameraActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  captureButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 35,
  },
});

export default CameraMediaPicker;
