import React, { useState } from 'react';
import { 
  View, 
  StyleSheet, 
  ScrollView, 
  Dimensions 
} from 'react-native';
import {
  Modal,
  Portal,
  Surface,
  Text,
  Button,
  IconButton,
  List,
  Chip,
  Switch,
  Divider
} from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import Slider from '@react-native-community/slider';
import { useSearchStore, useSearchFilters } from '../stores/searchStore';
import type { SearchFilters } from '../types/search';
import { SEARCH_CONSTANTS } from '../types/search';

const { height: screenHeight } = Dimensions.get('window');

// Filter option types based on legacy Search.js
const KITCHEN_TYPES = [
  { label: 'Italian', value: 'italian', icon: '🇮🇹' },
  { label: 'French', value: 'french', icon: '🇫🇷' },
  { label: 'Spanish', value: 'spanish', icon: '🇪🇸' },
  { label: 'German', value: 'german', icon: '🇩🇪' },
  { label: 'Asian', value: 'asian', icon: '🥢' },
  { label: 'Indian', value: 'indian', icon: '🇮🇳' },
  { label: 'Mediterranean', value: 'mediterranean', icon: '🫒' },
  { label: 'Mexican', value: 'mexican', icon: '🇲🇽' },
  { label: 'Vegan', value: 'vegan', icon: '🌱' },
  { label: 'Vegetarian', value: 'vegetarian', icon: '🥗' },
];

const BEVERAGE_TYPES = [
  { label: 'Wine', value: 'wine', icon: '🍷' },
  { label: 'Beer', value: 'beer', icon: '🍺' },
  { label: 'Cocktails', value: 'cocktails', icon: '🍸' },
  { label: 'Non-alcoholic', value: 'non_alcoholic', icon: '🥤' },
  { label: 'Coffee/Tea', value: 'coffee_tea', icon: '☕' },
];

const EVENT_TYPES = [
  { label: 'Dinner', value: 'dinner', icon: '🍽️' },
  { label: 'Lunch', value: 'lunch', icon: '🥗' },
  { label: 'Brunch', value: 'brunch', icon: '🥐' },
  { label: 'Cooking Class', value: 'cooking_class', icon: '👨‍🍳' },
  { label: 'Wine Tasting', value: 'wine_tasting', icon: '🍷' },
  { label: 'BBQ', value: 'bbq', icon: '🔥' },
  { label: 'Picnic', value: 'picnic', icon: '🧺' },
  { label: 'Food Tour', value: 'food_tour', icon: '🚶‍♂️' },
];

const LOCATION_TYPES = [
  { label: 'Home', value: 'home', icon: '🏠' },
  { label: 'Restaurant', value: 'restaurant', icon: '🍽️' },
  { label: 'Outdoor', value: 'outdoor', icon: '🌳' },
  { label: 'Rooftop', value: 'rooftop', icon: '🏢' },
  { label: 'Garden', value: 'garden', icon: '🌺' },
  { label: 'Beach', value: 'beach', icon: '🏖️' },
];

const INTOLERANCES = [
  { label: 'Gluten-free', value: 'gluten_free', icon: '🌾' },
  { label: 'Dairy-free', value: 'dairy_free', icon: '🥛' },
  { label: 'Nut-free', value: 'nut_free', icon: '🥜' },
  { label: 'Shellfish-free', value: 'shellfish_free', icon: '🦐' },
  { label: 'Egg-free', value: 'egg_free', icon: '🥚' },
  { label: 'Soy-free', value: 'soy_free', icon: '🫘' },
];

interface SearchFilterModalProps {
  visible: boolean;
  onDismiss: () => void;
}

export default function SearchFilterModal({ visible, onDismiss }: SearchFilterModalProps) {
  const theme = useTheme();
  const { active: activeFilters, hasUnsaved } = useSearchFilters();
  const { 
    setActiveFilters, 
    applyFilters, 
    clearFilters, 
    resetToAppliedFilters,
    getActiveFilterCount
  } = useSearchStore();

  // Local state for date pickers
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setActiveFilters({ [key]: value });
  };

  const handleArrayFilterToggle = (key: keyof SearchFilters, value: string) => {
    const currentArray = (activeFilters[key] as string[]) || [];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    handleFilterChange(key, newArray.length > 0 ? newArray : undefined);
  };

  const handleApply = () => {
    applyFilters();
    onDismiss();
  };

  const handleClear = () => {
    clearFilters();
  };

  const handleCancel = () => {
    if (hasUnsaved) {
      resetToAppliedFilters();
    }
    onDismiss();
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  const renderMultiSelectChips = (
    options: { label: string; value: string; icon: string }[],
    filterKey: keyof SearchFilters,
    title: string
  ) => {
    const selectedValues = (activeFilters[filterKey] as string[]) || [];

    return (
      <List.Accordion
        title={title}
        left={props => (
          <MaterialCommunityIcons 
            name="food" 
            size={24} 
            color={theme.colors.primary}
            style={{ marginLeft: 8 }}
          />
        )}
        right={props => (
          selectedValues.length > 0 && (
            <Chip 
              mode="flat" 
              compact 
              style={{ backgroundColor: theme.colors.primary }}
              textStyle={{ color: 'white', fontSize: 12 }}
            >
              {selectedValues.length}
            </Chip>
          )
        )}
      >
        <View style={styles.chipsContainer}>
          {options.map((option) => {
            const isSelected = selectedValues.includes(option.value);
            return (
              <Chip
                key={option.value}
                mode={isSelected ? "flat" : "outlined"}
                selected={isSelected}
                onPress={() => handleArrayFilterToggle(filterKey, option.value)}
                style={[
                  styles.chip,
                  isSelected && { backgroundColor: theme.colors.primary }
                ]}
                textStyle={[
                  styles.chipText,
                  isSelected && { color: 'white' }
                ]}
                icon={() => <Text style={{ fontSize: 16 }}>{option.icon}</Text>}
              >
                {option.label}
              </Chip>
            );
          })}
        </View>
      </List.Accordion>
    );
  };

  return (
    <Portal>
      <Modal 
        visible={visible} 
        onDismiss={handleCancel}
        contentContainerStyle={styles.modal}
      >
        <SafeAreaView style={styles.container}>
          {/* Header */}
          <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.headerContent}>
              <IconButton
                icon="close"
                size={24}
                onPress={handleCancel}
                iconColor={theme.colors.onSurface}
              />
              <Text style={[styles.headerTitle, { color: theme.colors.onSurface }]}>
                Filters
              </Text>
              <Button
                mode="text"
                onPress={handleClear}
                textColor={theme.colors.primary}
                disabled={getActiveFilterCount() === 0}
              >
                Clear
              </Button>
            </View>
            {getActiveFilterCount() > 0 && (
              <View style={styles.filterCount}>
                <Text style={[styles.filterCountText, { color: theme.colors.primary }]}>
                  {getActiveFilterCount()} filters active
                </Text>
              </View>
            )}
          </Surface>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Date Range */}
            <List.Accordion
              title="Date Range"
              left={props => (
                <MaterialCommunityIcons 
                  name="calendar" 
                  size={24} 
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
            >
              <View style={styles.dateContainer}>
                <Button
                  mode="outlined"
                  onPress={() => setShowStartDatePicker(true)}
                  style={styles.dateButton}
                  icon="calendar"
                >
                  {activeFilters.dateRange?.[0] 
                    ? formatDate(new Date(activeFilters.dateRange[0]))
                    : 'Start Date'
                  }
                </Button>
                
                <Button
                  mode="outlined"
                  onPress={() => setShowEndDatePicker(true)}
                  style={styles.dateButton}
                  icon="calendar"
                >
                  {activeFilters.dateRange?.[1] 
                    ? formatDate(new Date(activeFilters.dateRange[1]))
                    : 'End Date'
                  }
                </Button>

                {(activeFilters.dateRange?.[0] || activeFilters.dateRange?.[1]) && (
                  <Button
                    mode="text"
                    onPress={() => handleFilterChange('dateRange', undefined)}
                    textColor={theme.colors.error}
                  >
                    Clear Dates
                  </Button>
                )}
              </View>
            </List.Accordion>

            <Divider />

            {/* Price Range */}
            <List.Accordion
              title={`Price Range: €${activeFilters.priceRange?.[0] || SEARCH_CONSTANTS.MIN_PRICE} - €${activeFilters.priceRange?.[1] || SEARCH_CONSTANTS.MAX_PRICE}`}
              left={props => (
                <MaterialCommunityIcons 
                  name="currency-eur" 
                  size={24} 
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
            >
              <View style={styles.sliderContainer}>
                <Text style={[styles.sliderLabel, { color: theme.colors.onSurface }]}>
                  Minimum Price: €{activeFilters.priceRange?.[0] || SEARCH_CONSTANTS.MIN_PRICE}
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={SEARCH_CONSTANTS.MIN_PRICE}
                  maximumValue={SEARCH_CONSTANTS.MAX_PRICE}
                  value={activeFilters.priceRange?.[0] || SEARCH_CONSTANTS.MIN_PRICE}
                  onValueChange={(value) => {
                    const currentRange = activeFilters.priceRange || [SEARCH_CONSTANTS.MIN_PRICE, SEARCH_CONSTANTS.MAX_PRICE];
                    handleFilterChange('priceRange', [Math.round(value), currentRange[1]]);
                  }}
                  step={5}
                  minimumTrackTintColor={theme.colors.primary}
                  maximumTrackTintColor={theme.colors.outline}
                  thumbTintColor={theme.colors.primary}
                />
                
                <Text style={[styles.sliderLabel, { color: theme.colors.onSurface }]}>
                  Maximum Price: €{activeFilters.priceRange?.[1] || SEARCH_CONSTANTS.MAX_PRICE}
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={SEARCH_CONSTANTS.MIN_PRICE}
                  maximumValue={SEARCH_CONSTANTS.MAX_PRICE}
                  value={activeFilters.priceRange?.[1] || SEARCH_CONSTANTS.MAX_PRICE}
                  onValueChange={(value) => {
                    const currentRange = activeFilters.priceRange || [SEARCH_CONSTANTS.MIN_PRICE, SEARCH_CONSTANTS.MAX_PRICE];
                    handleFilterChange('priceRange', [currentRange[0], Math.round(value)]);
                  }}
                  step={5}
                  minimumTrackTintColor={theme.colors.primary}
                  maximumTrackTintColor={theme.colors.outline}
                  thumbTintColor={theme.colors.primary}
                />
              </View>
            </List.Accordion>

            <Divider />

            {/* Distance */}
            <List.Accordion
              title={`Distance: ${activeFilters.radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS}km`}
              left={props => (
                <MaterialCommunityIcons 
                  name="map-marker-radius" 
                  size={24} 
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
            >
              <View style={styles.sliderContainer}>
                <Text style={[styles.sliderLabel, { color: theme.colors.onSurface }]}>
                  Search Radius: {activeFilters.radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS}km
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={SEARCH_CONSTANTS.MIN_DISTANCE_FILTER}
                  maximumValue={SEARCH_CONSTANTS.MAX_DISTANCE_FILTER}
                  value={activeFilters.radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS}
                  onValueChange={(value) => handleFilterChange('radius', Math.round(value))}
                  step={5}
                  minimumTrackTintColor={theme.colors.primary}
                  maximumTrackTintColor={theme.colors.outline}
                  thumbTintColor={theme.colors.primary}
                />
              </View>
            </List.Accordion>

            <Divider />

            {/* Duration Range */}
            <List.Accordion
              title={`Duration: ${Math.round((activeFilters.durationRange?.[0] || 0) / 60)}h - ${Math.round((activeFilters.durationRange?.[1] || SEARCH_CONSTANTS.MAX_DURATION_MIN) / 60)}h`}
              left={props => (
                <MaterialCommunityIcons 
                  name="clock-outline" 
                  size={24} 
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
            >
              <View style={styles.sliderContainer}>
                <Text style={[styles.sliderLabel, { color: theme.colors.onSurface }]}>
                  Minimum Duration: {Math.round((activeFilters.durationRange?.[0] || 0) / 60)}h
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={0}
                  maximumValue={SEARCH_CONSTANTS.MAX_DURATION_MIN}
                  value={activeFilters.durationRange?.[0] || 0}
                  onValueChange={(value) => {
                    const currentRange = activeFilters.durationRange || [0, SEARCH_CONSTANTS.MAX_DURATION_MIN];
                    handleFilterChange('durationRange', [Math.round(value / 60) * 60, currentRange[1]]);
                  }}
                  step={60}
                  minimumTrackTintColor={theme.colors.primary}
                  maximumTrackTintColor={theme.colors.outline}
                  thumbTintColor={theme.colors.primary}
                />
                
                <Text style={[styles.sliderLabel, { color: theme.colors.onSurface }]}>
                  Maximum Duration: {Math.round((activeFilters.durationRange?.[1] || SEARCH_CONSTANTS.MAX_DURATION_MIN) / 60)}h
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={0}
                  maximumValue={SEARCH_CONSTANTS.MAX_DURATION_MIN}
                  value={activeFilters.durationRange?.[1] || SEARCH_CONSTANTS.MAX_DURATION_MIN}
                  onValueChange={(value) => {
                    const currentRange = activeFilters.durationRange || [0, SEARCH_CONSTANTS.MAX_DURATION_MIN];
                    handleFilterChange('durationRange', [currentRange[0], Math.round(value / 60) * 60]);
                  }}
                  step={60}
                  minimumTrackTintColor={theme.colors.primary}
                  maximumTrackTintColor={theme.colors.outline}
                  thumbTintColor={theme.colors.primary}
                />
              </View>
            </List.Accordion>

            <Divider />

            {/* Kitchen Types */}
            {renderMultiSelectChips(KITCHEN_TYPES, 'kitchenTypes', 'Cuisine Types')}
            
            <Divider />

            {/* Event Types */}
            {renderMultiSelectChips(EVENT_TYPES, 'eventTypes', 'Event Types')}
            
            <Divider />

            {/* Beverage Types */}
            {renderMultiSelectChips(BEVERAGE_TYPES, 'beverageTypes', 'Beverages')}
            
            <Divider />

            {/* Location Types */}
            {renderMultiSelectChips(LOCATION_TYPES, 'locationTypes', 'Location Types')}
            
            <Divider />

            {/* Dietary Restrictions */}
            {renderMultiSelectChips(INTOLERANCES, 'intolerances', 'Dietary Restrictions')}
            
            <Divider />

            {/* Additional Options */}
            <List.Accordion
              title="Additional Options"
              left={props => (
                <MaterialCommunityIcons 
                  name="tune" 
                  size={24} 
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
            >
              <View style={styles.switchContainer}>
                <View style={styles.switchRow}>
                  <Text style={[styles.switchLabel, { color: theme.colors.onSurface }]}>
                    Only show available events
                  </Text>
                  <Switch
                    value={activeFilters.hasAvailableSpots || false}
                    onValueChange={(value) => handleFilterChange('hasAvailableSpots', value)}
                    color={theme.colors.primary}
                  />
                </View>
                
                <View style={styles.switchRow}>
                  <Text style={[styles.switchLabel, { color: theme.colors.onSurface }]}>
                    Include private events
                  </Text>
                  <Switch
                    value={activeFilters.isPrivate || false}
                    onValueChange={(value) => handleFilterChange('isPrivate', value)}
                    color={theme.colors.primary}
                  />
                </View>
                
                <View style={styles.switchRow}>
                  <Text style={[styles.switchLabel, { color: theme.colors.onSurface }]}>
                    Include mature content (18+)
                  </Text>
                  <Switch
                    value={activeFilters.includeNSFW || false}
                    onValueChange={(value) => handleFilterChange('includeNSFW', value)}
                    color={theme.colors.primary}
                  />
                </View>
              </View>
            </List.Accordion>
          </ScrollView>

          {/* Footer */}
          <Surface style={[styles.footer, { backgroundColor: theme.colors.surface }]}>
            <Button
              mode="outlined"
              onPress={handleCancel}
              style={styles.footerButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleApply}
              style={styles.footerButton}
              buttonColor={theme.colors.primary}
            >
              Apply Filters
            </Button>
          </Surface>

          {/* Date Pickers */}
          {showStartDatePicker && (
            <DateTimePicker
              value={activeFilters.dateRange?.[0] ? new Date(activeFilters.dateRange[0]) : new Date()}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowStartDatePicker(false);
                if (selectedDate) {
                  const currentRange = activeFilters.dateRange || [undefined, undefined];
                  handleFilterChange('dateRange', [selectedDate.toISOString(), currentRange[1]]);
                }
              }}
            />
          )}

          {showEndDatePicker && (
            <DateTimePicker
              value={activeFilters.dateRange?.[1] ? new Date(activeFilters.dateRange[1]) : new Date()}
              mode="date"
              display="default"
              onChange={(event, selectedDate) => {
                setShowEndDatePicker(false);
                if (selectedDate) {
                  const currentRange = activeFilters.dateRange || [undefined, undefined];
                  handleFilterChange('dateRange', [currentRange[0], selectedDate.toISOString()]);
                }
              }}
            />
          )}
        </SafeAreaView>
      </Modal>
    </Portal>
  );
}

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
  },
  header: {
    paddingVertical: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  filterCount: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  filterCountText: {
    fontSize: 14,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
  },
  chip: {
    marginRight: 8,
    marginBottom: 8,
  },
  chipText: {
    fontSize: 14,
  },
  sliderContainer: {
    padding: 16,
  },
  sliderLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  slider: {
    width: '100%',
    height: 40,
    marginBottom: 16,
  },
  dateContainer: {
    padding: 16,
    gap: 12,
  },
  dateButton: {
    marginVertical: 4,
  },
  switchContainer: {
    padding: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 14,
    flex: 1,
    marginRight: 16,
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    elevation: 8,
  },
  footerButton: {
    flex: 1,
  },
});
