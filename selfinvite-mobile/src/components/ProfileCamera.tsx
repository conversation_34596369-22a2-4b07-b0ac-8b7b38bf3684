import React, { useState, useRef, useCallback } from 'react';
import { View, StyleSheet, Alert, Modal, Dimensions } from 'react-native';
import { Button, Surface, Text, IconButton, useTheme, Avatar } from 'react-native-paper';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';

interface ProfileCameraProps {
  currentAvatar?: string;
  onPhotoTaken: (uri: string) => void;
  onPhotoSelected: (uri: string) => void;
  disabled?: boolean;
}

export const ProfileCamera: React.FC<ProfileCameraProps> = ({
  currentAvatar,
  onPhotoTaken,
  onPhotoSelected,
  disabled = false,
}) => {
  const theme = useTheme();
  const [showCamera, setShowCamera] = useState(false);
  const [cameraType, setCameraType] = useState<CameraType>('front');
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef<CameraView>(null);

  const requestCameraPermissions = useCallback(async () => {
    if (!permission) {
      const result = await requestPermission();
      return result.granted;
    }
    return permission.granted;
  }, [permission, requestPermission]);

  const requestMediaPermissions = useCallback(async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to select photos.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  }, []);

  const takePhoto = useCallback(async () => {
    const hasPermission = await requestCameraPermissions();
    if (!hasPermission) {
      Alert.alert(
        'Permission Required',
        'Please grant camera permissions to take photos.',
        [{ text: 'OK' }]
      );
      return;
    }

    setShowCamera(true);
  }, [requestCameraPermissions]);

  const selectFromLibrary = useCallback(async () => {
    const hasPermission = await requestMediaPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets?.[0]) {
        const asset = result.assets[0];
        
        // Process the image to make it square and optimize
        const processedImage = await ImageManipulator.manipulateAsync(
          asset.uri,
          [
            { resize: { width: 400, height: 400 } },
          ],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );

        onPhotoSelected(processedImage.uri);
      }
    } catch (error) {
      console.error('Error selecting photo:', error);
      Alert.alert('Error', 'Failed to select photo. Please try again.');
    }
  }, [requestMediaPermissions, onPhotoSelected]);

  const handleCameraCapture = useCallback(async () => {
    if (!cameraRef.current) return;

    try {
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo) {
        // Process the image to make it square and optimize
        const processedImage = await ImageManipulator.manipulateAsync(
          photo.uri,
          [
            { resize: { width: 400, height: 400 } },
          ],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );

        onPhotoTaken(processedImage.uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setShowCamera(false);
    }
  }, [onPhotoTaken]);

  const showPhotoOptions = useCallback(() => {
    Alert.alert(
      'Update Profile Photo',
      'Choose how you want to update your profile photo',
      [
        { text: 'Take Photo', onPress: takePhoto },
        { text: 'Choose from Library', onPress: selectFromLibrary },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, [takePhoto, selectFromLibrary]);

  return (
    <View style={styles.container}>
      <Surface style={[styles.avatarContainer, { backgroundColor: theme.colors.surface }]}>
        <Avatar.Image
          size={120}
          source={{ uri: currentAvatar }}
          style={styles.avatar}
        />
        
        <IconButton
          icon="camera-plus"
          iconColor={theme.colors.primary}
          size={24}
          onPress={showPhotoOptions}
          disabled={disabled}
          style={[styles.cameraButton, { backgroundColor: theme.colors.surface }]}
        />
      </Surface>

      <Text style={[styles.instruction, { color: theme.colors.onSurface }]}>
        Tap the camera icon to update your profile photo
      </Text>

      {/* Camera Modal */}
      <Modal
        visible={showCamera}
        animationType="slide"
        onRequestClose={() => setShowCamera(false)}
      >
        <View style={styles.cameraContainer}>
          <CameraView
            ref={cameraRef}
            style={styles.camera}
            facing={cameraType}
          >
            <View style={styles.cameraControls}>
              <IconButton
                icon="close"
                iconColor="white"
                size={30}
                onPress={() => setShowCamera(false)}
                style={styles.closeButton}
              />
              
              <View style={styles.cameraActions}>
                <IconButton
                  icon="camera-flip"
                  iconColor="white"
                  size={30}
                  onPress={() => setCameraType(cameraType === 'back' ? 'front' : 'back')}
                />
                
                <View style={styles.captureContainer}>
                  <IconButton
                    icon="camera"
                    iconColor="white"
                    size={60}
                    onPress={handleCameraCapture}
                    style={styles.captureButton}
                  />
                </View>
              </View>
            </View>
          </CameraView>
        </View>
      </Modal>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 20,
  },
  avatarContainer: {
    position: 'relative',
    borderRadius: 60,
    padding: 4,
    elevation: 4,
  },
  avatar: {
    backgroundColor: '#f0f0f0',
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    elevation: 2,
  },
  instruction: {
    marginTop: 12,
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
  },
  cameraContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  cameraControls: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  closeButton: {
    alignSelf: 'flex-start',
  },
  cameraActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  captureContainer: {
    flex: 1,
    alignItems: 'center',
  },
  captureButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 40,
  },
});

export default ProfileCamera;
