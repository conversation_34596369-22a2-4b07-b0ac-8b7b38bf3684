import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useNotifications } from '../hooks/useNotifications';
import logger from '../common/logger';

interface NotificationContextType {
  isInitialized: boolean;
  isEnabled: boolean;
  pushToken: string | null;
  preferences: any;
  initialize: () => Promise<void>;
  requestPermissions: () => Promise<boolean>;
  clearBadgeCount: () => Promise<void>;
  updatePreferences: (preferences: any) => Promise<void>;
  scheduleEventReminder: (eventId: string, eventTitle: string, eventDate: Date) => Promise<string>;
  scheduleBookingReminder: (bookingId: string, eventTitle: string, eventDate: Date) => Promise<string>;
}

const NotificationContext = createContext<NotificationContextType | null>(null);

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const notificationHook = useNotifications();

  // Initialize notifications when the app starts
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        await notificationHook.initialize();
        logger.info('Notification provider initialized');
      } catch (error) {
        logger.error('Failed to initialize notification provider:', error);
      }
    };

    initializeNotifications();
  }, []);

  return (
    <NotificationContext.Provider value={notificationHook}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotificationContext(): NotificationContextType {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
}
