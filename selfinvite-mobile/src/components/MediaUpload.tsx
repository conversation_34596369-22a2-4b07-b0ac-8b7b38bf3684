import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import { useMediaUpload } from '../hooks/useMediaUpload';
import type { UploadResult } from '../utils/mediaUploadService';

const { width } = Dimensions.get('window');
const ITEM_WIDTH = (width - 60) / 3; // 3 items per row with margins

interface MediaUploadProps {
  eventId: string;
  onMediaUploaded?: (media: UploadResult[]) => void;
  onMediaRemoved?: (media: UploadResult[]) => void;
  maxFiles?: number;
  allowedTypes?: ('image' | 'video')[];
  className?: string;
}

interface MediaItem {
  uri: string;
  type: 'image' | 'video';
  name: string;
  isUploading?: boolean;
  uploadProgress?: number;
  uploadResult?: UploadResult;
  error?: string;
}

export const MediaUpload: React.FC<MediaUploadProps> = ({
  eventId,
  onMediaUploaded,
  onMediaRemoved,
  maxFiles = 10,
  allowedTypes = ['image', 'video'],
  className,
}) => {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const { state, uploadMedia, clearUploads } = useMediaUpload();

  const requestPermissions = useCallback(async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant camera roll permissions to select media.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  }, []);

  const selectMedia = useCallback(async () => {
    if (mediaItems.length >= maxFiles) {
      Alert.alert('Maximum Files Reached', `You can only upload up to ${maxFiles} files.`);
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: allowedTypes.includes('video') 
          ? ImagePicker.MediaTypeOptions.All 
          : ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets) {
        const newMediaItems: MediaItem[] = result.assets.map(asset => ({
          uri: asset.uri,
          type: asset.type === 'video' ? 'video' : 'image',
          name: asset.fileName || `media_${Date.now()}`,
          isUploading: false,
        }));

        setMediaItems(prev => [...prev, ...newMediaItems]);
      }
    } catch (error) {
      console.error('Error selecting media:', error);
      Alert.alert('Error', 'Failed to select media. Please try again.');
    }
  }, [mediaItems.length, maxFiles, allowedTypes, requestPermissions]);

  const uploadSelectedMedia = useCallback(async () => {
    const itemsToUpload = mediaItems.filter(item => !item.uploadResult && !item.error);
    
    if (itemsToUpload.length === 0) {
      Alert.alert('No Media to Upload', 'Please select media files first.');
      return;
    }

    try {
      const uris = itemsToUpload.map(item => item.uri);
      const results = await uploadMedia(uris, eventId);
      
      // Update media items with upload results
      setMediaItems(prev => prev.map(item => {
        const result = results.find(r => r.path.includes(item.name) || r.url.includes(item.name));
        if (result) {
          return { ...item, uploadResult: result, isUploading: false, uploadProgress: 100 };
        }
        return item;
      }));

      onMediaUploaded?.(results);
      Alert.alert('Success', 'Media uploaded successfully!');
    } catch (error) {
      console.error('Upload error:', error);
      Alert.alert('Upload Failed', 'Failed to upload media. Please try again.');
    }
  }, [mediaItems, uploadMedia, eventId, onMediaUploaded]);

  const removeMedia = useCallback((index: number) => {
    const item = mediaItems[index];
    setMediaItems(prev => prev.filter((_, i) => i !== index));
    
    if (item.uploadResult) {
      onMediaRemoved?.(mediaItems.filter(m => m.uploadResult).map(m => m.uploadResult!));
    }
  }, [mediaItems, onMediaRemoved]);

  const retryUpload = useCallback(async (index: number) => {
    const item = mediaItems[index];
    if (!item) return;

    setMediaItems(prev => prev.map((m, i) => 
      i === index ? { ...m, isUploading: true, error: undefined } : m
    ));

    try {
      const results = await uploadMedia([item.uri], eventId);
      const result = results[0];
      
      setMediaItems(prev => prev.map((m, i) => 
        i === index ? { ...m, uploadResult: result, isUploading: false, uploadProgress: 100 } : m
      ));
    } catch (error) {
      setMediaItems(prev => prev.map((m, i) => 
        i === index ? { ...m, isUploading: false, error: 'Upload failed' } : m
      ));
    }
  }, [mediaItems, uploadMedia, eventId]);

  const renderMediaItem = useCallback((item: MediaItem, index: number) => {
    const isUploaded = !!item.uploadResult;
    const hasError = !!item.error;

    return (
      <View key={`${item.uri}-${index}`} style={styles.mediaItem}>
        <View style={styles.mediaContainer}>
          {item.type === 'image' ? (
            <Image source={{ uri: item.uri }} style={styles.mediaPreview} />
          ) : (
            <View style={styles.videoPreview}>
              <Ionicons name="videocam" size={24} color="#666" />
              <Text style={styles.videoText}>Video</Text>
            </View>
          )}
          
          {/* Upload Progress Overlay */}
          {item.isUploading && (
            <View style={styles.progressOverlay}>
              <ActivityIndicator size="small" color="#fff" />
              <Text style={styles.progressText}>
                {item.uploadProgress ? `${item.uploadProgress}%` : 'Uploading...'}
              </Text>
            </View>
          )}

          {/* Error Overlay */}
          {hasError && (
            <View style={styles.errorOverlay}>
              <Ionicons name="alert-circle" size={20} color="#ff4444" />
              <Text style={styles.errorText}>Failed</Text>
            </View>
          )}

          {/* Success Overlay */}
          {isUploaded && (
            <View style={styles.successOverlay}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
            </View>
          )}

          {/* Remove Button */}
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => removeMedia(index)}
          >
            <Ionicons name="close-circle" size={20} color="#ff4444" />
          </TouchableOpacity>

          {/* Retry Button */}
          {hasError && (
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => retryUpload(index)}
            >
              <Ionicons name="refresh" size={16} color="#fff" />
            </TouchableOpacity>
          )}
        </View>

        <Text style={styles.mediaName} numberOfLines={1}>
          {item.name}
        </Text>
      </View>
    );
  }, [removeMedia, retryUpload]);

  return (
    <View style={[styles.container, className]}>
      <View style={styles.header}>
        <Text style={styles.title}>Event Media</Text>
        <Text style={styles.subtitle}>
          {mediaItems.length}/{maxFiles} files selected
        </Text>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.mediaList}
        contentContainerStyle={styles.mediaListContent}
      >
        {/* Add Media Button */}
        <TouchableOpacity
          style={[styles.mediaItem, styles.addButton]}
          onPress={selectMedia}
          disabled={mediaItems.length >= maxFiles}
        >
          <Ionicons 
            name="add" 
            size={32} 
            color={mediaItems.length >= maxFiles ? "#ccc" : "#007AFF"} 
          />
          <Text style={[styles.addButtonText, 
            mediaItems.length >= maxFiles && styles.addButtonTextDisabled
          ]}>
            Add Media
          </Text>
        </TouchableOpacity>

        {/* Media Items */}
        {mediaItems.map(renderMediaItem)}
      </ScrollView>

      {/* Upload Button */}
      {mediaItems.length > 0 && (
        <View style={styles.uploadSection}>
          <TouchableOpacity
            style={[
              styles.uploadButton,
              state.isUploading && styles.uploadButtonDisabled
            ]}
            onPress={uploadSelectedMedia}
            disabled={state.isUploading}
          >
            {state.isUploading ? (
              <>
                <ActivityIndicator size="small" color="#fff" />
                <Text style={styles.uploadButtonText}>
                  Uploading... {state.progress.percentage}%
                </Text>
              </>
            ) : (
              <>
                <Ionicons name="cloud-upload" size={20} color="#fff" />
                <Text style={styles.uploadButtonText}>
                  Upload {mediaItems.filter(item => !item.uploadResult).length} Files
                </Text>
              </>
            )}
          </TouchableOpacity>

          {state.errors.length > 0 && (
            <Text style={styles.errorMessage}>
              {state.errors[state.errors.length - 1]}
            </Text>
          )}
        </View>
      )}

      {/* Upload Progress */}
      {state.isUploading && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${state.progress.percentage}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {state.progress.loaded} / {state.progress.total} bytes
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
  mediaList: {
    marginBottom: 16,
  },
  mediaListContent: {
    paddingRight: 16,
  },
  mediaItem: {
    width: ITEM_WIDTH,
    marginRight: 12,
    alignItems: 'center',
  },
  mediaContainer: {
    width: ITEM_WIDTH,
    height: ITEM_WIDTH,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#f5f5f5',
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
  },
  videoPreview: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  videoText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  addButton: {
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  addButtonText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    textAlign: 'center',
  },
  addButtonTextDisabled: {
    color: '#ccc',
  },
  mediaName: {
    fontSize: 11,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
    maxWidth: ITEM_WIDTH,
  },
  progressOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressText: {
    color: '#fff',
    fontSize: 10,
    marginTop: 4,
  },
  errorOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 10,
    color: '#ff4444',
    marginLeft: 2,
  },
  successOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 4,
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    padding: 2,
  },
  retryButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 4,
  },
  uploadSection: {
    marginTop: 16,
  },
  uploadButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadButtonDisabled: {
    backgroundColor: '#ccc',
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  errorMessage: {
    color: '#ff4444',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  progressContainer: {
    marginTop: 12,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#f0f0f0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
  },
});

export default MediaUpload;
