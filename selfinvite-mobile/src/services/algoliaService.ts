import { algoliasearch, SearchClient } from 'algoliasearch';
import type { Event, MediaProperty } from '../types/event';
import type { SearchFilters, SearchResults, SearchSortOptions } from '../types/search';
import log from '../common/logger';

// Algolia configuration
const ALGOLIA_CONFIG = {
  APP_ID: process.env.EXPO_PUBLIC_ALGOLIA_APP_ID || '',
  SEARCH_API_KEY: process.env.EXPO_PUBLIC_ALGOLIA_SEARCH_API_KEY || '',
  INDEX_NAME: 'event_offers',
};

// Initialize Algolia search client
const client = algoliasearch(
  ALGOLIA_CONFIG.APP_ID,
  ALGOLIA_CONFIG.SEARCH_API_KEY
);

// Event Offer interface matching your backend model
interface AlgoliaEventOffer {
  objectID: string;
  host_user_id: string;
  title: string;
  description?: string;
  max_participants: number;
  type_kitchens: string[];
  type_beverages: string[];
  type_events: string[];
  type_locations: string[];
  type_intolerances: string[];
  city?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  event_date: string;
  price_per_person: number;
  duration_minutes: number;
  medias: MediaProperty[];
  status: string;
  created_at: string;
  updated_at: string;
  _geoloc?: {
    lat: number;
    lng: number;
  };
}

// Algolia search service class
export class AlgoliaSearchService {
  private client: SearchClient;
  private indexName: string;

  constructor() {
    if (!ALGOLIA_CONFIG.APP_ID || !ALGOLIA_CONFIG.SEARCH_API_KEY) {
      throw new Error('Algolia configuration is missing. Please check your environment variables.');
    }
    
    this.client = client;
    this.indexName = ALGOLIA_CONFIG.INDEX_NAME;
  }

  // Main search function with comprehensive filtering
  async searchEvents(
    query: string = '',
    filters: SearchFilters = {},
    options: {
      page?: number;
      limit?: number;
      sort?: SearchSortOptions[];
      facets?: string[];
    } = {}
  ): Promise<SearchResults> {
    try {
      log.info('Algolia search request:', { query, filters, options });
      
      // Build Algolia search parameters
      const searchParams = {
        query: query || '',
        hitsPerPage: options.limit || 20,
        page: options.page || 0,
        // ...this.buildAlgoliaFilters(filters),
        ...this.buildAlgoliaSort(options.sort),
        facets: options.facets || [
          'type_kitchens',
          'type_beverages', 
          'type_events',
          'type_locations',
          'type_intolerances',
          'city',
          'country',
        ],
        attributesToRetrieve: [
          'objectID',
          'host_user_id',
          'title',
          'description',
          'max_participants',
          'type_kitchens',
          'type_beverages',
          'type_events',
          'type_locations',
          'type_intolerances',
          'city',
          'country',
          'latitude',
          'longitude',
          'event_date',
          'price_per_person',
          'duration_minutes',
          'medias',
          'status',
          'created_at',
          'updated_at',
        ],
      };

      log.info('Algolia search request:', searchParams);
      
      const response = await this.client.searchSingleIndex({
        indexName: this.indexName,
        searchParams: searchParams
      });
      
      log.debug('Algolia search response:', response);
      
      // Transform Algolia response to match SearchResults interface
      return this.transformAlgoliaResponse(response, query, options.sort);
    } catch (error) {
      log.error('Algolia search error:', error);
      throw new Error('Failed to search events');
    }
  }

  // Build Algolia filters from SearchFilters
  private buildAlgoliaFilters(filters: SearchFilters): any {
    const algoliaFilters: any = {};

    // Location filters
    if (filters.city) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`city:${filters.city}`);
    }

    if (filters.country) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`country:${filters.country}`);
    }

    // Category filters (arrays for multi-select)
    if (filters.kitchenTypes && filters.kitchenTypes.length > 0) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`type_kitchens:${filters.kitchenTypes.join(',')}`);
    }

    if (filters.beverageTypes && filters.beverageTypes.length > 0) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`type_beverages:${filters.beverageTypes.join(',')}`);
    }

    if (filters.eventTypes && filters.eventTypes.length > 0) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`type_events:${filters.eventTypes.join(',')}`);
    }

    if (filters.locationTypes && filters.locationTypes.length > 0) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`type_locations:${filters.locationTypes.join(',')}`);
    }

    if (filters.intolerances && filters.intolerances.length > 0) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`type_intolerances:${filters.intolerances.join(',')}`);
    }

    // Numeric filters
    if (filters.priceRange) {
      const [minPrice, maxPrice] = filters.priceRange;
      if (minPrice !== undefined || maxPrice !== undefined) {
        algoliaFilters.numericFilters = algoliaFilters.numericFilters || [];
        let priceFilter = 'price_per_person';
        if (minPrice !== undefined && maxPrice !== undefined) {
          priceFilter += ` >= ${minPrice} AND price_per_person <= ${maxPrice}`;
        } else if (minPrice !== undefined) {
          priceFilter += ` >= ${minPrice}`;
        } else if (maxPrice !== undefined) {
          priceFilter += ` <= ${maxPrice}`;
        }
        algoliaFilters.numericFilters.push(priceFilter);
      }
    }

    if (filters.durationRange) {
      const [minDuration, maxDuration] = filters.durationRange;
      if (minDuration !== undefined || maxDuration !== undefined) {
        algoliaFilters.numericFilters = algoliaFilters.numericFilters || [];
        let durationFilter = 'duration_minutes';
        if (minDuration !== undefined && maxDuration !== undefined) {
          durationFilter += ` >= ${minDuration} AND duration_minutes <= ${maxDuration}`;
        } else if (minDuration !== undefined) {
          durationFilter += ` >= ${minDuration}`;
        } else if (maxDuration !== undefined) {
          durationFilter += ` <= ${maxDuration}`;
        }
        algoliaFilters.numericFilters.push(durationFilter);
      }
    }

    if (filters.maxParticipants) {
      algoliaFilters.numericFilters = algoliaFilters.numericFilters || [];
      algoliaFilters.numericFilters.push(`max_participants >= ${filters.maxParticipants}`);
    }

    // Status filters
    if (filters.status && filters.status.length > 0) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`status:${filters.status.join(',')}`);
    }

    // Date filters
    if (filters.dateRange) {
      const [startDate, endDate] = filters.dateRange;
      if (startDate || endDate) {
        algoliaFilters.numericFilters = algoliaFilters.numericFilters || [];
        const now = Math.floor(Date.now() / 1000);
        let dateFilter = 'event_date';
        if (startDate && endDate) {
          const startTimestamp = Math.floor(new Date(startDate).getTime() / 1000);
          const endTimestamp = Math.floor(new Date(endDate).getTime() / 1000);
          dateFilter += ` >= ${startTimestamp} AND event_date <= ${endTimestamp}`;
        } else if (startDate) {
          const startTimestamp = Math.floor(new Date(startDate).getTime() / 1000);
          dateFilter += ` >= ${startTimestamp}`;
        } else if (endDate) {
          const endTimestamp = Math.floor(new Date(endDate).getTime() / 1000);
          dateFilter += ` <= ${endTimestamp}`;
        }
        algoliaFilters.numericFilters.push(dateFilter);
      }
    }

    // Geographic filters
    if (filters.coordinates && filters.radius) {
      algoliaFilters.aroundLatLng = `${filters.coordinates.lat},${filters.coordinates.lng}`;
      algoliaFilters.aroundRadius = filters.radius * 1000; // Convert km to meters
    }

    // Boolean filters
    if (filters.hasAvailableSpots !== undefined) {
      algoliaFilters.numericFilters = algoliaFilters.numericFilters || [];
      if (filters.hasAvailableSpots) {
        algoliaFilters.numericFilters.push('max_participants > 0');
      }
    }

    if (filters.isPrivate !== undefined) {
      algoliaFilters.facetFilters = algoliaFilters.facetFilters || [];
      algoliaFilters.facetFilters.push(`is_private:${filters.isPrivate}`);
    }

    return algoliaFilters;
  }

  // Build Algolia sort parameters
  private buildAlgoliaSort(sortOptions?: SearchSortOptions[]): any {
    // For now, we'll disable sorting to avoid the "Unknown parameter: sort" error
    // Algolia sorting needs to be configured on the index level
    // We can implement client-side sorting as a fallback
    return {};
  }

  // Transform Algolia response to SearchResults format
  private transformAlgoliaResponse(algoliaResponse: any, query: string, sortOptions?: SearchSortOptions[]): SearchResults {
    let hits = algoliaResponse.hits.map((hit: AlgoliaEventOffer) => this.transformAlgoliaHit(hit));
    
    // Apply client-side sorting if sort options are provided
    if (sortOptions && sortOptions.length > 0 && sortOptions[0]) {
      hits = this.applyClientSideSorting(hits, sortOptions[0]);
    }
    
    return {
      hits,
      totalHits: algoliaResponse.nbHits,
      totalPages: algoliaResponse.nbPages,
      currentPage: algoliaResponse.page,
      processingTimeMs: algoliaResponse.processingTimeMS,
      facetDistribution: algoliaResponse.facets || {},
      query,
    };
  }

  // Apply client-side sorting to search results
  private applyClientSideSorting(events: Event[], sortOption: SearchSortOptions): Event[] {
    return events.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortOption.field) {
        case 'event_date':
          aValue = new Date(a.event_date).getTime();
          bValue = new Date(b.event_date).getTime();
          break;
        case 'price_per_person':
          aValue = a.price_per_person;
          bValue = b.price_per_person;
          break;
        case 'created_at':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'distance':
          // Distance sorting should be handled by the geolocation service
          // For now, return as-is
          return 0;
        default:
          // Default to event_date
          aValue = new Date(a.event_date).getTime();
          bValue = new Date(b.event_date).getTime();
      }

      if (sortOption.direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }

  // Transform individual Algolia hit to Event format
  private transformAlgoliaHit(hit: AlgoliaEventOffer): Event {
    return {
      id: hit.objectID,
      title: hit.title,
      description: hit.description || '',
      event_date: hit.event_date,
      startTime: hit.event_date, // Use event_date as startTime for now
      street: '', // Not available in Algolia data
      city: hit.city || '',
      postal_code: '', // Not available in Algolia data
      country: hit.country || '',
      longitude: hit.longitude || 0,
      latitude: hit.latitude || 0,
      host_user_id: hit.host_user_id,
      host: {
        id: hit.host_user_id,
        username: '', // Not available in Algolia data
      },
      max_participants: hit.max_participants,
      currentParticipants: 0, // Not available in Algolia data
      type_kitchens: hit.type_kitchens,
      type_events: hit.type_events,
      type_beverages: hit.type_beverages,
      type_intolerances: hit.type_intolerances,
      type_locations: hit.type_locations,
      price_per_person: hit.price_per_person,
      duration_minutes: hit.duration_minutes,
      medias: hit.medias.map(media => media as MediaProperty),
      status: hit.status as 'draft' | 'published' | 'cancelled' | 'completed',
      isPrivate: false, // Not available in Algolia data
      createdAt: hit.created_at,
      updatedAt: hit.updated_at,
    };
  }

  // Search with price range filter
  async searchByPriceRange(
    minPrice: number, 
    maxPrice: number, 
    query?: string
  ): Promise<Event[]> {
    const result = await this.searchEvents(query, {
      priceRange: [minPrice, maxPrice],
    });
    return result.hits;
  }

  // Search by location (city)
  async searchByCity(city: string, query?: string): Promise<Event[]> {
    const result = await this.searchEvents(query, { city });
    return result.hits;
  }

  // Search by event type
  async searchByEventType(eventTypes: string[], query?: string): Promise<Event[]> {
    const result = await this.searchEvents(query, { eventTypes });
    return result.hits;
  }

  // Search nearby events
  async searchNearby(
    lat: number,
    lng: number,
    radius: number = 10, // km
    filters: Omit<SearchFilters, 'coordinates' | 'radius'> = {},
    limit: number = 20
  ): Promise<SearchResults> {
    return this.searchEvents('', {
      ...filters,
      coordinates: { lat, lng },
      radius,
    }, { limit });
  }

  // Get search suggestions/autocomplete
  async getSearchSuggestions(query: string, limit: number = 5): Promise<any[]> {
    try {
      if (!query || query.length < 2) return [];

      const response = await this.client.searchSingleIndex({
        indexName: this.indexName,
        searchParams: {
          query,
          hitsPerPage: limit,
          attributesToRetrieve: ['title', 'city', 'type_kitchens'],
          attributesToHighlight: ['title', 'city'],
        }
      });

      return response.hits.map((hit: any) => ({
        text: hit.title,
        highlighted: hit._highlightResult?.title?.value || hit.title,
        type: 'event',
        metadata: {
          city: hit.city,
          cuisine: hit.type_kitchens?.[0],
        },
      }));
    } catch (error) {
      log.error('Search suggestions error:', error);
      return [];
    }
  }

  // Health check for search service
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.searchSingleIndex({
        indexName: this.indexName,
        searchParams: { query: '', hitsPerPage: 1 }
      });
      return true;
    } catch (error) {
      log.error('Algolia health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const algoliaSearchService = new AlgoliaSearchService();

// Export configuration for other modules
export { ALGOLIA_CONFIG };
