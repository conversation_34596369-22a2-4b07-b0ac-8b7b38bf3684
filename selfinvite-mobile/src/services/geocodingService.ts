import * as Location from 'expo-location';
import logger from '@/common/logger';
import type { 
  GeocodedAddress, 
  AddressSearchResult, 
  GeocodingResponse 
} from '../types/address';

// Mapbox Geocoding API service using HTTP requests (Expo Go compatible)
class GeocodingService {
  private readonly baseUrl = 'https://api.mapbox.com/geocoding/v5/mapbox.places';
  private readonly accessToken: string;

  constructor() {
    // Get Mapbox access token from environment
    this.accessToken = process.env.EXPO_PUBLIC_MAPBOX_ACCESS_TOKEN || '';
    if (!this.accessToken) {
      logger.warn('Mapbox access token not found in environment variables. Geocoding features will not work.');
    }
  }

  /**
   * Search for addresses using Mapbox Geocoding API
   */
  async searchAddresses(query: string, limit: number = 5): Promise<AddressSearchResult[]> {
    if (!this.accessToken) {
      throw new Error('Mapbox access token not configured');
    }

    if (!query.trim()) {
      return [];
    }

    try {
      const encodedQuery = encodeURIComponent(query);
      const url = `${this.baseUrl}/${encodedQuery}.json?access_token=${this.accessToken}&limit=${limit}&types=address,poi,place,locality,neighborhood,district,postcode,region,country`;

      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Mapbox API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.features || !Array.isArray(data.features)) {
        logger.warn('Invalid response format from Mapbox API', data);
        return [];
      }

      return data.features.map((feature: any) => ({
        id: feature.id,
        placeName: feature.place_name,
        formattedAddress: feature.place_name,
        coordinates: feature.center, // [longitude, latitude]
        street: this.extractStreetFromFeature(feature),
        city: this.extractContextValue(feature.context, 'place'),
        country: this.extractContextValue(feature.context, 'country'),
        postalCode: this.extractContextValue(feature.context, 'postcode'),
        context: feature.context?.map((ctx: any) => ({
          id: ctx.id,
          text: ctx.text
        })),
        bbox: feature.bbox
      }));

    } catch (error) {
      logger.error('Error searching addresses:', error);
      throw new Error('Failed to search addresses. Please try again.');
    }
  }

  /**
   * Reverse geocode coordinates to get address
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<GeocodedAddress | null> {
    if (!this.accessToken) {
      throw new Error('Mapbox access token not configured');
    }

    try {
      const url = `${this.baseUrl}/${longitude},${latitude}.json?access_token=${this.accessToken}&types=address,poi,place,locality,neighborhood,district,postcode,region,country`;

      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Mapbox API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.features || data.features.length === 0) {
        logger.warn('No address found for coordinates', { latitude, longitude });
        return null;
      }

      const feature = data.features[0];
      
      const city = this.extractContextValue(feature.context, 'place');
      const country = this.extractContextValue(feature.context, 'country');
      const postalCode = this.extractContextValue(feature.context, 'postcode');
      const street = this.extractStreetFromFeature(feature);
      const context = feature.context?.map((ctx: any) => ({
        id: ctx.id,
        text: ctx.text
      }));

      return {
        latitude,
        longitude,
        formattedAddress: feature.place_name,
        ...(city && { city }),
        ...(country && { country }),
        ...(postalCode && { postalCode }),
        ...(street && { street }),
        placeName: feature.place_name,
        confidence: feature.relevance,
        ...(context && { context })
      };

    } catch (error) {
      logger.error('Error reverse geocoding:', error);
      throw new Error('Failed to get address for location. Please try again.');
    }
  }

  /**
   * Get current location using Expo Location
   */
  async getCurrentLocation(): Promise<GeocodedAddress> {
    try {
      // Check location permission
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();
        if (newStatus !== 'granted') {
          throw new Error('Location permission denied');
        }
      }

      // Check if location services are enabled
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        throw new Error('Location services are disabled');
      }

      // Get current position
      const { coords } = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const { latitude, longitude } = coords;

      // Reverse geocode to get address
      const address = await this.reverseGeocode(latitude, longitude);
      
      if (!address) {
        // Fallback to basic address if reverse geocoding fails
        return {
          latitude,
          longitude,
          formattedAddress: `Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
          placeName: 'Current Location'
        };
      }

      return address;

    } catch (error) {
      logger.error('Error getting current location:', error);
      throw new Error('Failed to get current location. Please check your location settings.');
    }
  }

  /**
   * Extract specific context value from Mapbox response
   */
  private extractContextValue(context: any[], type: string): string | undefined {
    if (!context || !Array.isArray(context)) {
      return undefined;
    }

    const item = context.find((ctx: any) => ctx.id?.startsWith(type));
    return item?.text;
  }

  /**
   * Extract street information from Mapbox feature
   * Mapbox doesn't always provide separate street fields, so we need to parse intelligently
   */
  private extractStreetFromFeature(feature: any): string | undefined {
    // Method 1: Try to get street from context with 'address' type
    const streetFromContext = this.extractContextValue(feature.context, 'address');
    if (streetFromContext) {
      return streetFromContext;
    }

    // Method 2: Try to get street from properties
    if (feature.properties?.address) {
      return feature.properties.address;
    }

    // Method 3: The 'text' field often contains the street name for address features
    if (feature.text && feature.place_type?.includes('address')) {
      return feature.text;
    }

    // Method 4: Parse from place_name by removing known components
    if (feature.place_name) {
      const placeName = feature.place_name;
      const city = this.extractContextValue(feature.context, 'place');
      const country = this.extractContextValue(feature.context, 'country');
      const postalCode = this.extractContextValue(feature.context, 'postcode');
      const region = this.extractContextValue(feature.context, 'region');
      
      let street = placeName;
      
      // Remove country if present
      if (country && street.includes(country)) {
        street = street.replace(`, ${country}`, '').replace(country, '');
      }
      
      // Remove postal code if present
      if (postalCode && street.includes(postalCode)) {
        street = street.replace(`, ${postalCode}`, '').replace(postalCode, '');
      }
      
      // Remove region if present
      if (region && street.includes(region)) {
        street = street.replace(`, ${region}`, '').replace(region, '');
      }
      
      // Remove city if present
      if (city && street.includes(city)) {
        street = street.replace(`, ${city}`, '').replace(city, '');
      }
      
      // Clean up any remaining commas and trim
      street = street.replace(/^,\s*|,\s*$/g, '').trim();
      
      // Return street if it's not empty and different from the full place name
      if (street && street !== placeName && street.length > 0) {
        return street;
      }
    }

    // Method 5: For POI features, use the text field
    if (feature.place_type?.includes('poi') && feature.text) {
      return feature.text;
    }

    return undefined;
  }

  /**
   * Validate coordinates
   */
  isValidCoordinates(latitude: number, longitude: number): boolean {
    return (
      typeof latitude === 'number' &&
      typeof longitude === 'number' &&
      latitude >= -90 &&
      latitude <= 90 &&
      longitude >= -180 &&
      longitude <= 180 &&
      !isNaN(latitude) &&
      !isNaN(longitude)
    );
  }

  /**
   * Calculate distance between two coordinates (in kilometers)
   */
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * (Math.PI / 180);
    const dLon = (lon2 - lon1) * (Math.PI / 180);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * (Math.PI / 180)) *
        Math.cos(lat2 * (Math.PI / 180)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
}

// Export singleton instance
export const geocodingService = new GeocodingService();
export default geocodingService;
