import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { apiClient } from '../utils/apiClient';
import logger from '../common/logger';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface NotificationToken {
  token: string;
  type: 'expo' | 'fcm';
  deviceId: string;
  platform: 'ios' | 'android';
}

export interface NotificationPreferences {
  messages: boolean;
  bookings: boolean;
  payments: boolean;
  events: boolean;
  marketing: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
}

export interface NotificationData {
  type: 'message' | 'booking' | 'payment' | 'event' | 'marketing';
  priority: 'high' | 'normal' | 'low';
  category: string;
  data?: Record<string, any>;
  deepLink?: string;
}

export interface ScheduledNotification {
  id: string;
  title: string;
  body: string;
  data: NotificationData;
  trigger: Notifications.NotificationTriggerInput;
}

class NotificationService {
  private expoPushToken: string | null = null;
  private fcmToken: string | null = null;
  private isInitialized = false;

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Request permissions
      await this.requestPermissions();
      
      // Get push tokens
      await this.registerForPushNotifications();
      
      // Set up notification listeners
      this.setupNotificationListeners();
      
      this.isInitialized = true;
      logger.info('Notification service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize notification service:', error);
      throw error;
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    if (!Device.isDevice) {
      logger.warn('Must use physical device for push notifications');
      return false;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      logger.warn('Failed to get push token for push notification!');
      return false;
    }

    return true;
  }

  /**
   * Register for push notifications and get tokens
   */
  async registerForPushNotifications(): Promise<void> {
    try {
      // Get Expo push token with better error handling
      const expoToken = await Notifications.getExpoPushTokenAsync({
        projectId: '00d2df2e-0cb4-457f-b775-da1661161e4b', // From your app.json
      });

      this.expoPushToken = expoToken.data;
      logger.info('Expo push token:', this.expoPushToken);

      // Send token to backend
      await this.sendTokenToBackend({
        token: this.expoPushToken,
        type: 'expo',
        deviceId: Device.osInternalBuildId || 'unknown',
        platform: Platform.OS as 'ios' | 'android',
      });

      // Configure notification categories
      await this.setupNotificationCategories();

    } catch (error) {
      // Handle Firebase initialization error more gracefully
      if (error instanceof Error && error.message.includes('Default FirebaseApp is not initialized')) {
        logger.warn('Firebase not initialized - using Expo push notifications only');
        logger.info('To fix this, add google-services.json to your Android project');
        logger.info('Guide: https://docs.expo.dev/push-notifications/fcm-credentials/');

        // Continue without Firebase - Expo push notifications will still work
        // but you won't get FCM tokens for direct Firebase messaging
        return;
      }

      logger.error('Error registering for push notifications:', error);
      throw error;
    }
  }

  /**
   * Send notification token to backend
   */
  private async sendTokenToBackend(tokenData: NotificationToken): Promise<void> {
    try {
      await apiClient.post('/notifications/register-token', tokenData);
      logger.info('Notification token sent to backend');
    } catch (error) {
      logger.error('Failed to send token to backend:', error);
      // Don't throw - this shouldn't block the app
    }
  }

  /**
   * Setup notification categories for rich notifications
   */
  private async setupNotificationCategories(): Promise<void> {
    const categories = [
      {
        identifier: 'MESSAGE',
        actions: [
          {
            identifier: 'REPLY',
            buttonTitle: 'Reply',
            options: { opensAppToForeground: true },
          },
          {
            identifier: 'MARK_READ',
            buttonTitle: 'Mark as Read',
            options: { opensAppToForeground: false },
          },
        ],
      },
      {
        identifier: 'BOOKING',
        actions: [
          {
            identifier: 'VIEW_BOOKING',
            buttonTitle: 'View Booking',
            options: { opensAppToForeground: true },
          },
          {
            identifier: 'RESPOND',
            buttonTitle: 'Respond',
            options: { opensAppToForeground: true },
          },
        ],
      },
      {
        identifier: 'PAYMENT',
        actions: [
          {
            identifier: 'VIEW_PAYMENT',
            buttonTitle: 'View Details',
            options: { opensAppToForeground: true },
          },
        ],
      },
    ];

    await Notifications.setNotificationCategoryAsync('MESSAGE', categories[0]?.actions || []);
    await Notifications.setNotificationCategoryAsync('BOOKING', categories[1]?.actions || []);
    await Notifications.setNotificationCategoryAsync('PAYMENT', categories[2]?.actions || []);
  }

  /**
   * Setup notification event listeners
   */
  private setupNotificationListeners(): void {
    // Handle notification received while app is running
    Notifications.addNotificationReceivedListener(this.handleNotificationReceived);
    
    // Handle notification tapped
    Notifications.addNotificationResponseReceivedListener(this.handleNotificationResponse);
  }

  /**
   * Handle notification received
   */
  private handleNotificationReceived = (notification: Notifications.Notification) => {
    logger.info('Notification received:', notification);
    
    // Update app badge count
    this.updateBadgeCount();
    
    // Handle different notification types
    const data = notification.request.content.data as unknown as NotificationData;
    if (data) {
      this.handleNotificationByType(data);
    }
  };

  /**
   * Handle notification response (tapped)
   */
  private handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    logger.info('Notification response:', response);
    
    const data = response.notification.request.content.data as unknown as NotificationData;
    if (!data) return;
    
    // Handle deep linking
    if (data.deepLink) {
      this.handleDeepLink(data.deepLink);
    }
    
    // Handle action responses
    if (response.actionIdentifier) {
      this.handleNotificationAction(response.actionIdentifier, data);
    }
  };

  /**
   * Handle notification by type
   */
  private handleNotificationByType(data: NotificationData): void {
    switch (data.type) {
      case 'message':
        // Update message count in UI
        break;
      case 'booking':
        // Update booking status in UI
        break;
      case 'payment':
        // Update payment status in UI
        break;
      case 'event':
        // Update event data in UI
        break;
    }
  }

  /**
   * Handle deep linking
   */
  private handleDeepLink(deepLink: string): void {
    // Implement deep linking logic
    logger.info('Handling deep link:', deepLink);
  }

  /**
   * Handle notification action responses
   */
  private handleNotificationAction(actionId: string, data: NotificationData): void {
    logger.info('Handling notification action:', actionId, 'with data:', data);

    switch (actionId) {
      case 'REPLY':
        // Open chat screen
        break;
      case 'MARK_READ':
        // Mark message as read
        break;
      case 'VIEW_BOOKING':
        // Open booking details
        break;
      case 'RESPOND':
        // Open booking response screen
        break;
      case 'VIEW_PAYMENT':
        // Open payment details
        break;
    }
  }

  /**
   * Update app badge count
   */
  private async updateBadgeCount(): Promise<void> {
    try {
      const badgeCount = await Notifications.getBadgeCountAsync();
      await Notifications.setBadgeCountAsync(badgeCount + 1);
    } catch (error) {
      logger.error('Failed to update badge count:', error);
    }
  }

  /**
   * Clear app badge count
   */
  async clearBadgeCount(): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      logger.error('Failed to clear badge count:', error);
    }
  }

  /**
   * Schedule a local notification
   */
  async scheduleNotification(notification: ScheduledNotification): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data as unknown as Record<string, unknown>,
          categoryIdentifier: notification.data.category,
          sound: 'default',
        },
        trigger: notification.trigger,
      });

      logger.info('Scheduled notification:', notificationId);
      return notificationId;
    } catch (error) {
      logger.error('Failed to schedule notification:', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      logger.info('Cancelled notification:', notificationId);
    } catch (error) {
      logger.error('Failed to cancel notification:', error);
    }
  }

  /**
   * Cancel all scheduled notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      logger.info('Cancelled all scheduled notifications');
    } catch (error) {
      logger.error('Failed to cancel all notifications:', error);
    }
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(): Promise<NotificationPreferences> {
    try {
      const response = await apiClient.get<NotificationPreferences>('/user/preferences');
      return response;
    } catch (error) {
      logger.error('Failed to get notification preferences:', error);
      // Return default preferences
      return {
        messages: true,
        bookings: true,
        payments: true,
        events: true,
        marketing: false,
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
        },
      };
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(preferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      await apiClient.patch('/user/preferences', preferences);
      logger.info('Updated notification preferences');
    } catch (error) {
      logger.error('Failed to update notification preferences:', error);
      throw error;
    }
  }

  /**
   * Get current push token
   */
  getPushToken(): string | null {
    return this.expoPushToken;
  }

  /**
   * Check if notifications are enabled
   */
  async areNotificationsEnabled(): Promise<boolean> {
    const { status } = await Notifications.getPermissionsAsync();
    return status === 'granted';
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
