declare module 'react-native-simple-radio-button' {
  import { Component } from 'react';
  import { ViewStyle, TextStyle } from 'react-native';

  interface RadioFormProps {
    radio_props: Array<{
      label: string;
      value: any;
    }>;
    initial?: any;
    onPress?: (value: any) => void;
    formHorizontal?: boolean;
    labelHorizontal?: boolean;
    buttonColor?: string;
    selectedButtonColor?: string;
    buttonInnerColor?: string;
    buttonOuterColor?: string;
    labelColor?: string;
    selectedLabelColor?: string;
    labelStyle?: TextStyle;
    buttonSize?: number;
    buttonOuterSize?: number;
    buttonInnerSize?: number;
    buttonStyle?: ViewStyle;
    labelStyle?: TextStyle;
    animation?: boolean;
    accessible?: boolean;
    accessibilityLabel?: string;
  }

  export default class RadioForm extends Component<RadioFormProps> {}
}
