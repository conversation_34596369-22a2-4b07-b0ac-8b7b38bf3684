export interface Feedback {
  id: string
  fromUserId: string
  fromUser: {
    id: string
    nickname: string
    avatar?: string
  }
  toUserId: string
  toUser: {
    id: string
    nickname: string
    avatar?: string
  }
  eventId: string
  event: {
    id: string
    title: string
    date: string
  }
  rating: number // 1-5
  comment?: string
  type: 'host' | 'guest'
  createdAt: string
}

export interface CreateFeedbackRequest {
  toUserId: string
  eventId: string
  rating: number
  comment?: string
  type: 'host' | 'guest'
} 