export interface ITypeProp {
  id: string;
  label: string;
  value: string;
  icon: any;
  children?: Array<ITypeProp>;
}

export interface IProperty {
  id?: number;
  name: string;
}

export interface IPics {
  pic_0: string;
  pic_1?: string;
  pic_2?: string;
  pic_3?: string;
  pic_4?: string;
}

export interface IProperties {

}
// export interface IEvent {
//   id: string;
//   maxPartecipants: number;
//   typeKs: Array<IProperty>;
//   typeBs: Array<IProperty>;
//   typeEs: Array<IProperty>;
//   intolerances: Array<IProperty>;
//   typeLs: Array<IProperty>;
//   date: string;
//   pics: IPics;
//   street?: string;
//   city: string;
//   postalcode?: string;
//   country: string;
//   pricepp: number;
//   userLogin: string;
//   name: string;
//   lon: number;
//   lat: number;
//   state: number;
//   type: number;
//   duration: number;
//   dateCreated: Date;
//   properties: IProperties;
// }

// export class EventOffer implements IEvent {
//   constructor(
//     public id = 'null',
//     public maxPartecipants = 0,
//     public typeKs = [],
//     public typeBs = [],
//     public typeEs = [],
//     public intolerances = [],
//     public typeLs = [],
//     public date = '',
//     // eslint-disable-next-line @typescript-eslint/camelcase
//     public pics = { pic_0: '' },
//     public street = 'null',
//     public city = 'null',
//     public postalcode = 'null',
//     public country = 'null',
//     public pricepp = 0,
//     public userLogin = 'null',
//     public name = 'null',
//     public lon = 0.0,
//     public lat = 0.0,
//     public state = 0,
//     public type = 0,
//     public duration = 0,
//     public dateCreated = new Date(),
//     public properties = {},
//   ) {}
// }
