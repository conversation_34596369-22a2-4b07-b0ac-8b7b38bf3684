import type { Event } from './event'
import type { PaymentLite } from './payment'

export enum BookingStatus {
  PENDING_CONFIRMATION = 'pending_confirmation',
  ACCEPTED = 'confirmed',
  PAYMENT_CONFIRMED = 'payment_confirmed',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
  CANCELLED_BY_GUEST = 'cancelled_by_guest',
  CANCELLED_BY_HOST = 'cancelled_by_host'
}

export interface EventBooking {
  id: string
  eventId: string
  number_of_guests: number
  total_amount_paid: number
  event: Event
  userId: string
  notes_to_host: string
  guest_user_id: string
  event_offer_id: string
  user: {
    id: string
    nickname: string
    avatar?: string
  }
  booking_status: BookingStatus
  message?: string
  requestedAt: string
  respondedAt?: string
}

export interface CreateEventBookingRequest {
  event_offer_id: string
  number_of_guests: number
  notes_to_host?: string
}

export interface ChangeEventBookingRequest {
  booking_id: string
  number_of_guests: number
  notes_to_host?: string
}

export interface RespondEventBookingRequest {
  event_booking: EventBookingResponse
  message: string
}

export interface EventBookingResponse {
  id: string
  event_offer_id: string
  guest_user_id: string
  booking_status: BookingStatus
  number_of_guests: number
  total_amount_paid: number
  booked_at: string
  notes_to_host: string
  created_at: string
  updated_at: string
}

export interface AugmentedEventBooking {
  event_offer: Event
  event_booking: EventBooking
  payment: PaymentLite
}
