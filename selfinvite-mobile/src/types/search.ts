import type { Event } from './event'
import type { UserProfile } from './user'

// Enhanced search results with Algolia response structure
export interface SearchResults {
  hits: Event[]
  totalHits: number
  totalPages: number
  currentPage: number
  processingTimeMs: number
  facetDistribution?: FacetCounts
  query: string
}

// Legacy interface for backward compatibility
export interface LegacySearchResults {
  events: Event[]
  users: UserProfile[]
  total: number
}

// Comprehensive search filters based on legacy Search.js
export interface SearchFilters {
  // Text search
  query?: string
  
  // Location filters
  city?: string
  country?: string
  coordinates?: {
    lat: number
    lng: number
  }
  radius?: number // in kilometers
  
  // Category filters (arrays for multi-select)
  kitchenTypes?: string[] // type_kitchens
  beverageTypes?: string[] // type_beverages
  eventTypes?: string[] // type_events
  locationTypes?: string[] // type_locations
  intolerances?: string[] // type_intolerances
  
  // Numeric range filters
  priceRange?: [number?, number?] // [min, max] in euros
  durationRange?: [number?, number?] // [min, max] in minutes
  maxParticipants?: number // guest count
  
  // Date filters
  dateRange?: [string?, string?] // [startDate, endDate] ISO strings
  
  // Status and availability
  status?: ('draft' | 'published' | 'cancelled' | 'completed')[]
  hasAvailableSpots?: boolean
  isPrivate?: boolean
  
  // Content filters
  includeNSFW?: boolean
  tags?: string[]
  
  // User-specific filters
  blockedUserIds?: string[]
  excludeHostedByMe?: boolean
  excludeJoinedByMe?: boolean
}

// Search parameters for API calls
export interface SearchParams {
  query?: string
  filters?: SearchFilters
  sort?: SearchSortOptions[]
  page?: number
  limit?: number
  facets?: string[]
  type?: 'events' | 'users' | 'all'
}

// Sorting options
export interface SearchSortOptions {
  field: 'event_date' | 'price_per_person' | 'duration_minutes' | 'created_at' | 'distance'
  direction: 'asc' | 'desc'
}

// Facet counts for filter UI
export interface FacetCounts {
  [facetName: string]: {
    [facetValue: string]: number
  }
}

// Search suggestions for autocomplete
export interface SearchSuggestion {
  text: string
  highlighted: string
  type: 'event' | 'location' | 'cuisine' | 'tag'
  metadata?: {
    city?: string
    cuisine?: string
    eventCount?: number
  }
}

// Algolia response structure
export interface AlgoliaResponse<T> {
  hits: T[]
  query: string
  processingTimeMs: number
  limit: number
  offset: number
  nbHits: number
  nbPages: number
  page: number
  facets?: FacetCounts
}

// Legacy Meilisearch response structure (for backward compatibility)
export interface MeilisearchResponse<T> {
  hits: T[]
  query: string
  processingTimeMs: number
  limit: number
  offset: number
  estimatedTotalHits?: number
  facetDistribution?: FacetCounts
}

// Geographic bounding box for map searches
export interface GeoBoundingBox {
  topLeft: {
    lat: number
    lng: number
  }
  bottomRight: {
    lat: number
    lng: number
  }
}

// Filter state for UI components
export interface FilterState {
  activeFilters: SearchFilters
  appliedFilters: SearchFilters
  isFilterModalVisible: boolean
  hasUnsavedChanges: boolean
}

// Search history item
export interface SearchHistoryItem {
  id: string
  query: string
  filters: SearchFilters
  timestamp: string
  resultCount: number
}

// Saved search
export interface SavedSearch {
  id: string
  name: string
  query: string
  filters: SearchFilters
  createdAt: string
  updatedAt: string
  notificationsEnabled: boolean
}

// Search analytics event
export interface SearchAnalyticsEvent {
  query: string
  filters: SearchFilters
  resultCount: number
  clickedResults: string[] // event IDs
  timestamp: string
  userId?: string
}

// Filter option for UI components
export interface FilterOption {
  label: string
  value: string
  icon?: string
  count?: number
  selected?: boolean
}

// Filter category for organizing filters
export interface FilterCategory {
  key: string
  label: string
  icon?: string
  options: FilterOption[]
  type: 'single' | 'multiple' | 'range' | 'date' | 'location'
}

// Constants from legacy Search.js
export const SEARCH_CONSTANTS = {
  MIN_PRICE: 0,
  MAX_PRICE: 100,
  MAX_DURATION_MIN: 480, // 8 hours
  MIN_DISTANCE_FILTER: 0,
  MAX_DISTANCE_FILTER: 500, // 500km
  DEFAULT_SEARCH_RADIUS: 25, // 25km
  DEBOUNCE_DELAY: 300, // ms
  SUGGESTION_MIN_CHARS: 2,
  DEFAULT_PAGE_SIZE: 20,
  MAX_SEARCH_HISTORY: 10,
} as const 