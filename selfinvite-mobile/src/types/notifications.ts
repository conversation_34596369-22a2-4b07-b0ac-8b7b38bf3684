export interface NotificationToken {
  token: string;
  type: 'expo' | 'fcm';
  deviceId: string;
  platform: 'ios' | 'android';
}

export interface NotificationPreferences {
  messages: boolean;
  bookings: boolean;
  payments: boolean;
  events: boolean;
  marketing: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
}

export interface NotificationData {
  type: 'message' | 'booking' | 'payment' | 'event' | 'marketing';
  priority: 'high' | 'normal' | 'low';
  category: string;
  data?: Record<string, any>;
  deepLink?: string;
}

export interface ScheduledNotification {
  id: string;
  title: string;
  body: string;
  data: NotificationData;
  trigger: {
    date?: Date;
    seconds?: number;
    repeats?: boolean;
  };
}

// Specific notification types for different app features
export interface MessageNotificationData extends NotificationData {
  type: 'message';
  data: {
    threadId: string;
    senderId: string;
    senderName: string;
    messagePreview: string;
  };
  deepLink: string; // selfinvite://chat/{threadId}
}

export interface BookingNotificationData extends NotificationData {
  type: 'booking';
  data: {
    bookingId: string;
    eventId: string;
    eventTitle: string;
    status: 'pending_confirmation' | 'confirmed' | 'accepted' | 'rejected' | 'cancelled';
    hostName?: string;
    guestName?: string;
  };
  deepLink: string; // selfinvite://booking/{bookingId}
}

export interface PaymentNotificationData extends NotificationData {
  type: 'payment';
  data: {
    paymentId: string;
    bookingId: string;
    amount: number;
    currency: string;
    status: 'pending' | 'completed' | 'failed' | 'refunded';
    eventTitle: string;
  };
  deepLink: string; // selfinvite://payment/{paymentId}
}

export interface EventNotificationData extends NotificationData {
  type: 'event';
  data: {
    eventId: string;
    eventTitle: string;
    eventDate: string;
    hostName: string;
    action: 'created' | 'updated' | 'cancelled' | 'reminder';
  };
  deepLink: string; // selfinvite://event/{eventId}
}

// API request/response types
export interface RegisterTokenRequest {
  token: string;
  type: 'expo' | 'fcm';
  deviceId: string;
  platform: 'ios' | 'android';
}

export interface UpdatePreferencesRequest {
  messages?: boolean;
  bookings?: boolean;
  payments?: boolean;
  events?: boolean;
  marketing?: boolean;
  quietHours?: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export interface SendNotificationRequest {
  userId: string;
  title: string;
  body: string;
  data: NotificationData;
  priority?: 'high' | 'normal' | 'low';
  scheduledFor?: string; // ISO date string
}

export interface NotificationHistory {
  id: string;
  title: string;
  body: string;
  data: NotificationData;
  sentAt: string;
  readAt?: string;
  clickedAt?: string;
}

// Notification analytics
export interface NotificationAnalytics {
  sent: number;
  delivered: number;
  opened: number;
  clicked: number;
  conversionRate: number;
}

export interface NotificationCampaign {
  id: string;
  name: string;
  type: 'marketing' | 'transactional' | 'reminder';
  targetAudience: string[];
  content: {
    title: string;
    body: string;
    data: NotificationData;
  };
  scheduledFor: string;
  status: 'draft' | 'scheduled' | 'sent' | 'completed' | 'cancelled';
  analytics: NotificationAnalytics;
  createdAt: string;
  updatedAt: string;
}
