import { ITypeProp } from './IEvent';
import { images } from './images';
import { t } from '../translations';

/**
 * Beverage options with internationalized labels
 * All labels are translated using the i18n system
 */
export const getBeverageOptions = (): Array<ITypeProp> => [
  {
    id: '__water',
    label: t('__water'),
    value: '__water',
    icon: images._water,
  },
  {
    id: '__alcoholic',
    label: t('__alcoholic'),
    value: '__alcoholic',
    icon: images._alcohol,
  },
  {
    id: '__analcoholic',
    label: t('__analcoholic'),
    value: '__analcoholic',
    icon: images._analcohol,
  },
  {
    id: '__wine',
    label: t('__wine'),
    value: '__wine',
    icon: images._wine,
  },
  {
    id: '__cocktail',
    label: t('__cocktail'),
    value: '__cocktail',
    icon: images._cocktail,
  },
  {
    id: '__beer',
    label: t('__beer'),
    value: '__beer',
    icon: images._beer,
  },
  {
    id: '__liquor',
    label: t('__liquor'),
    value: '__liquor',
    icon: images._liquor,
  },
  {
    id: '__smoothie',
    label: t('__smoothie'),
    value: '__smoothie',
    icon: images._smoothie,
  },
  {
    id: '__self_drink',
    label: t('__self_drink'),
    value: '__self_drink',
    icon: images._selfmade_drink,
  },
  {
    id: '__infused',
    label: t('__infused'),
    value: '__infused',
    icon: images._infusion,
  },
  {
    id: '__veg_milk',
    label: t('__veg_milk'),
    value: '__veg_milk',
    icon: images._veg_milk,
  },
  {
    id: '__milk',
    label: t('__milk'),
    value: '__milk',
    icon: images._milk,
  },
  {
    id: '__fresh_juice',
    label: t('__fresh_juice'),
    value: '__fresh_juice',
    icon: images._juices,
  },
];

// Export the function for backward compatibility
export const beverageOptions = getBeverageOptions();
