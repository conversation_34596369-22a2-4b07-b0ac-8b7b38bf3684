export interface UserProfile {
  user_id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatar_url?: string;
  phone_number?: string;
  date_of_birth?: string;
  bio?: string;
  address?: string;
  city?: string;
  country?: string;
  postal_code?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  user_id: string;
  badges?: string[];
  intolerances?: string[];
  preferences?: string[];
  notification_preferences?: string[];
  personal_tags?: string[];
  lang_key?: string;
  blocked_users?: string[];
}

export interface UpdatePreferencesRequest {
  user_id: string;
  badges?: string[];
  blocked_users?: string[];
  intolerances?: string[];
  preferences?: string[];
  notification_preferences?: string[];
  personal_tags?: string[];
  lang_key?: string;
}

export interface UpdateProfileRequest {
  username?: string;
  firstName?: string;
  lastName?: string;
  phone_number?: string;
  date_of_birth?: string;
  bio?: string;
  avatar_url?: string;
  address?: string;
  city?: string;
  country?: string;
  postal_code?: string;
}
