import { ITypeProp } from './IEvent';
import { images } from './images';
import { t } from '../translations';

/**
 * Intolerance options with internationalized labels
 * All labels are translated using the i18n system
 */
export const getIntoleranceOptions = (): Array<ITypeProp> => [
  { id: '__eggs', label: t('__eggs'), value: '__eggs', icon: images._eggs },
  { id: '__soy', label: t('__soy'), value: '__soy', icon: images._soy },
  {
    id: '__celery',
    label: t('__celery'),
    value: '__celery',
    icon: images._celery,
  },
  {
    id: '__mustard',
    label: t('__mustard'),
    value: '__mustard',
    icon: images._mustard,
  },
  {
    id: '__sesame',
    label: t('__sesame'),
    value: '__sesame',
    icon: images._sesame,
  },
  {
    id: '__sulphites',
    label: t('__sulphites'),
    value: '__sulphites',
    icon: images._sulphite,
  },
  {
    id: '__lupins',
    label: t('__lupins'),
    value: '__lupins',
    icon: images._lupins,
  },
  { id: '__clams', label: t('__clams'), value: '__clams', icon: images._shell },
  { id: '__dairy', label: t('__dairy'), value: '__dairy', icon: images._cheese },
  {
    id: '__gluten',
    label: t('__gluten'),
    value: '__gluten',
    icon: images._gluten,
  },
  { id: '__nuts', label: t('__nuts'), value: '__nuts', icon: images._peanut },
  {
    id: '__legumes',
    label: t('__legumes'),
    value: '__legumes',
    icon: images._beans,
  },
  { id: '__fish', label: t('__fish'), value: '__fish', icon: images._fish },
  {
    id: '__shellfish',
    label: t('__shellfish'),
    value: '__shellfish',
    icon: images._shrimp,
  },
];

// Export the function for backward compatibility
export const intoleranceOptions = getIntoleranceOptions();
