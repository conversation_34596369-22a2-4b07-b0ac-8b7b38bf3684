export type MessageStatus = 'visible' | 'deleted';

export interface Message {
  id: string
  event_booking_id: string
  sender_id: string
  receiver_id: string
  body: string
  sent_at: string
  read_at?: string
  sender_status: MessageStatus
  receiver_status: MessageStatus
  created_at: string
  updated_at: string
  // Optional populated fields for UI
  sender?: {
    id: string
    nickname: string
    avatar?: string
  }
  receiver?: {
    id: string
    nickname: string
    avatar?: string
  }
}

export interface Conversation {
  id: string
  participants: {
    id: string
    nickname: string
    avatar?: string
  }[]
  lastMessage?: Message
  unreadCount: number
  createdAt: string
  updatedAt: string
}

export interface SendMessageRequest {
  body: string
  event_booking_id: string
  receiver_id: string
} 