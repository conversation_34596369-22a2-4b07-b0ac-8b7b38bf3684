import { ITypeProp } from './IEvent';
import { images } from './images';
import { t } from '../translations';

/**
 * Kitchen options with internationalized labels
 * All labels are translated using the i18n system
 */
export const getKitchenOptions = (): Array<ITypeProp> => [
  {
    id: '__pizza',
    label: t('__pizza'),
    value: '__pizza',
    icon: images._pizza,
  },
  {
    id: '__ethnic',
    label: t('__ethnic'),
    value: '__ethnic',
    icon: images._ethnic,
  },
  {
    id: '__fusion',
    label: t('__fusion'),
    value: '__fusion',
    icon: images._fusion,
  },
  {
    id: '__mediterrean',
    label: t('__mediterrean'),
    value: '__mediterrean',
    icon: images._greek, // Using Greek icon as Mediterranean representation
  },
  {
    id: '__asian',
    label: t('__asian'),
    value: '__asian',
    icon: images._ramen,
  },
  {
    id: '__chinese',
    label: t('__chinese'),
    value: '__chinese',
    icon: images._rice_generic, // Using rice_generic instead of rice
  },
  {
    id: '__japanese',
    label: t('__japanese'),
    value: '__japanese',
    icon: images._sushi,
  },
  {
    id: '__indian',
    label: t('__indian'),
    value: '__indian',
    icon: images._ethnic,
  },
  {
    id: '__turkish',
    label: t('__turkish'),
    value: '__turkish',
    icon: images._turkish,
  },
  {
    id: '__burgers',
    label: t('__burgers'),
    value: '__burgers',
    icon: images._burger,
  },
  {
    id: '__greek',
    label: t('__greek'),
    value: '__greek',
    icon: images._greek,
  },
  {
    id: '__italian',
    label: t('__italian'),
    value: '__italian',
    icon: images._italian,
  },
  {
    id: '__pasta_risotto',
    label: t('__pasta_risotto'),
    value: '__pasta_risotto',
    icon: images._pasta_risotto,
  },
  {
    id: '__baking',
    label: t('__baking'),
    value: '__baking',
    icon: images._baking,
  },
  {
    id: '__pastry',
    label: t('__pastry'),
    value: '__pastry',
    icon: images._pastry,
  },
  {
    id: '__rice',
    label: t('__rice'),
    value: '__rice',
    icon: images._rice_generic,
  },
  {
    id: '__cereals',
    label: t('__cereals'),
    value: '__cereals',
    icon: images._cereal,
  },
  {
    id: '__pate',
    label: t('__pate'),
    value: '__pate',
    icon: images._meat_pie,
  },
  {
    id: '__seafood',
    label: t('__seafood'),
    value: '__seafood',
    icon: images._seafood,
  },
  {
    id: '__mushrooms',
    label: t('__mushrooms'),
    value: '__mushrooms',
    icon: images._mushrooms,
  },
  {
    id: '__raw',
    label: t('__raw'),
    value: '__raw',
    icon: images._raw,
  },
  {
    id: '__bio',
    label: t('__bio'),
    value: '__bio',
    icon: images._bio,
  },
  {
    id: '__frozen',
    label: t('__frozen'),
    value: '__frozen',
    icon: images._frozen,
  },
  {
    id: '__canned',
    label: t('__canned'),
    value: '__canned',
    icon: images._canned_food,
  },
  {
    id: '__homemade',
    label: t('__homemade'),
    value: '__homemade',
    icon: images._homemade,
  },
  {
    id: '__african',
    label: t('__african'),
    value: '__african',
    icon: images._african,
  },
  {
    id: '__vegeterian',
    label: t('__vegeterian'),
    value: '__vegeterian',
    icon: images._vegeterian,
  },
  {
    id: '__vegan',
    label: t('__vegan'),
    value: '__vegan',
    icon: images._vegan,
  },
  {
    id: '__street_food',
    label: t('__street_food'),
    value: '__street_food',
    icon: images._street_food,
  },
  {
    id: '__fish',
    label: t('__fish'),
    value: '__fish',
    icon: images._fish,
  },
  {
    id: '__meat',
    label: t('__meat'),
    value: '__meat',
    icon: images._meat2,
  },
  {
    id: '__junk_food',
    label: t('__junk_food'),
    value: '__junk_food',
    icon: images._street_food, // Using street_food icon for junk food
  },
  {
    id: '__thai',
    label: t('__thai'),
    value: '__thai',
    icon: images._thai,
  },
  {
    id: '__mexican',
    label: t('__mexican'),
    value: '__mexican',
    icon: images._mexican,
  },
  {
    id: '__argentina',
    label: t('__argentina'),
    value: '__argentina',
    icon: images._argentina,
  },
  {
    id: '__brazilian',
    label: t('__brazilian'),
    value: '__brazilian',
    icon: images._brazilian,
  },
  {
    id: '__gourmet_deli',
    label: t('__gourmet_deli'),
    value: '__gourmet_deli',
    icon: images._chef,
  },
  {
    id: '__chips_popcorn',
    label: t('__chips_popcorn'),
    value: '__chips_popcorn',
    icon: images._popcorn,
  },
  {
    id: '__bacon',
    label: t('__bacon'),
    value: '__bacon',
    icon: images._bacon,
  },
  {
    id: '__sweets_candies',
    label: t('__sweets_candies'),
    value: '__sweets_candies',
    icon: images._candies,
  },
  {
    id: '__icecream',
    label: t('__icecream'),
    value: '__icecream',
    icon: images._ice_cream,
  },
  {
    id: '__cake',
    label: t('__cake'),
    value: '__cake',
    icon: images._cake,
  },
  {
    id: '__poultry_meat',
    label: t('__poultry_meat'),
    value: '__poultry_meat',
    icon: images._poultry,
  },
  {
    id: '__pork',
    label: t('__pork'),
    value: '__pork',
    icon: images._pork,
  },
  {
    id: '__duck',
    label: t('__duck'),
    value: '__duck',
    icon: images._duck,
  },
  {
    id: '__chicken',
    label: t('__chicken'),
    value: '__chicken',
    icon: images._chicken,
  },
  {
    id: '__salad',
    label: t('__salad'),
    value: '__salad',
    icon: images._salad,
  },
  {
    id: '__season_fruit',
    label: t('__season_fruit'),
    value: '__season_fruit',
    icon: images._seasonable_fruit,
  },
  {
    id: '__season_veg',
    label: t('__season_veg'),
    value: '__season_veg',
    icon: images._seasonable_vegetables,
  },
  {
    id: '__breads',
    label: t('__breads'),
    value: '__breads',
    icon: images._bakery,
  },
  {
    id: '__jam',
    label: t('__jam'),
    value: '__jam',
    icon: images._jam,
  },
  {
    id: '__butter',
    label: t('__butter'),
    value: '__butter',
    icon: images._toast,
  },
  {
    id: '__evo',
    label: t('__evo'),
    value: '__evo',
    icon: images._olive_oil,
  },
  {
    id: '__oil',
    label: t('__oil'),
    value: '__oil',
    icon: images._seed_oil,
  },
  {
    id: '__eggs',
    label: t('__eggs'),
    value: '__eggs',
    icon: images._eggs,
  },
  {
    id: '__cheese',
    label: t('__cheese'),
    value: '__cheese',
    icon: images._cheese,
  },
  {
    id: '__km0',
    label: t('__km0'),
    value: '__km0',
    icon: images._km0,
  },
];

// Export the function for backward compatibility
export const kitchenOptions = getKitchenOptions();
