export interface Review {
  id: string
  userId: string
  user: {
    id: string
    nickname: string
    avatar?: string
  }
  eventId: string
  event: {
    id: string
    title: string
    date: string
  }
  rating: number
  title?: string
  content?: string
  images?: string[]
  helpfulCount: number
  isHelpful?: boolean // Current user's helpful vote
  createdAt: string
  updatedAt: string
}

export interface CreateReviewRequest {
  eventId: string
  rating: number
  title?: string
  content?: string
  images?: string[]
}

export interface UpdateReviewRequest {
  rating?: number
  title?: string
  content?: string
  images?: string[]
} 