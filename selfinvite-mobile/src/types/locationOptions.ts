import { ITypeProp } from './IEvent';
import { images } from './images';
import { t } from '../translations';

/**
 * Location options with internationalized labels
 * All labels are translated using the i18n system
 */
export const getLocationOptions = (): Array<ITypeProp> => [
  {
    id: '__terrace',
    label: t('__terrace'),
    value: '__terrace',
    icon: images._terrace,
  },
  {
    id: '__neighborhood',
    label: t('__neighborhood'),
    value: '__neighborhood',
    icon: images._neighborhood,
  },
  {
    id: '__garden',
    label: t('__garden'),
    value: '__garden',
    icon: images._garden,
  },
  {
    id: '__canteen',
    label: t('__canteen'),
    value: '__canteen',
    icon: images._canteen,
  },
  {
    id: '__home',
    label: t('__home'),
    value: '__home',
    icon: images._house,
  },
  {
    id: '__restaurant',
    label: t('__restaurant'),
    value: '__restaurant',
    icon: images._restaurant,
  },
  {
    id: '__hotel',
    label: t('__hotel'),
    value: '__hotel',
    icon: images._hotel,
  },
  {
    id: '__bnb',
    label: t('__bnb'),
    value: '__bnb',
    icon: images._bnb,
  },

  {
    id: '__park',
    label: t('__park'),
    value: '__park',
    icon: images._park,
  },
  {
    id: '__square',
    label: t('__square'),
    value: '__square',
    icon: images._square,
  },
  {
    id: '__club',
    label: t('__club'),
    value: '__club',
    icon: images._club,
  },
  {
    id: '__beach',
    label: t('__beach'),
    value: '__beach',
    icon: images._beach,
  },
  {
    id: '__camping',
    label: t('__camping'),
    value: '__camping',
    icon: images._camping,
  },
  {
    id: '__sport_facilities',
    label: t('__sport_facilities'),
    value: '__sport_facilities',
    icon: images._sports_facilities,
  },
  {
    id: '__office',
    label: t('__office'),
    value: '__office',
    icon: images._office,
  },
  {
    id: '__rooftop',
    label: t('__rooftop'),
    value: '__rooftop',
    icon: images._rooftop,
  },
  {
    id: '__bonfire',
    label: t('__bonfire'),
    value: '__bonfire',
    icon: images._bonfire,
  },
  {
    id: '__guest_bath',
    label: t('__guest_bath'),
    value: '__guest_bath',
    icon: images._private_toilets,
  },
  {
    id: '__pet_friendly',
    label: t('__pet_friendly'),
    value: '__pet_friendly',
    icon: images._pet_care,
  },
  {
    id: '__private_room',
    label: t('__private_room'),
    value: '__private_room',
    icon: images._private_room,
  },
  {
    id: '__wifi',
    label: t('__wifi'),
    value: '__wifi',
    icon: images._wifi,
  },
  {
    id: '__airco',
    label: t('__airco'),
    value: '__airco',
    icon: images._airco,
  },
  {
    id: '__swim_pool',
    label: t('__swim_pool'),
    value: '__swim_pool',
    icon: images._swimming_pool,
  },
  {
    id: '__playground',
    label: t('__playground'),
    value: '__playground',
    icon: images._playground,
  },
  {
    id: '__wood_oven',
    label: t('__wood_oven'),
    value: '__wood_oven',
    icon: images._wood_oven,
  },
  {
    id: '__indoor',
    label: t('__indoor'),
    value: '__indoor',
    icon: images._indoor,
  },
  {
    id: '__outdoor',
    label: t('__outdoor'),
    value: '__outdoor',
    icon: images._outdoor,
  },
  {
    id: '__yacht',
    label: t('__yacht'),
    value: '__yacht',
    icon: images._yacht,
  },
  {
    id: '__sail_boat',
    label: t('__sail_boat'),
    value: '__sail_boat',
    icon: images._sail_boat,
  },
  {
    id: '__wheelchair_access',
    label: t('__wheelchair_access'),
    value: '__wheelchair_access',
    icon: images._wheelchair,
  },
];

// Export the function for backward compatibility
export const locationOptions = getLocationOptions();
