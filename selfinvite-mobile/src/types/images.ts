export const images = {
  _profile: require('@assets/images/no_user.png'),
  _no_profile: require('@assets/images/no_profile.png'),

  _cheese: require('@assets/images/cheese.png'),
  _eggs: require('@assets/images/eggs.png'),
  _peanut: require('@assets/images/peanut.png'),
  _soy: require('@assets/images/soy.png'),
  _celery: require('@assets/images/celery.png'),
  _mustard: require('@assets/images/mustard.png'),
  _sesame: require('@assets/images/sesame.png'),
  _sulphite: require('@assets/images/sulphite.png'),
  _lupins: require('@assets/images/lupin.png'),
  _shell: require('@assets/images/shell.png'),
  _picnic: require('@assets/images/picnic.png'),
  _badge: require('@assets/images/badge.png'),
  _yoga: require('@assets/images/meditation.png'),
  _tea: require('@assets/images/bubble-tea.png'),
  _fruit: require('@assets/images/fruit.png'),
  _beer: require('@assets/images/beer.png'),
  _chestnut: require('@assets/images/chestnut.png'),
  _shrimp: require('@assets/images/shrimp.png'),
  _orange: require('@assets/images/orange.png'),
  _grill: require('@assets/images/grill.png'),
  _birthday: require('@assets/images/birthday-cake.png'),
  _christmas: require('@assets/images/christmas.png'),
  _university: require('@assets/images/university.png'),
  _halloween: require('@assets/images/halloween.png'),
  _mixer: require('@assets/images/dj.png'),
  _basketball: require('@assets/images/basketball.png'),
  _pancakes: require('@assets/images/pancakes.png'),
  _breakfast: require('@assets/images/breakfast.png'),
  _lunch: require('@assets/images/lunch.png'),
  _aperitif: require('@assets/images/aperitif.png'),
  _packed_food: require('@assets/images/packed-food.png'),
  _food_delivery: require('@assets/images/food-delivery.png'),
  _dinner: require('@assets/images/dinner.png'),
  _private: require('@assets/images/private.png'),

  _wedding: require('@assets/images/wedding-arch.png'),
  _party: require('@assets/images/party.png'),
  _bachelor: require('@assets/images/bachelor.png'),
  _folk: require('@assets/images/folk.png'),
  _ballroom_dance: require('@assets/images/ballroom_dance.png'),
  _karaoke: require('@assets/images/karaoke.png'),
  _live_music: require('@assets/images/live_music.png'),
  _pool_party: require('@assets/images/pool_party.png'),
  _beach_party: require('@assets/images/beach_party.png'),
  _children_party: require('@assets/images/children_party.png'),
  _running: require('@assets/images/running.png'),
  _book_club: require('@assets/images/book_club.png'),
  _hiking: require('@assets/images/hiking.png'),
  _new_year_eve: require('@assets/images/new_year_eve.png'),
  _sports: require('@assets/images/stadium.png'),
  _tennis: require('@assets/images/tennis.png'),
  _climbing: require('@assets/images/climbing.png'),
  _ski: require('@assets/images/skis.png'),
  _skateboarding: require('@assets/images/skateboard.png'),
  _surfboard: require('@assets/images/surfboard.png'),
  _football: require('@assets/images/football-player.png'),
  _cards: require('@assets/images/cards.png'),
  _board_game: require('@assets/images/board-game.png'),
  _live_sports: require('@assets/images/live-sports.png'),
  _tv: require('@assets/images/tv.png'),
  _orgy: require('@assets/images/orgy.png'),
  _naturism: require('@assets/images/naturism.png'),
  _agriculture: require('@assets/images/agriculture.png'),
  _movie: require('@assets/images/movie.png'),
  _journalism: require('@assets/images/journalism.png'),
  _cosplay: require('@assets/images/cosplay.png'),
  _wine_tasting: require('@assets/images/wine-tasting.png'),
  _motorcycle: require('@assets/images/motorcycle.png'),
  _workout: require('@assets/images/workout.png'),
  _brewers: require('@assets/images/brewers.png'),
  _smokers: require('@assets/images/smokers.png'),
  _ethnic: require('@assets/images/ethnic.png'),
  _pizza: require('@assets/images/pizza.png'),
  _pastry: require('@assets/images/pastry.png'),
  _baking: require('@assets/images/baking.png'),
  _fusion: require('@assets/images/fusion.png'),
  _bruschetta: require('@assets/images/bruschetta.png'),
  _ramen: require('@assets/images/ramen.png'),
  _rice: require('@assets/images/rice.png'),
  _japanese: require('@assets/images/sushi.png'),
  _sushi: require('@assets/images/sushi.png'),
  _turkish: require('@assets/images/shawarma.png'),
  _greek: require('@assets/images/eggplant.png'),
  _italian: require('@assets/images/spaghetti.png'),
  _african: require('@assets/images/couscous.png'),
  _vegeterian: require('@assets/images/broccoli.png'),
  _vegan: require('@assets/images/vegan.png'),
  _street_food: require('@assets/images/street_food.png'),
  _fish: require('@assets/images/fish.png'),
  _meat: require('@assets/images/meat.png'),
  _meat2: require('@assets/images/meat-2.png'),
  _junk_food: require('@assets/images/street_food.png'),
  _burger: require('@assets/images/burger.png'),
  _thai: require('@assets/images/thai.png'),
  _mexican: require('@assets/images/taco.png'),
  _argentina: require('@assets/images/empanada.png'),

  _alcohol: require('@assets/images/liquor.png'),
  _analcohol: require('@assets/images/non-alcoholic.png'),
  _juices: require('@assets/images/orange-juice.png'),
  _wine: require('@assets/images/wine.png'),
  _cocktail: require('@assets/images/cocktail.png'),
  _liquor: require('@assets/images/herbal-liquor.png'),

  _bnb: require('@assets/images/bnb.png'),
  _hotel: require('@assets/images/hotel.png'),
  _restaurant: require('@assets/images/restaurant.png'),
  _house: require('@assets/images/house.png'),
  _canteen: require('@assets/images/canteen.png'),
  _garden: require('@assets/images/garden.png'),
  _terrace: require('@assets/images/terrace.png'),
  _street: require('@assets/images/street.png'),

  _gluten: require('@assets/images/gluten.png'),
  _beans: require('@assets/images/beans.png'),
  _brazilian: require('@assets/images/brazilian.png'),

  _chef: require('@assets/images/chef.png'),
  _event: require('@assets/images/event.png'),
  _kitchen_default: require('@assets/images/kitchen_default.png'),
  _drink_default: require('@assets/images/drink_default.png'),
  _calendar: require('@assets/images/calendar.png'),
  _calendar_big: require('@assets/images/calendar_big.png'),
  _error_big: require('@assets/images/error_big.png'),
  _time: require('@assets/images/time.png'),
  _people_icon: require('@assets/images/people.png'),
  _money: require('@assets/images/money.png'),
  _infusion: require('@assets/images/herbal-tea.png'),
  _neighborhood: require('@assets/images/residential.png'),
  _sports_facilities: require('@assets/images/sport-facilities.png'),
  _camping: require('@assets/images/tent.png'),
  _bar: require('@assets/images/drinks.png'),
  _beach: require('@assets/images/beach.png'),
  _club: require('@assets/images/night-club.png'),
  _park: require('@assets/images/park.png'),
  _square: require('@assets/images/city-square.png'),
  _office: require('@assets/images/workspace.png'),
  _rooftop: require('@assets/images/rooftop.png'),
  _veg_milk: require('@assets/images/veg-milk.png'),
  _milk: require('@assets/images/milk.png'),

  _lgbtq: require('@assets/images/rainbow-flag.png'),
  _speeddate: require('@assets/images/meter.png'),
  _music: require('@assets/images/music.png'),
  _dark_kitchen: require('@assets/images/dark-kitchen.png'),
  _speak_easy: require('@assets/images/speak-easy.png'),
  _single_party: require('@assets/images/intersex.png'),
  _popcorn: require('@assets/images/popcorn.png'),
  _bacon: require('@assets/images/bacon.png'),
  _candies: require('@assets/images/candies.png'),
  _ice_cream: require('@assets/images/ice-cream.png'),
  _icecream: require('@assets/images/ice-cream.png'), // Alias for consistency
  _cake: require('@assets/images/cake.png'),
  _poultry: require('@assets/images/poultry.png'),
  _pork: require('@assets/images/pig.png'),
  _chicken: require('@assets/images/chicken-leg.png'),
  _salad: require('@assets/images/salad.png'),
  _sandwich: require('@assets/images/sandwich.png'),
  _water: require('@assets/images/water.png'),

  _outdoor: require('@assets/images/outdoor.png'),
  _indoor: require('@assets/images/indoor.png'),
  _playground: require('@assets/images/basketball-court.png'),
  _swimming_pool: require('@assets/images/swimming-pool.png'),
  _airco: require('@assets/images/airco.png'),
  _wifi: require('@assets/images/wifi.png'),
  _private_room: require('@assets/images/private_room.png'),
  _pet_care: require('@assets/images/pet-care.png'),
  _private_toilets: require('@assets/images/toilets.png'),
  _homemade: require('@assets/images/baker.png'),
  _canned_food: require('@assets/images/canned-food.png'),
  _frozen: require('@assets/images/frozen.png'),
  _local: require('@assets/images/local.png'),
  _bio: require('@assets/images/bio.png'),
  _raw: require('@assets/images/raw.png'),
  _mushrooms: require('@assets/images/mushrooms.png'),
  _salami: require('@assets/images/salami.png'),
  _meat_pie: require('@assets/images/meat-pie.png'),
  _cereal: require('@assets/images/cereal.png'),
  _duck: require('@assets/images/duck.png'),
  _pasta_risotto: require('@assets/images/risotto.png'),
  _seed_oil: require('@assets/images/oil.png'),
  _olive_oil: require('@assets/images/olive-oil.png'),
  _bakery: require('@assets/images/bakery.png'),
  _jam: require('@assets/images/jam.png'),
  _selfmade_drink: require('@assets/images/drinking-water.png'),
  _seasonable_vegetables: require('@assets/images/carrot.png'),
  _wood_oven: require('@assets/images/wood-oven.png'),
  _seasonable_fruit: require('@assets/images/watermelon.png'),
  _toast: require('@assets/images/toast.png'),
  _bonfire: require('@assets/images/bonfire.png'),
  _seafood: require('@assets/images/seafood.png'),
  _rabbit: require('@assets/images/rabbit.png'),
  _lamb: require('@assets/images/lamb.png'),
  _smoothie: require('@assets/images/smoothie.png'),
  _rice_generic: require('@assets/images/rice_generic.png'),
  _yacht: require('@assets/images/yacht.png'),
  _sail_boat: require('@assets/images/sailing.png'),
  _warning: require('@assets/images/warning.png'),
  _success: require('@assets/images/checked.png'),
  _policy: require('@assets/images/policy.png'),
  _male: require('@assets/images/male.png'),
  _female: require('@assets/images/female.png'),
  _no_genre: require('@assets/images/no_genre.png'),
  _distance: require('@assets/images/distance.png'),
  _map_marker: require('@assets/map/map_marker.png'),
  _wheelchair: require('@assets/images/wheelchair.png'),
  _km0: require('@assets/images/km0.png'),
  _dummy_event: require('@assets/images/dummy-event.png'),
  _gte18: require('@assets/images/gte18.png'),
  _settings: require('@assets/images/settings.png'),
  _red_flag: require('@assets/images/red-flag.png'),
  _block_user: require('@assets/images/block-user.png'),
  _spam: require('@assets/images/spam.png'),
  _host_2021: require('@assets/images/2021.png'),
  _best_chef: require('@assets/images/best_chef.png'),
  _padel: require('@assets/images/padel.png'),

  _fishing: require('@assets/images/fishing.png'),
  _blockchain: require('@assets/images/blockchain.png'),
  _metaverse: require('@assets/images/virtual-reality.png'),

  _food_slide: require('@assets/screens/food_screen.png'),
  _table_slide: require('@assets/screens/table_screen.png'),
  _map_slide: require('@assets/screens/map_screen.png'),
  _people_slide: require('@assets/screens/peoples_screen.png'),
};
