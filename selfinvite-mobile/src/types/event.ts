// Type definitions for backend enums
export type KitchenType = string
export type BeverageType = string
export type EventType = string
export type LocationType = string
export type IntoleranceType = string

export interface MediaProperty {
  url: string
  type: 'image' | 'video'
  caption?: string
}

// Event Types
export interface Event {
  id: string
  title: string
  description: string
  event_date: string
  startTime: string
  endTime?: string
  
  street: string
  city: string
  postal_code: string
  country: string
  longitude: number
  latitude: number
  host_user_id: string
  host: {
    id: string
    username: string
    avatar?: string
  }
  max_participants: number
  currentParticipants: number
  type_kitchens?: string[]
  type_events?: string[]
  type_beverages?: string[]
  type_intolerances?: string[]
  type_locations?: string[]
  price_per_person?: number
  duration_minutes?: number
  currency?: string
  medias?: MediaProperty[]
  status: 'draft' | 'published' | 'cancelled' | 'completed'
  isPrivate: boolean
  tags?: string[]
  createdAt: string
  updatedAt: string

  
  distance?: number
}

export interface CreateEventRequest {
  title: string
  description?: string
  max_participants: number
  type_kitchens?: KitchenType[]
  type_beverages?: BeverageType[]
  type_events?: EventType[]
  type_locations?: LocationType[]
  type_intolerances?: IntoleranceType[]
  street?: string
  city?: string
  postal_code?: string
  country?: string
  longitude?: number
  latitude?: number
  event_date: string // ISO date string
  duration_minutes: number
  price_per_person: number
  medias?: MediaProperty[]
}

export interface UpdateEventRequest extends Partial<CreateEventRequest> {
  status?: 'draft' | 'published' | 'cancelled' | 'completed'
}

export interface EventSearchParams {
  city?: string
  date?: string
  kitchenType?: string[]
  eventType?: string[]
  beverageType?: string[]
  intolerances?: string[]
  locationType?: string[]
  maxPrice?: number
  radius?: number
  coordinates?: {
    lat: number
    lng: number
  }
  page?: number
  limit?: number
}
