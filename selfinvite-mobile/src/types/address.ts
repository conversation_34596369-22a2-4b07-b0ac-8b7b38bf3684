export type Address = {
  latitude: number;
  longitude: number;
  formattedAddress: string;
};

// Enhanced geocoded address with more details
export interface GeocodedAddress {
  latitude: number;
  longitude: number;
  formattedAddress: string;
  city?: string;
  country?: string;
  postalCode?: string;
  street?: string;
  placeName?: string;
  confidence?: number;
  context?: Array<{
    id: string;
    text: string;
  }>;
}

// Mapbox search result type
export interface AddressSearchResult {
  id: string;
  placeName: string;
  formattedAddress: string;
  coordinates: [number, number]; // [longitude, latitude]
  street?: string;
  city?: string;
  country?: string;
  postalCode?: string;
  context?: Array<{
    id: string;
    text: string;
  }>;
  bbox?: [number, number, number, number]; // [minLng, minLat, maxLng, maxLat]
}

// Geocoding service response
export interface GeocodingResponse {
  results: AddressSearchResult[];
  attribution?: string;
}

// Location selection mode
export type LocationSelectionMode = 'current' | 'search';

// Geocoding component props
export interface GeocodingSelectorProps {
  onLocationSelect: (address: GeocodedAddress) => void;
  initialLocation?: GeocodedAddress;
  mode?: LocationSelectionMode;
  placeholder?: string;
  showMapCard?: boolean;
  style?: any;
}