import { ITypeProp } from './IEvent';
import { images } from './images';
import { t } from '../translations';

/**
 * Event options with internationalized labels
 * All labels are translated using the i18n system
 */
export const getEventOptions = (): Array<ITypeProp> => [
  {
    id: '__breakfast',
    label: t('__breakfast'),
    value: '__breakfast',
    icon: images._breakfast,
  },
  {
    id: '__brunch',
    label: t('__brunch'),
    value: '__brunch',
    icon: images._pancakes,
  },
  {
    id: '__lunch',
    label: t('__lunch'),
    value: '__lunch',
    icon: images._lunch,
  },
  {
    id: '__aperitif',
    label: t('__aperitif'),
    value: '__aperitif',
    icon: images._aperitif,
  },
  {
    id: '__packed_food',
    label: t('__packed_food'),
    value: '__packed_food',
    icon: images._packed_food,
  },
  {
    id: '__food_delivery',
    label: t('__food_delivery'),
    value: '__food_delivery',
    icon: images._food_delivery,
  },
  {
    id: '__dinner',
    label: t('__dinner'),
    value: '__dinner',
    icon: images._dinner,
  },
  {
    id: '__picnic',
    label: t('__picnic'),
    value: '__picnic',
    icon: images._picnic,
  },
  {
    id: '__special_events',
    label: t('__special_events'),
    value: '__special_events',
    icon: images._badge,
    children: [
      {
        id: '__speed_date',
        label: t('__speed_date'),
        value: '__speed_date',
        icon: images._speeddate,
      },
      {
        id: '__halloween_party',
        label: t('__halloween_party'),
        value: '__halloween_party',
        icon: images._halloween,
      },
      {
        id: '__graduation_party',
        label: t('__graduation_party'),
        value: '__graduation_party',
        icon: images._university,
      },
      {
        id: '__wedding_party',
        label: t('__wedding_party'),
        value: '__wedding_party',
        icon: images._wedding,
      },
      {
        id: '__birthday_party',
        label: t('__birthday_party'),
        value: '__birthday_party',
        icon: images._birthday,
      },
      {
        id: '__bachelor_ette_party',
        label: t('__bachelor_ette_party'),
        value: '__bachelor_ette_party',
        icon: images._bachelor,
      },
      {
        id: '__christmas_party',
        label: t('__christmas_party'),
        value: '__christmas_party',
        icon: images._christmas,
      },
      {
        id: '__new_year_eve_party',
        label: t('__new_year_eve_party'),
        value: '__new_year_eve_party',
        icon: images._new_year_eve,
      },
      {
        id: '__lgbtq_party',
        label: t('__lgbtq_party'),
        value: '__lgbtq_party',
        icon: images._lgbtq,
      },
      {
        id: '__singles_party',
        label: t('__singles_party'),
        value: '__singles_party',
        icon: images._single_party,
      },
      {
        id: '__swingers_party',
        label: t('__swingers_party'),
        value: '__swingers_party',
        icon: images._orgy,
      },
    ],
  },

  {
    id: '__speak_easy',
    label: t('__speak_easy'),
    value: '__speak_easy',
    icon: images._speak_easy,
  },
  {
    id: '__dark_kitchen',
    label: t('__dark_kitchen'),
    value: '__dark_kitchen',
    icon: images._dark_kitchen,
  },
  {
    id: '__sports_events',
    label: t('__sports_events'),
    value: '__sports_events',
    icon: images._sports,
    children: [
      {
        id: '__tennis',
        label: t('__tennis'),
        value: '__tennis',
        icon: images._tennis,
      },
      {
        id: '__padel',
        label: t('__padel'),
        value: '__padel',
        icon: images._padel,
      },
      {
        id: '__climbing',
        label: t('__climbing'),
        value: '__climbing',
        icon: images._climbing,
      },
      {
        id: '__ski',
        label: t('__ski'),
        value: '__ski',
        icon: images._ski,
      },
      {
        id: '__running',
        label: t('__running'),
        value: '__running',
        icon: images._running,
      },
      {
        id: '__hiking',
        label: t('__hiking'),
        value: '__hiking',
        icon: images._hiking,
      },
      {
        id: '__playground_3vs3',
        label: t('__playground_3vs3'),
        value: '__playground_3vs3',
        icon: images._basketball,
      },
      {
        id: '__football',
        label: t('__football'),
        value: '__football',
        icon: images._football,
      },
      {
        id: '__workout_fitness_course',
        label: t('__workout_fitness_course'),
        value: '__workout_fitness_course',
        icon: images._workout,
      },
      {
        id: '__skateboarding',
        label: t('__skateboarding'),
        value: '__skateboarding',
        icon: images._skateboarding,
      },
      {
        id: '__surfboarding',
        label: t('__surfboarding'),
        value: '__surfboarding',
        icon: images._surfboard,
      },
      {
        id: '__fishing',
        label: t('__fishing'),
        value: '__fishing',
        icon: images._fishing,
      },
    ],
  },
  {
    id: '__music_events',
    label: t('__music_events'),
    value: '__music_events',
    icon: images._music,
    children: [
      {
        id: '__karaoke',
        label: t('__karaoke'),
        value: '__karaoke',
        icon: images._karaoke,
      },
      {
        id: '__folk_dance',
        label: t('__folk_dance'),
        value: '__folk_dance',
        icon: images._folk,
      },
      {
        id: '__ballroom_dance',
        label: t('__ballroom_dance'),
        value: '__ballroom_dance',
        icon: images._ballroom_dance,
      },
      {
        id: '__live_music',
        label: t('__live_music'),
        value: '__live_music',
        icon: images._live_music,
      },
      {
        id: '__dj_set',
        label: t('__dj_set'),
        value: '__dj_set',
        icon: images._mixer,
      },
      {
        id: '__after_party',
        label: t('__after_party'),
        value: '__after_party',
        icon: images._party,
      },
    ],
  },
  {
    id: '__book_club',
    label: t('__book_club'),
    value: '__book_club',
    icon: images._book_club,
  },
  {
    id: '__pool_party',
    label: t('__pool_party'),
    value: '__pool_party',
    icon: images._pool_party,
  },
  {
    id: '__beach_party',
    label: t('__beach_party'),
    value: '__beach_party',
    icon: images._beach_party,
  },
  {
    id: '__children_party',
    label: t('__children_party'),
    value: '__children_party',
    icon: images._children_party,
  },
  {
    id: '__card_game_tournament',
    label: t('__card_game_tournament'),
    value: '__card_game_tournament',
    icon: images._cards,
  },
  {
    id: '__game_board_tournament',
    label: t('__game_board_tournament'),
    value: '__game_board_tournament',
    icon: images._board_game,
  },
  {
    id: '__blockchain',
    label: t('__blockchain'),
    value: '__blockchain',
    icon: images._blockchain,
  },
  {
    id: '__metaverse',
    label: t('__metaverse'),
    value: '__metaverse',
    icon: images._metaverse,
  },
  {
    id: '__tv_sports',
    label: t('__tv_sports'),
    value: '__tv_sports',
    icon: images._live_sports,
  },
  {
    id: '__tv_series',
    label: t('__tv_series'),
    value: '__tv_series',
    icon: images._tv,
  },
  {
    id: '__yoga',
    label: t('__yoga'),
    value: '__yoga',
    icon: images._yoga,
  },
  {
    id: '__naturism_party',
    label: t('__naturism_party'),
    value: '__naturism_party',
    icon: images._naturism,
  },
  {
    id: '__home_growers',
    label: t('__home_growers'),
    value: '__home_growers',
    icon: images._agriculture,
  },
  {
    id: '__movie_lovers',
    label: t('__movie_lovers'),
    value: '__movie_lovers',
    icon: images._movie,
  },
  {
    id: '__live_tv',
    label: t('__live_tv'),
    value: '__live_tv',
    icon: images._journalism,
  },
  {
    id: '__rpg_cosplaying_party',
    label: t('__rpg_cosplaying_party'),
    value: '__rpg_cosplaying_party',
    icon: images._cosplay,
  },
  {
    id: '__tasting',
    label: t('__tasting'),
    value: '__tasting',
    icon: images._wine_tasting,
  },
  {
    id: '__bike_lovers',
    label: t('__bike_lovers'),
    value: '__bike_lovers',
    icon: images._motorcycle,
  },
  {
    id: '__bbq_lovers',
    label: t('__bbq_lovers'),
    value: '__bbq_lovers',
    icon: images._grill,
  },
  {
    id: '__homebrewers',
    label: t('__homebrewers'),
    value: '__homebrewers',
    icon: images._brewers,
  },
  {
    id: '__smokers',
    label: t('__smokers'),
    value: '__smokers',
    icon: images._smokers,
  },
];

export function getEventImageIcon(eventType: string) {
  const res: Array<ITypeProp> = getEventOptions().filter(e => e.id === eventType);
  if (res.length !== 0) {
    return res[0]?.icon || images._event;
  }

  const res2 = res
    .map(e => e.children)
    .flat()
    .filter(g => g !== undefined)
    .filter((e2: any) => e2.id === eventType);
  if (res2 && Array.isArray(res2) && res2.length > 0) {
    return res2[0]?.icon || images._event;
  }

  return images._event;
}

// Export the function for backward compatibility
export const eventOptions = getEventOptions();
