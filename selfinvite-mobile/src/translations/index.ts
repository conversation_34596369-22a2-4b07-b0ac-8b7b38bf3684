import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';

import fr from './global_fr.json';
import it from './global_it.json';
import de from './global_de.json';
import en from './global_en.json';
import es from './global_es.json';
import pt from './global_pt.json';

/**
 * Internationalization setup for Selfinvite Mobile App
 * Supports: English, German, French, Spanish, Italian, Portuguese
 */
const i18n = new I18n(
  { 
    en: en, 
    de: de, 
    fr: fr, 
    es: es, 
    it: it, 
    pt: pt 
  },
  { 
    enableFallback: true, 
    locale: Localization?.getLocales()[0]?.languageCode || 'en'
  },
);

// Set the locale from device settings
i18n.locale = Localization?.getLocales()[0]?.languageCode || 'en';

export default i18n;

// Export the translate function for convenience
export const t = (key: string, options?: any) => i18n.t(key, options);

// Export available locales
export const availableLocales = ['en', 'de', 'fr', 'es', 'it', 'pt'] as const;
export type Locale = typeof availableLocales[number];
