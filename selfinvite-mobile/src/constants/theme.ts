import { DefaultTheme, MD3DarkTheme } from 'react-native-paper';

// Selfinvite Brand Colors
export const colors = {
  primary: '#F24958',
  primaryVariant: '#E53E3E',
  secondary: '#4A90E2',
  secondaryVariant: '#357ABD',
  background: '#F5F7FA',
  surface: '#FFFFFF',
  surfaceVariant: '#F1F3F4',
  onSurface: '#1C1B1F',
  onSurfaceVariant: '#49454F',
  onBackground: '#1C1B1F',
  onPrimary: '#FFFFFF',
  onSecondary: '#FFFFFF',
  error: '#B3261E',
  onError: '#FFFFFF',
  errorContainer: '#F9DEDC',
  onErrorContainer: '#410E0B',
  success: '#2E7D32',
  warning: '#F57C00',
  info: '#1976D2',
  whiteBackground: '#FFFFFF',
  text: '#212121',
  textSecondary: '#757575',
  border: '#E0E0E0',
  disabled: '#BDBDBD',
  placeholder: '#9E9E9E',
  black: '#000000',
  white: '#FFFFFF',
  transparent: 'transparent',
};

// Dark Theme Colors
export const darkColors = {
  ...colors,
  primary: '#F24958',
  background: '#121212',
  surface: '#1E1E1E',
  surfaceVariant: '#2C2C2C',
  onSurface: '#E1E1E1',
  onSurfaceVariant: '#CAC4D0',
  onBackground: '#E1E1E1',
  text: '#FFFFFF',
  textSecondary: '#B3B3B3',
  border: '#333333',
  whiteBackground: '#1E1E1E',
};

// Material Design 3 Light Theme
export const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    onPrimary: colors.onPrimary,
    primaryContainer: '#FFE6E8',
    onPrimaryContainer: '#3E000A',
    secondary: colors.secondary,
    onSecondary: colors.onSecondary,
    secondaryContainer: '#E1F4FF',
    onSecondaryContainer: '#001D36',
    tertiary: '#7B5800',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFDF9E',
    onTertiaryContainer: '#271900',
    error: colors.error,
    onError: colors.onError,
    errorContainer: colors.errorContainer,
    onErrorContainer: colors.onErrorContainer,
    background: colors.background,
    onBackground: colors.onBackground,
    surface: colors.surface,
    onSurface: colors.onSurface,
    surfaceVariant: colors.surfaceVariant,
    onSurfaceVariant: colors.onSurfaceVariant,
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
    elevation: {
      level0: 'transparent',
      level1: '#F7F2FA',
      level2: '#F1EAFF',
      level3: '#ECE6F0',
      level4: '#EAE7F0',
      level5: '#E6E0E9',
    },
  },
};

// Material Design 3 Dark Theme
export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: darkColors.primary,
    onPrimary: '#680E20',
    primaryContainer: '#8F2734',
    onPrimaryContainer: '#FFE6E8',
    secondary: '#B1C5FF',
    onSecondary: '#002C69',
    secondaryContainer: '#004494',
    onSecondaryContainer: '#E1F4FF',
    tertiary: '#E5C16F',
    onTertiary: '#3F2E00',
    tertiaryContainer: '#5B4300',
    onTertiaryContainer: '#FFDF9E',
    error: '#FFB4AB',
    onError: '#690005',
    errorContainer: '#93000A',
    onErrorContainer: '#FFDAD6',
    background: darkColors.background,
    onBackground: darkColors.onBackground,
    surface: darkColors.surface,
    onSurface: darkColors.onSurface,
    surfaceVariant: darkColors.surfaceVariant,
    onSurfaceVariant: darkColors.onSurfaceVariant,
    outline: '#938F99',
    outlineVariant: '#49454F',
    elevation: {
      level0: 'transparent',
      level1: '#1D1B20',
      level2: '#232025',
      level3: '#2A252B',
      level4: '#2C2730',
      level5: '#2F2A36',
    },
  },
};

// Typography
export const typography = {
  displayLarge: {
    fontSize: 57,
    lineHeight: 64,
    fontWeight: '400' as const,
  },
  displayMedium: {
    fontSize: 45,
    lineHeight: 52,
    fontWeight: '400' as const,
  },
  displaySmall: {
    fontSize: 36,
    lineHeight: 44,
    fontWeight: '400' as const,
  },
  headlineLarge: {
    fontSize: 32,
    lineHeight: 40,
    fontWeight: '400' as const,
  },
  headlineMedium: {
    fontSize: 28,
    lineHeight: 36,
    fontWeight: '400' as const,
  },
  headlineSmall: {
    fontSize: 24,
    lineHeight: 32,
    fontWeight: '400' as const,
  },
  titleLarge: {
    fontSize: 22,
    lineHeight: 28,
    fontWeight: '400' as const,
  },
  titleMedium: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500' as const,
  },
  titleSmall: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500' as const,
  },
  labelLarge: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '500' as const,
  },
  labelMedium: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '500' as const,
  },
  labelSmall: {
    fontSize: 11,
    lineHeight: 16,
    fontWeight: '500' as const,
  },
  bodyLarge: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400' as const,
  },
  bodyMedium: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400' as const,
  },
  bodySmall: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
  },
};

// Spacing
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Border Radius
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  full: 9999,
};

export default {
  colors,
  darkColors,
  lightTheme,
  darkTheme,
  typography,
  spacing,
  borderRadius,
}; 