import { useState, useCallback } from 'react';
import { mediaUploadService, type UploadResult, type UploadProgress } from '../utils/mediaUploadService';
import { showErrorDialog, showSuccessDialog } from '../stores';

export interface MediaUploadState {
  isUploading: boolean;
  progress: UploadProgress;
  uploadedFiles: UploadResult[];
  errors: string[];
}

export interface UseMediaUploadReturn {
  state: MediaUploadState;
  uploadMedia: (uris: string[], eventId: string) => Promise<UploadResult[]>;
  uploadSingleMedia: (uri: string, eventId: string) => Promise<UploadResult>;
  clearUploads: () => void;
  removeUploadedFile: (index: number) => void;
  retryUpload: (uri: string, eventId: string) => Promise<UploadResult>;
}

export const useMediaUpload = (): UseMediaUploadReturn => {
  const [state, setState] = useState<MediaUploadState>({
    isUploading: false,
    progress: { loaded: 0, total: 0, percentage: 0 },
    uploadedFiles: [],
    errors: [],
  });

  const updateProgress = useCallback((progress: UploadProgress) => {
    setState(prev => ({
      ...prev,
      progress,
    }));
  }, []);

  const uploadSingleMedia = useCallback(async (uri: string, eventId: string): Promise<UploadResult> => {
    try {
      setState(prev => ({
        ...prev,
        isUploading: true,
        errors: [],
      }));

      const result = await mediaUploadService.uploadMedia(uri, eventId, updateProgress);
      
      setState(prev => ({
        ...prev,
        uploadedFiles: [...prev.uploadedFiles, result],
        isUploading: false,
        progress: { loaded: 100, total: 100, percentage: 100 },
      }));

      showSuccessDialog('Media uploaded successfully!', 'Success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      setState(prev => ({
        ...prev,
        isUploading: false,
        errors: [...prev.errors, errorMessage],
      }));

      showErrorDialog(errorMessage, 'Upload Error');
      throw error;
    }
  }, [updateProgress]);

  const uploadMedia = useCallback(async (uris: string[], eventId: string): Promise<UploadResult[]> => {
    if (uris.length === 0) {
      return [];
    }

    try {
      setState(prev => ({
        ...prev,
        isUploading: true,
        errors: [],
        progress: { loaded: 0, total: uris.length * 100, percentage: 0 },
      }));

      const results = await mediaUploadService.uploadMultipleMedia(uris, eventId, updateProgress);
      
      setState(prev => ({
        ...prev,
        uploadedFiles: [...prev.uploadedFiles, ...results],
        isUploading: false,
        progress: { loaded: uris.length * 100, total: uris.length * 100, percentage: 100 },
      }));

      showSuccessDialog(`${results.length} media files uploaded successfully!`, 'Success');
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      setState(prev => ({
        ...prev,
        isUploading: false,
        errors: [...prev.errors, errorMessage],
      }));

      showErrorDialog(errorMessage, 'Upload Error');
      throw error;
    }
  }, [updateProgress]);

  const clearUploads = useCallback(() => {
    setState({
      isUploading: false,
      progress: { loaded: 0, total: 0, percentage: 0 },
      uploadedFiles: [],
      errors: [],
    });
  }, []);

  const removeUploadedFile = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      uploadedFiles: prev.uploadedFiles.filter((_, i) => i !== index),
    }));
  }, []);

  const retryUpload = useCallback(async (uri: string, eventId: string): Promise<UploadResult> => {
    // Remove any previous errors for this upload
    setState(prev => ({
      ...prev,
      errors: prev.errors.filter(error => !error.includes(uri)),
    }));

    return uploadSingleMedia(uri, eventId);
  }, [uploadSingleMedia]);

  return {
    state,
    uploadMedia,
    uploadSingleMedia,
    clearUploads,
    removeUploadedFile,
    retryUpload,
  };
};
