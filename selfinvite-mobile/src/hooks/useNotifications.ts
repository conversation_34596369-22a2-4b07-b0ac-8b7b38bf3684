import { useEffect, useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as Notifications from 'expo-notifications';
import { notificationService, type NotificationPreferences } from '../services/notificationService';
import logger from '../common/logger';

export interface UseNotificationsReturn {
  // State
  isInitialized: boolean;
  isEnabled: boolean;
  pushToken: string | null;
  preferences: NotificationPreferences | null;
  
  // Actions
  initialize: () => Promise<void>;
  requestPermissions: () => Promise<boolean>;
  clearBadgeCount: () => Promise<void>;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
  scheduleEventReminder: (eventId: string, eventTitle: string, eventDate: Date) => Promise<string>;
  scheduleBookingReminder: (bookingId: string, eventTitle: string, eventDate: Date) => Promise<string>;
}

export function useNotifications(): UseNotificationsReturn {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [pushToken, setPushToken] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Initialize notifications
  const initialize = useCallback(async () => {
    try {
      await notificationService.initialize();
      setIsInitialized(true);
      
      const enabled = await notificationService.areNotificationsEnabled();
      setIsEnabled(enabled);
      
      const token = notificationService.getPushToken();
      setPushToken(token);
      
      logger.info('Notifications initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize notifications:', error);
    }
  }, []);

  // Request permissions
  const requestPermissions = useCallback(async (): Promise<boolean> => {
    try {
      const granted = await notificationService.requestPermissions();
      if (granted) {
        await initialize();
      }
      return granted;
    } catch (error) {
      logger.error('Failed to request notification permissions:', error);
      return false;
    }
  }, [initialize]);

  // Clear badge count
  const clearBadgeCount = useCallback(async () => {
    try {
      await notificationService.clearBadgeCount();
    } catch (error) {
      logger.error('Failed to clear badge count:', error);
    }
  }, []);

  // Get notification preferences
  const { data: preferences, refetch: refetchPreferences } = useQuery({
    queryKey: ['notificationPreferences'],
    queryFn: notificationService.getNotificationPreferences,
    enabled: isInitialized,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update notification preferences
  const updatePreferencesMutation = useMutation({
    mutationFn: notificationService.updateNotificationPreferences,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notificationPreferences'] });
      logger.info('Notification preferences updated successfully');
    },
    onError: (error) => {
      logger.error('Failed to update notification preferences:', error);
    },
  });

  const updatePreferences = useCallback(async (newPreferences: Partial<NotificationPreferences>) => {
    updatePreferencesMutation.mutate(newPreferences);
  }, [updatePreferencesMutation]);

  // Schedule event reminder
  const scheduleEventReminder = useCallback(async (
    eventId: string,
    eventTitle: string,
    eventDate: Date
  ): Promise<string> => {
    try {
      const reminderTime = new Date(eventDate.getTime() - 2 * 60 * 60 * 1000); // 2 hours before
      const now = new Date();
      
      if (reminderTime <= now) {
        throw new Error('Event is too soon to schedule a reminder');
      }

      const notificationId = await notificationService.scheduleNotification({
        id: `event-reminder-${eventId}`,
        title: 'Event Reminder',
        body: `Your event "${eventTitle}" is starting in 2 hours!`,
        data: {
          type: 'event',
          priority: 'high',
          category: 'EVENT',
          data: { eventId },
          deepLink: `selfinvite://event/${eventId}`,
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.DATE,
          date: reminderTime,
        },
      });

      logger.info('Scheduled event reminder:', notificationId);
      return notificationId;
    } catch (error) {
      logger.error('Failed to schedule event reminder:', error);
      throw error;
    }
  }, []);

  // Schedule booking reminder
  const scheduleBookingReminder = useCallback(async (
    bookingId: string,
    eventTitle: string,
    eventDate: Date
  ): Promise<string> => {
    try {
      const reminderTime = new Date(eventDate.getTime() - 24 * 60 * 60 * 1000); // 24 hours before
      const now = new Date();
      
      if (reminderTime <= now) {
        throw new Error('Event is too soon to schedule a reminder');
      }

      const notificationId = await notificationService.scheduleNotification({
        id: `booking-reminder-${bookingId}`,
        title: 'Upcoming Event',
        body: `Don't forget about "${eventTitle}" tomorrow!`,
        data: {
          type: 'event',
          priority: 'normal',
          category: 'EVENT',
          data: { bookingId, eventTitle },
          deepLink: `selfinvite://booking/${bookingId}`,
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.DATE,
          date: reminderTime,
        },
      });

      logger.info('Scheduled booking reminder:', notificationId);
      return notificationId;
    } catch (error) {
      logger.error('Failed to schedule booking reminder:', error);
      throw error;
    }
  }, []);

  // Initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  return {
    // State
    isInitialized,
    isEnabled,
    pushToken,
    preferences: preferences || null,
    
    // Actions
    initialize,
    requestPermissions,
    clearBadgeCount,
    updatePreferences,
    scheduleEventReminder,
    scheduleBookingReminder,
  };
}

// Hook for notification-specific actions
export function useNotificationActions() {
  const { clearBadgeCount } = useNotifications();

  const handleMessageNotification = useCallback(async (messageData: any) => {
    // Clear badge when user opens messages
    await clearBadgeCount();
    
    // Navigate to specific chat if needed
    if (messageData.threadId) {
      // Navigation logic here
      logger.info('Navigating to chat thread:', messageData.threadId);
    }
  }, [clearBadgeCount]);

  const handleBookingNotification = useCallback(async (bookingData: any) => {
    // Navigate to booking details
    if (bookingData.bookingId) {
      logger.info('Navigating to booking:', bookingData.bookingId);
    }
  }, []);

  const handlePaymentNotification = useCallback(async (paymentData: any) => {
    // Navigate to payment details
    if (paymentData.paymentId) {
      logger.info('Navigating to payment:', paymentData.paymentId);
    }
  }, []);

  const handleEventNotification = useCallback(async (eventData: any) => {
    // Navigate to event details
    if (eventData.eventId) {
      logger.info('Navigating to event:', eventData.eventId);
    }
  }, []);

  return {
    handleMessageNotification,
    handleBookingNotification,
    handlePaymentNotification,
    handleEventNotification,
  };
}
