import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query'
import { messagesApi } from '../api/messagesApi'
import type { Message, Conversation, SendMessageRequest } from '../types'

// Query Keys
export const messageKeys = {
  all: ['messages'] as const,
  conversations: () => [...messageKeys.all, 'conversations'] as const,
  conversation: (conversationId: string) => [...messageKeys.all, 'conversation', conversationId] as const,
  messages: (conversationId: string) => [...messageKeys.all, 'messages', conversationId] as const,
  unreadCount: () => [...messageKeys.all, 'unread-count'] as const,
}

// Get user's conversations
export const useConversations = () => {
  return useQuery({
    queryKey: messageKeys.conversations(),
    queryFn: () => messagesApi.getConversations(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get single conversation
export const useConversation = (conversationId: string) => {
  return useQuery({
    queryKey: messageKeys.conversation(conversationId),
    queryFn: () => messagesApi.getConversation(conversationId),
    enabled: !!conversationId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get messages for a conversation (with infinite scroll)
export const useMessages = (conversationId: string, limit: number = 20) => {
  return useInfiniteQuery({
    queryKey: messageKeys.messages(conversationId),
    queryFn: ({ pageParam }: { pageParam?: string }) => messagesApi.getMessages(conversationId, { 
      limit, 
      before: pageParam 
    }),
    initialPageParam: undefined,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    enabled: !!conversationId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get unread message count
export const useUnreadCount = () => {
  return useQuery({
    queryKey: messageKeys.unreadCount(),
    queryFn: () => messagesApi.getUnreadCount(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  })
}

// Send message
export const useSendMessage = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ conversationId, message }: { conversationId: string; message: SendMessageRequest }) =>
      messagesApi.sendMessage(conversationId, message),
    onSuccess: (newMessage, variables) => {
      // Optimistically add message to cache
      queryClient.setQueryData(
        messageKeys.messages(variables.conversationId),
        (oldData: any) => {
          if (!oldData) return oldData
          
          return {
            ...oldData,
            pages: oldData.pages.map((page: any, index: number) => {
              if (index === 0) {
                return {
                  ...page,
                  messages: [newMessage, ...page.messages],
                }
              }
              return page
            }),
          }
        }
      )
      
      // Invalidate conversation to update last message
      queryClient.invalidateQueries({ queryKey: messageKeys.conversation(variables.conversationId) })
      queryClient.invalidateQueries({ queryKey: messageKeys.conversations() })
    },
    onError: (error) => {
      console.error('Failed to send message:', error)
    },
  })
}

// Mark messages as read
export const useMarkAsRead = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ conversationId, messageIds }: { conversationId: string; messageIds: string[] }) =>
      messagesApi.markAsRead(conversationId, messageIds),
    onSuccess: (_, variables) => {
      // Invalidate unread count
      queryClient.invalidateQueries({ queryKey: messageKeys.unreadCount() })
      
      // Invalidate conversations list to update unread status
      queryClient.invalidateQueries({ queryKey: messageKeys.conversations() })
    },
    onError: (error) => {
      console.error('Failed to mark messages as read:', error)
    },
  })
}

// Delete message
export const useDeleteMessage = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ conversationId, messageId }: { conversationId: string; messageId: string }) =>
      messagesApi.deleteMessage(conversationId, messageId),
    onSuccess: (_, variables) => {
      // Remove message from cache
      queryClient.setQueryData(
        messageKeys.messages(variables.conversationId),
        (oldData: any) => {
          if (!oldData) return oldData
          
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              messages: page.messages.filter((msg: Message) => msg.id !== variables.messageId),
            })),
          }
        }
      )
    },
    onError: (error) => {
      console.error('Failed to delete message:', error)
    },
  })
}

// Create new conversation
export const useCreateConversation = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (participantIds: string[]) => messagesApi.createConversation(participantIds),
    onSuccess: (newConversation: Conversation) => {
      // Add to conversations list
      queryClient.setQueryData(
        messageKeys.conversations(),
        (oldData: Conversation[] | undefined) => {
          if (!oldData) return [newConversation]
          return [newConversation, ...oldData]
        }
      )
      
      // Add conversation detail to cache
      queryClient.setQueryData(messageKeys.conversation(newConversation.id), newConversation)
    },
    onError: (error) => {
      console.error('Failed to create conversation:', error)
    },
  })
}

// Leave conversation
export const useLeaveConversation = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (conversationId: string) => messagesApi.leaveConversation(conversationId),
    onSuccess: (_, conversationId) => {
      // Remove from conversations list
      queryClient.setQueryData(
        messageKeys.conversations(),
        (oldData: Conversation[] | undefined) => {
          if (!oldData) return []
          return oldData.filter(conv => conv.id !== conversationId)
        }
      )
      
      // Remove conversation from cache
      queryClient.removeQueries({ queryKey: messageKeys.conversation(conversationId) })
      queryClient.removeQueries({ queryKey: messageKeys.messages(conversationId) })
    },
    onError: (error) => {
      console.error('Failed to leave conversation:', error)
    },
  })
}

// Block user
export const useBlockUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (userId: string) => messagesApi.blockUser(userId),
    onSuccess: () => {
      // Invalidate conversations to refresh blocked status
      queryClient.invalidateQueries({ queryKey: messageKeys.conversations() })
    },
    onError: (error) => {
      console.error('Failed to block user:', error)
    },
  })
}

// Unblock user
export const useUnblockUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (userId: string) => messagesApi.unblockUser(userId),
    onSuccess: () => {
      // Invalidate conversations to refresh blocked status
      queryClient.invalidateQueries({ queryKey: messageKeys.conversations() })
    },
    onError: (error) => {
      console.error('Failed to unblock user:', error)
    },
  })
} 