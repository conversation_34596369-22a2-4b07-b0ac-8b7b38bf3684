import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { reviewsApi } from '../api/reviewsApi'
import type { Review, CreateReviewRequest, UpdateReviewRequest } from '../types'

// Query Keys
export const reviewKeys = {
  all: ['reviews'] as const,
  lists: () => [...reviewKeys.all, 'list'] as const,
  list: (filters?: any) => [...reviewKeys.lists(), filters] as const,
  details: () => [...reviewKeys.all, 'detail'] as const,
  detail: (id: string) => [...reviewKeys.details(), id] as const,
  myReviews: () => [...reviewKeys.all, 'my-reviews'] as const,
  receivedReviews: () => [...reviewKeys.all, 'received-reviews'] as const,
  eventReviews: (eventId: string) => [...reviewKeys.all, 'event', eventId] as const,
  userReviews: (userId: string) => [...reviewKeys.all, 'user', userId] as const,
}

// Get all reviews with filters
export const useReviews = (filters?: any) => {
  return useQuery({
    queryKey: reviewKeys.list(filters),
    queryFn: () => reviewsApi.getReviews(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get single review by ID
export const useReviewDetail = (reviewId: string) => {
  return useQuery({
    queryKey: reviewKeys.detail(reviewId),
    queryFn: () => reviewsApi.getReviewDetail(reviewId),
    enabled: !!reviewId,
    staleTime: 5 * 60 * 1000,
  })
}

// Get user's sent reviews
export const useMyReviews = () => {
  return useQuery({
    queryKey: reviewKeys.myReviews(),
    queryFn: () => reviewsApi.getMyReviews(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get user's received reviews
export const useReceivedReviews = () => {
  return useQuery({
    queryKey: reviewKeys.receivedReviews(),
    queryFn: () => reviewsApi.getReceivedReviews(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get reviews for a specific event
export const useEventReviews = (eventId: string) => {
  return useQuery({
    queryKey: reviewKeys.eventReviews(eventId),
    queryFn: () => reviewsApi.getEventReviews(eventId),
    enabled: !!eventId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get reviews for a specific user
export const useUserReviews = (userId: string) => {
  return useQuery({
    queryKey: reviewKeys.userReviews(userId),
    queryFn: () => reviewsApi.getUserReviews(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Create new review
export const useCreateReview = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (reviewData: CreateReviewRequest) => reviewsApi.createReview(reviewData),
    onSuccess: (newReview) => {
      // Invalidate and refetch review lists
      queryClient.invalidateQueries({ queryKey: reviewKeys.lists() })
      queryClient.invalidateQueries({ queryKey: reviewKeys.myReviews() })
      queryClient.invalidateQueries({ queryKey: reviewKeys.receivedReviews() })
      
      // Add the new review to the cache
      queryClient.setQueryData(reviewKeys.detail(newReview.id), newReview)
    },
    onError: (error) => {
      console.error('Failed to create review:', error)
    },
  })
}

// Update review
export const useUpdateReview = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ reviewId, reviewData }: { reviewId: string; reviewData: UpdateReviewRequest }) =>
      reviewsApi.updateReview(reviewId, reviewData),
    onSuccess: (updatedReview, variables) => {
      // Update the review in cache
      queryClient.setQueryData(reviewKeys.detail(variables.reviewId), updatedReview)
      
      // Invalidate lists that might contain this review
      queryClient.invalidateQueries({ queryKey: reviewKeys.lists() })
      queryClient.invalidateQueries({ queryKey: reviewKeys.myReviews() })
      queryClient.invalidateQueries({ queryKey: reviewKeys.receivedReviews() })
    },
    onError: (error) => {
      console.error('Failed to update review:', error)
    },
  })
}

// Delete review
export const useDeleteReview = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (reviewId: string) => reviewsApi.deleteReview(reviewId),
    onSuccess: (_, reviewId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: reviewKeys.detail(reviewId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: reviewKeys.lists() })
      queryClient.invalidateQueries({ queryKey: reviewKeys.myReviews() })
      queryClient.invalidateQueries({ queryKey: reviewKeys.receivedReviews() })
    },
    onError: (error) => {
      console.error('Failed to delete review:', error)
    },
  })
}

// Report review
export const useReportReview = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ reviewId, reason }: { reviewId: string; reason: string }) =>
      reviewsApi.reportReview(reviewId, reason),
    onSuccess: () => {
      // Invalidate review lists to refresh reported status
      queryClient.invalidateQueries({ queryKey: reviewKeys.lists() })
    },
    onError: (error) => {
      console.error('Failed to report review:', error)
    },
  })
} 