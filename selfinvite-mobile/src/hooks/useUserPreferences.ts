import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userProfileApi } from '../api/userProfileApi'
import type { UserPreferences, UpdatePreferencesRequest } from '../types'
import logger from '../common/logger'

export const useUserPreferences = () => {
  return useQuery({
    queryKey: ['userPreferences'],
    queryFn: userProfileApi.getMyPreferences,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch user preferences:', error)
    },
  })
}

export const useUpdateUserPreferences = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (preferencesData: UpdatePreferencesRequest) => 
      userProfileApi.updateMyPreferences(preferencesData),
    onSuccess: (updatedPreferences) => {
      // Update the cache with the new preferences data
      queryClient.setQueryData(['userPreferences'], updatedPreferences)
      queryClient.invalidateQueries({ queryKey: ['userPreferences'] })
      logger.info('User preferences updated successfully')
    },
    onError: (error: Error) => {
      logger.error('Failed to update user preferences:', error)
    },
  })
}

export const useUserBadges = () => {
  return useQuery({
    queryKey: ['userBadges'],
    queryFn: userProfileApi.getMyBadges,
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch user badges:', error)
    },
  })
}

export const useUserStats = () => {
  return useQuery({
    queryKey: ['userStats'],
    queryFn: userProfileApi.getMyStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch user stats:', error)
    },
  })
}
