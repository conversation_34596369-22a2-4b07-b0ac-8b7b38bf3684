import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { userProfileApi } from '../api/userProfileApi'
import type { UserProfile, UpdateProfileRequest } from '../types'
import logger from '../common/logger'

export const useUserProfile = () => {
  return useQuery({
    queryKey: ['userProfile'],
    queryFn: userProfileApi.getMyProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch user profile:', error)
    },
  })
}

export const useUserProfileById = (userId: string) => {
  return useQuery({
    queryKey: ['userProfile', userId],
    queryFn: () => userProfileApi.getUserProfile(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
    retry: 2,
    onError: (error: Error) => {
      logger.error(`Failed to fetch user profile for ${userId}:`, error)
    },
  })
}

export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (profileData: UpdateProfileRequest) => 
      userProfileApi.updateMyProfile(profileData),
    onSuccess: (updatedProfile) => {
      // Update the cache with the new profile data
      queryClient.setQueryData(['userProfile'], updatedProfile)
      queryClient.invalidateQueries({ queryKey: ['userProfile'] })
      logger.info('User profile updated successfully')
    },
    onError: (error: Error) => {
      logger.error('Failed to update user profile:', error)
    },
  })
}

export const useUserStats = () => {
  return useQuery({
    queryKey: ['userStats'],
    queryFn: userProfileApi.getMyStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch user stats:', error)
    },
  })
}

export const useUserBadges = () => {
  return useQuery({
    queryKey: ['userBadges'],
    queryFn: userProfileApi.getMyBadges,
    staleTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch user badges:', error)
    },
  })
}

export const useBlockedUsers = () => {
  return useQuery({
    queryKey: ['blockedUsers'],
    queryFn: userProfileApi.getBlockedUsers,
    staleTime: 5 * 60 * 1000,
    retry: 2,
    onError: (error: Error) => {
      logger.error('Failed to fetch blocked users:', error)
    },
  })
}

export const useBlockUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (userId: string) => userProfileApi.blockUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blockedUsers'] })
      logger.info('User blocked successfully')
    },
    onError: (error: Error) => {
      logger.error('Failed to block user:', error)
    },
  })
}

export const useUnblockUser = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (userId: string) => userProfileApi.unblockUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blockedUsers'] })
      logger.info('User unblocked successfully')
    },
    onError: (error: Error) => {
      logger.error('Failed to unblock user:', error)
    },
  })
}

export const useReportUser = () => {
  return useMutation({
    mutationFn: ({ userId, reason, description }: { 
      userId: string
      reason: string
      description?: string 
    }) => userProfileApi.reportUser(userId, reason, description),
    onSuccess: () => {
      logger.info('User reported successfully')
    },
    onError: (error: Error) => {
      logger.error('Failed to report user:', error)
    },
  })
}

export const useDeleteAccount = () => {
  return useMutation({
    mutationFn: userProfileApi.deleteAccount,
    onSuccess: () => {
      logger.info('Account deleted successfully')
    },
    onError: (error: Error) => {
      logger.error('Failed to delete account:', error)
    },
  })
}
