import { useMemo } from 'react';
import i18n, { t, availableLocales, type Locale } from '../translations';

/**
 * Custom hook for internationalization
 * Provides easy access to translation functions and locale management
 */
export const useI18n = () => {
  const translate = useMemo(() => t, []);
  
  const currentLocale = useMemo(() => {
    return i18n.locale?.split('-')[0] as Locale || 'en';
  }, [i18n.locale]);

  const setLocale = (locale: Locale) => {
    i18n.locale = locale;
  };

  const isRTL = useMemo(() => {
    // Add RTL languages here if needed in the future
    const rtlLanguages = ['ar', 'he', 'fa'];
    return rtlLanguages.includes(currentLocale);
  }, [currentLocale]);

  return {
    t: translate,
    currentLocale,
    setLocale,
    availableLocales,
    isRTL,
    i18n,
  };
};

export default useI18n;
