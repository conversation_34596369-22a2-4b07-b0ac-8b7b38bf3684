import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import React, { useCallback, useMemo, useRef } from 'react'
import { searchApi } from '../api/searchApi'
import { useSearchStore } from '../stores/searchStore'
import { useAuthStore } from '../stores/authStore'
import type {
  SearchFilters,
  SearchResults,
  SearchSuggestion,
  SearchSortOptions,
} from '../types/search'
import type { Event } from '../types/event'
import { SEARCH_CONSTANTS } from '../types/search'
import log from '@/common/logger'

// Query Keys
export const searchKeys = {
  all: ['search'] as const,
  searches: () => [...searchKeys.all, 'searches'] as const,
  search: (query: string, filters: SearchFilters, sort?: SearchSortOptions[]) => 
    [...searchKeys.searches(), query, filters, sort] as const,
  infinite: (query: string, filters: SearchFilters, sort?: SearchSortOptions[]) => 
    [...searchKeys.search(query, filters, sort), 'infinite'] as const,
  suggestions: (query: string) => [...searchKeys.all, 'suggestions', query] as const,
  nearby: (lat: number, lng: number, radius: number, filters?: SearchFilters) => 
    [...searchKeys.all, 'nearby', lat, lng, radius, filters] as const,
  trending: () => [...searchKeys.all, 'trending'] as const,
  facets: (facetName: string) => [...searchKeys.all, 'facets', facetName] as const,
}

// Debounce hook
function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  callbackRef.current = callback

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args)
      }, delay) as unknown as NodeJS.Timeout
    }) as T,
    [delay]
  )
}

// Main search hook with pagination
export const useSearch = (
  query?: string,
  filters?: SearchFilters,
  options: {
    enabled?: boolean
    sort?: SearchSortOptions[]
    limit?: number
    staleTime?: number
  } = {}
) => {
  const { user } = useAuthStore()
  
  // Add user-specific filters
  const enhancedFilters = useMemo(() => {
    const userFilters = { ...filters }
    
    // Add blocked users filter if user is authenticated
    // Note: blocked users would need to be fetched from user preferences
    // For now, we'll skip this filter until user preferences are properly integrated
    
    return userFilters
  }, [filters, user])

  return useQuery({
    queryKey: searchKeys.search(query || '', enhancedFilters, options.sort),
    queryFn: () => searchApi.searchEvents(query, enhancedFilters, {
      sort: options.sort || [],
      limit: options.limit || SEARCH_CONSTANTS.DEFAULT_PAGE_SIZE,
      page: 0
    }),
    enabled: options.enabled !== false,
    staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Infinite scroll search hook
export const useInfiniteSearch = (
  query?: string,
  filters?: SearchFilters,
  options: {
    enabled?: boolean
    sort?: SearchSortOptions[]
    limit?: number
  } = {}
) => {
  const { user } = useAuthStore()
  
  // Add user-specific filters
  const enhancedFilters = useMemo(() => {
    const userFilters = { ...filters }
    
    // Add blocked users filter if user is authenticated
    // Note: blocked users would need to be fetched from user preferences
    // For now, we'll skip this filter until user preferences are properly integrated
    
    return userFilters
  }, [filters, user])

  return useInfiniteQuery({
    queryKey: searchKeys.infinite(query || '', enhancedFilters, options.sort),
    queryFn: ({ pageParam = 0 }) => 
      searchApi.searchEvents(query, enhancedFilters, {
        sort: options.sort || [],
        limit: options.limit || SEARCH_CONSTANTS.DEFAULT_PAGE_SIZE,
        page: pageParam
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.currentPage < lastPage.totalPages - 1 
        ? lastPage.currentPage + 1 
        : undefined
    },
    enabled: options.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Search suggestions hook with debouncing
export const useSearchSuggestions = (
  query: string,
  options: {
    enabled?: boolean
    limit?: number
    debounceMs?: number
  } = {}
) => {
  const debouncedQuery = useRef(query)
  
  // Update debounced query
  const updateDebouncedQuery = useDebounce((newQuery: string) => {
    debouncedQuery.current = newQuery
  }, options.debounceMs || SEARCH_CONSTANTS.DEBOUNCE_DELAY)

  // Trigger debounce when query changes
  if (query !== debouncedQuery.current) {
    updateDebouncedQuery(query)
  }

  return useQuery({
    queryKey: searchKeys.suggestions(debouncedQuery.current),
    queryFn: () => searchApi.getSearchSuggestions(
      debouncedQuery.current, 
      options.limit || 5
    ),
    enabled: (options.enabled !== false) && 
             debouncedQuery.current.length >= SEARCH_CONSTANTS.SUGGESTION_MIN_CHARS,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Nearby events hook
export const useNearbySearch = (
  lat?: number,
  lng?: number,
  radius?: number,
  filters?: SearchFilters,
  options: {
    enabled?: boolean
    limit?: number
  } = {}
) => {
  const { user } = useAuthStore()
  
  const enhancedFilters = useMemo(() => {
    const userFilters = { ...filters }
    
    // Add blocked users filter if user is authenticated
    // Note: blocked users would need to be fetched from user preferences
    // For now, we'll skip this filter until user preferences are properly integrated
    
    return userFilters
  }, [filters, user])

  return useQuery({
    queryKey: searchKeys.nearby(
      lat || 0, 
      lng || 0, 
      radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS, 
      enhancedFilters
    ),
    queryFn: () => searchApi.searchNearby(
      lat!,
      lng!,
      radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS,
      enhancedFilters,
      options.limit || SEARCH_CONSTANTS.DEFAULT_PAGE_SIZE
    ),
    enabled: (options.enabled !== false) && !!lat && !!lng,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Trending searches hook
export const useTrendingSearches = () => {
  return useQuery({
    queryKey: searchKeys.trending(),
    queryFn: () => searchApi.getTrendingSearches(),
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  })
}

// Facet values hook
export const useFacetValues = (facetName: string) => {
  return useQuery({
    queryKey: searchKeys.facets(facetName),
    queryFn: () => searchApi.getFacetValues(facetName),
    enabled: !!facetName,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  })
}

// Combined hook that integrates with search store
export const useSearchWithStore = () => {
  const {
    query,
    appliedFilters,
    sortOptions,
    searchMode,
    userLocation,
    setQuery,
    addRecentQuery,
    addToHistory,
    setActiveFilters,
    applyFilters
  } = useSearchStore()

  // Main search query
  const searchQuery = useInfiniteSearch(query, appliedFilters, {
    sort: sortOptions,
    enabled: true
  })

  // Nearby search when location is available
  const nearbyQuery = useNearbySearch(
    userLocation?.latitude,
    userLocation?.longitude,
    appliedFilters.radius,
    { ...appliedFilters }, // Remove coordinates to avoid duplication
    { enabled: !!userLocation && searchMode === 'map' }
  )

  // Search suggestions
  const suggestionsQuery = useSearchSuggestions(query, {
    enabled: query.length >= SEARCH_CONSTANTS.SUGGESTION_MIN_CHARS
  })

  // Trending searches
  const trendingQuery = useTrendingSearches()

  // Helper functions
  const performSearch = useCallback((newQuery: string, newFilters?: SearchFilters) => {
    if (newQuery !== query) {
      setQuery(newQuery)
      if (newQuery.trim()) {
        addRecentQuery(newQuery)
      }
    }
    
    if (newFilters) {
      setActiveFilters(newFilters)
      applyFilters()
    }

    // The search will be triggered automatically by the useInfiniteSearch hook
    // when the query or appliedFilters change
  }, [query, setQuery, addRecentQuery, setActiveFilters, applyFilters])

  // Get all events from infinite query
  const allEvents = useMemo(() => {
    if (searchMode === 'map' && nearbyQuery.data) {
      return nearbyQuery.data.hits
    }
    
    return searchQuery.data?.pages?.flatMap(page => page.hits) || []
  }, [searchQuery.data, nearbyQuery.data, searchMode])

  // Get total count
  const totalCount = useMemo(() => {
    if (searchMode === 'map' && nearbyQuery.data) {
      return nearbyQuery.data.totalHits
    }
    
    return searchQuery.data?.pages?.[0]?.totalHits || 0
  }, [searchQuery.data, nearbyQuery.data, searchMode])

  // Add search to history when results are available
  React.useEffect(() => {
    if (searchQuery.data && searchQuery.data.pages.length > 0 && query.trim()) {
      addToHistory({
        query,
        filters: appliedFilters,
        resultCount: searchQuery.data.pages[0]?.totalHits || 0
      })
    }
  }, [searchQuery.data, query, appliedFilters, addToHistory])

  // Loading states
  const isLoading = searchQuery.isLoading || (searchMode === 'map' && nearbyQuery.isLoading)
  const isFetchingNextPage = searchQuery.isFetchingNextPage
  const hasNextPage = searchQuery.hasNextPage

  return {
    // Data
    events: allEvents,
    totalCount,
    facetDistribution: searchQuery.data?.pages?.[0]?.facetDistribution,
    suggestions: suggestionsQuery.data || [],
    trending: trendingQuery.data || [],
    
    // Loading states
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    isError: searchQuery.isError || nearbyQuery.isError,
    error: searchQuery.error || nearbyQuery.error,
    
    // Actions
    performSearch,
    fetchNextPage: searchQuery.fetchNextPage,
    refetch: searchQuery.refetch,
    
    // Store state
    query,
    filters: appliedFilters,
    searchMode,
    userLocation,
  }
}

// Analytics hook for tracking search behavior
export const useSearchAnalytics = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (event: {
      query: string
      filters: SearchFilters
      resultCount: number
      clickedEventId?: string
    }) => {
      // This would typically send to an analytics service
      log.info('Search analytics:', event)
      
      // For now, just log the event
      return Promise.resolve()
    },
    onSuccess: () => {
      // Could invalidate analytics queries here
    }
  })
}

// Health check hook
export const useSearchHealth = () => {
  return useQuery({
    queryKey: [...searchKeys.all, 'health'],
    queryFn: () => searchApi.healthCheck(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}
