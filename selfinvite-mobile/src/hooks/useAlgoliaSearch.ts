import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useCallback, useMemo, useRef } from 'react';
import { algoliaSearchService } from '../services/algoliaService';
import type {
  SearchFilters,
  SearchResults,
  SearchSuggestion,
  SearchSortOptions,
} from '../types/search';
import type { Event } from '../types/event';
import { SEARCH_CONSTANTS } from '../types/search';
import log from '../common/logger';

// Query Keys for Algolia search
export const algoliaSearchKeys = {
  all: ['algolia-search'] as const,
  searches: () => [...algoliaSearchKeys.all, 'searches'] as const,
  search: (query: string, filters: SearchFilters, sort?: SearchSortOptions[]) => 
    [...algoliaSearchKeys.searches(), query, filters, sort] as const,
  infinite: (query: string, filters: SearchFilters, sort?: SearchSortOptions[]) => 
    [...algoliaSearchKeys.search(query, filters, sort), 'infinite'] as const,
  suggestions: (query: string) => [...algoliaSearchKeys.all, 'suggestions', query] as const,
  nearby: (lat: number, lng: number, radius: number, filters?: SearchFilters) => 
    [...algoliaSearchKeys.all, 'nearby', lat, lng, radius, filters] as const,
  health: () => [...algoliaSearchKeys.all, 'health'] as const,
};

// Debounce hook for search input
function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  callbackRef.current = callback;

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args);
      }, delay) as unknown as NodeJS.Timeout;
    }) as T,
    [delay]
  );
}

// Main Algolia search hook with pagination
export const useAlgoliaSearch = (
  query?: string,
  filters?: SearchFilters,
  options: {
    enabled?: boolean;
    sort?: SearchSortOptions[];
    limit?: number;
    staleTime?: number;
  } = {}
) => {
  return useQuery({
    queryKey: algoliaSearchKeys.search(query || '', filters || {}, options.sort),
    queryFn: () => algoliaSearchService.searchEvents(query, filters || {}, {
      sort: options.sort || [],
      limit: options.limit || SEARCH_CONSTANTS.DEFAULT_PAGE_SIZE,
      page: 0
    }),
    enabled: options.enabled !== false,
    staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Infinite scroll Algolia search hook
export const useInfiniteAlgoliaSearch = (
  query?: string,
  filters?: SearchFilters,
  options: {
    enabled?: boolean;
    sort?: SearchSortOptions[];
    limit?: number;
  } = {}
) => {
  return useInfiniteQuery({
    queryKey: algoliaSearchKeys.infinite(query || '', filters || {}, options.sort),
    queryFn: ({ pageParam = 0 }) => 
      algoliaSearchService.searchEvents(query, filters || {}, {
        sort: options.sort || [],
        limit: options.limit || SEARCH_CONSTANTS.DEFAULT_PAGE_SIZE,
        page: pageParam
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.currentPage < lastPage.totalPages - 1 
        ? lastPage.currentPage + 1 
        : undefined;
    },
    enabled: options.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Algolia search suggestions hook with debouncing
export const useAlgoliaSearchSuggestions = (
  query: string,
  options: {
    enabled?: boolean;
    limit?: number;
    debounceMs?: number;
  } = {}
) => {
  const debouncedQuery = useRef(query);
  
  // Update debounced query
  const updateDebouncedQuery = useDebounce((newQuery: string) => {
    debouncedQuery.current = newQuery;
  }, options.debounceMs || SEARCH_CONSTANTS.DEBOUNCE_DELAY);

  // Trigger debounce when query changes
  if (query !== debouncedQuery.current) {
    updateDebouncedQuery(query);
  }

  return useQuery({
    queryKey: algoliaSearchKeys.suggestions(debouncedQuery.current),
    queryFn: () => algoliaSearchService.getSearchSuggestions(
      debouncedQuery.current, 
      options.limit || 5
    ),
    enabled: (options.enabled !== false) && 
             debouncedQuery.current.length >= SEARCH_CONSTANTS.SUGGESTION_MIN_CHARS,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Nearby events hook using Algolia
export const useAlgoliaNearbySearch = (
  lat?: number,
  lng?: number,
  radius?: number,
  filters?: SearchFilters,
  options: {
    enabled?: boolean;
    limit?: number;
  } = {}
) => {
  return useQuery({
    queryKey: algoliaSearchKeys.nearby(
      lat || 0, 
      lng || 0, 
      radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS, 
      filters
    ),
    queryFn: () => algoliaSearchService.searchNearby(
      lat!,
      lng!,
      radius || SEARCH_CONSTANTS.DEFAULT_SEARCH_RADIUS,
      filters || {},
      options.limit || SEARCH_CONSTANTS.DEFAULT_PAGE_SIZE
    ),
    enabled: (options.enabled !== false) && !!lat && !!lng,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Algolia health check hook
export const useAlgoliaHealth = () => {
  return useQuery({
    queryKey: algoliaSearchKeys.health(),
    queryFn: () => algoliaSearchService.healthCheck(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Combined hook that provides a complete Algolia search experience
export const useAlgoliaSearchWithStore = (
  query?: string,
  filters?: SearchFilters,
  options: {
    enabled?: boolean;
    sort?: SearchSortOptions[];
    limit?: number;
    useInfinite?: boolean;
  } = {}
) => {
  // Main search query
  const searchQuery = useAlgoliaSearch(query, filters, {
    sort: options.sort,
    limit: options.limit,
    enabled: (options.enabled && !options.useInfinite) || false
  });

  // Infinite search query
  const infiniteSearchQuery = useInfiniteAlgoliaSearch(query, filters, {
    sort: options.sort,
    limit: options.limit,
    enabled: (options.enabled && options.useInfinite) || false
  });

  // Search suggestions
  const suggestionsQuery = useAlgoliaSearchSuggestions(query || '', {
    enabled: (query?.length || 0) >= SEARCH_CONSTANTS.SUGGESTION_MIN_CHARS
  });

  // Health check
  const healthQuery = useAlgoliaHealth();

  // Get all events from infinite query
  const allEvents = useMemo(() => {
    if (options.useInfinite && infiniteSearchQuery.data) {
      return infiniteSearchQuery.data.pages?.flatMap(page => page.hits) || [];
    }
    
    return searchQuery.data?.hits || [];
  }, [searchQuery.data, infiniteSearchQuery.data, options.useInfinite]);

  // Get total count
  const totalCount = useMemo(() => {
    if (options.useInfinite && infiniteSearchQuery.data) {
      return infiniteSearchQuery.data.pages?.[0]?.totalHits || 0;
    }
    
    return searchQuery.data?.totalHits || 0;
  }, [searchQuery.data, infiniteSearchQuery.data, options.useInfinite]);

  // Loading states
  const isLoading = options.useInfinite 
    ? infiniteSearchQuery.isLoading 
    : searchQuery.isLoading;
  const isFetchingNextPage = infiniteSearchQuery.isFetchingNextPage;
  const hasNextPage = infiniteSearchQuery.hasNextPage;

  return {
    // Data
    events: allEvents,
    totalCount,
    facetDistribution: options.useInfinite 
      ? infiniteSearchQuery.data?.pages?.[0]?.facetDistribution
      : searchQuery.data?.facetDistribution,
    suggestions: suggestionsQuery.data || [],
    
    // Loading states
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    isError: options.useInfinite 
      ? infiniteSearchQuery.isError 
      : searchQuery.isError,
    error: options.useInfinite 
      ? infiniteSearchQuery.error 
      : searchQuery.error,
    
    // Health status
    isHealthy: healthQuery.data,
    healthError: healthQuery.error,
    
    // Actions
    fetchNextPage: infiniteSearchQuery.fetchNextPage,
    refetch: options.useInfinite 
      ? infiniteSearchQuery.refetch 
      : searchQuery.refetch,
  };
};

// Analytics hook for tracking Algolia search behavior
export const useAlgoliaSearchAnalytics = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (event: {
      query: string;
      filters: SearchFilters;
      resultCount: number;
      clickedEventId?: string;
    }) => {
      // This would typically send to an analytics service
      log.info('Algolia search analytics:', event);
      
      // For now, just log the event
      return Promise.resolve();
    },
    onSuccess: () => {
      // Could invalidate analytics queries here
    }
  });
};

// Utility hook for price range searches
export const useAlgoliaPriceRangeSearch = (
  minPrice: number,
  maxPrice: number,
  query?: string,
  options: {
    enabled?: boolean;
  } = {}
) => {
  return useQuery({
    queryKey: [...algoliaSearchKeys.all, 'price-range', minPrice, maxPrice, query],
    queryFn: () => algoliaSearchService.searchByPriceRange(minPrice, maxPrice, query),
    enabled: options.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Utility hook for city searches
export const useAlgoliaCitySearch = (
  city: string,
  query?: string,
  options: {
    enabled?: boolean;
  } = {}
) => {
  return useQuery({
    queryKey: [...algoliaSearchKeys.all, 'city', city, query],
    queryFn: () => algoliaSearchService.searchByCity(city, query),
    enabled: options.enabled !== false && !!city,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Utility hook for event type searches
export const useAlgoliaEventTypeSearch = (
  eventTypes: string[],
  query?: string,
  options: {
    enabled?: boolean;
  } = {}
) => {
  return useQuery({
    queryKey: [...algoliaSearchKeys.all, 'event-types', eventTypes, query],
    queryFn: () => algoliaSearchService.searchByEventType(eventTypes, query),
    enabled: options.enabled !== false && eventTypes.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
