import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { feedbackApi } from '../api/feedbackApi'
import type { Feedback, CreateFeedbackRequest, UpdateFeedbackRequest } from '../types'

// Query Keys
export const feedbackKeys = {
  all: ['feedback'] as const,
  lists: () => [...feedbackKeys.all, 'list'] as const,
  list: (filters?: any) => [...feedbackKeys.lists(), filters] as const,
  details: () => [...feedbackKeys.all, 'detail'] as const,
  detail: (id: string) => [...feedbackKeys.details(), id] as const,
  myFeedback: () => [...feedbackKeys.all, 'my-feedback'] as const,
  receivedFeedback: () => [...feedbackKeys.all, 'received-feedback'] as const,
  eventFeedback: (eventId: string) => [...feedbackKeys.all, 'event', eventId] as const,
}

// Get all feedback with filters
export const useFeedback = (filters?: any) => {
  return useQuery({
    queryKey: feedbackKeys.list(filters),
    queryFn: () => feedbackApi.getFeedback(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get single feedback by ID
export const useFeedbackDetail = (feedbackId: string) => {
  return useQuery({
    queryKey: feedbackKeys.detail(feedbackId),
    queryFn: () => feedbackApi.getFeedbackDetail(feedbackId),
    enabled: !!feedbackId,
    staleTime: 5 * 60 * 1000,
  })
}

// Get user's sent feedback
export const useMyFeedback = () => {
  return useQuery({
    queryKey: feedbackKeys.myFeedback(),
    queryFn: () => feedbackApi.getMyFeedback(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get user's received feedback
export const useReceivedFeedback = () => {
  return useQuery({
    queryKey: feedbackKeys.receivedFeedback(),
    queryFn: () => feedbackApi.getReceivedFeedback(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get feedback for a specific event
export const useEventFeedback = (eventId: string) => {
  return useQuery({
    queryKey: feedbackKeys.eventFeedback(eventId),
    queryFn: () => feedbackApi.getEventFeedback(eventId),
    enabled: !!eventId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Create new feedback
export const useCreateFeedback = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (feedbackData: CreateFeedbackRequest) => feedbackApi.createFeedback(feedbackData),
    onSuccess: (newFeedback) => {
      // Invalidate and refetch feedback lists
      queryClient.invalidateQueries({ queryKey: feedbackKeys.lists() })
      queryClient.invalidateQueries({ queryKey: feedbackKeys.myFeedback() })
      queryClient.invalidateQueries({ queryKey: feedbackKeys.receivedFeedback() })
      
      // Add the new feedback to the cache
      queryClient.setQueryData(feedbackKeys.detail(newFeedback.id), newFeedback)
    },
    onError: (error) => {
      console.error('Failed to create feedback:', error)
    },
  })
}

// Update feedback
export const useUpdateFeedback = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ feedbackId, feedbackData }: { feedbackId: string; feedbackData: UpdateFeedbackRequest }) =>
      feedbackApi.updateFeedback(feedbackId, feedbackData),
    onSuccess: (updatedFeedback, variables) => {
      // Update the feedback in cache
      queryClient.setQueryData(feedbackKeys.detail(variables.feedbackId), updatedFeedback)
      
      // Invalidate lists that might contain this feedback
      queryClient.invalidateQueries({ queryKey: feedbackKeys.lists() })
      queryClient.invalidateQueries({ queryKey: feedbackKeys.myFeedback() })
      queryClient.invalidateQueries({ queryKey: feedbackKeys.receivedFeedback() })
    },
    onError: (error) => {
      console.error('Failed to update feedback:', error)
    },
  })
}

// Delete feedback
export const useDeleteFeedback = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (feedbackId: string) => feedbackApi.deleteFeedback(feedbackId),
    onSuccess: (_, feedbackId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: feedbackKeys.detail(feedbackId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: feedbackKeys.lists() })
      queryClient.invalidateQueries({ queryKey: feedbackKeys.myFeedback() })
      queryClient.invalidateQueries({ queryKey: feedbackKeys.receivedFeedback() })
    },
    onError: (error) => {
      console.error('Failed to delete feedback:', error)
    },
  })
}

// Report feedback
export const useReportFeedback = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ feedbackId, reason }: { feedbackId: string; reason: string }) =>
      feedbackApi.reportFeedback(feedbackId, reason),
    onSuccess: () => {
      // Invalidate feedback lists to refresh reported status
      queryClient.invalidateQueries({ queryKey: feedbackKeys.lists() })
    },
    onError: (error) => {
      console.error('Failed to report feedback:', error)
    },
  })
} 