import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { paymentsApi, type Payment, type PaymentIntent, type RefundRequest, type TransactionHistory, type Refund } from '../api/paymentsApi'

// Query Keys
export const paymentKeys = {
  all: ['payments'] as const,
  transactions: () => [...paymentKeys.all, 'transactions'] as const,
  transaction: (transactionId: string) => [...paymentKeys.all, 'transaction', transactionId] as const,
  paymentIntents: () => [...paymentKeys.all, 'payment-intents'] as const,
  paymentIntent: (intentId: string) => [...paymentKeys.all, 'payment-intent', intentId] as const,
  refunds: () => [...paymentKeys.all, 'refunds'] as const,
  refund: (refundId: string) => [...paymentKeys.all, 'refund', refundId] as const,
}

// Get transaction history
export const useTransactionHistory = (params?: { limit?: number; offset?: number }) => {
  return useQuery({
    queryKey: [...paymentKeys.transactions(), params],
    queryFn: () => paymentsApi.getTransactionHistory(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Get single transaction
export const useTransaction = (transactionId: string) => {
  return useQuery({
    queryKey: paymentKeys.transaction(transactionId),
    queryFn: () => paymentsApi.getTransaction(transactionId),
    enabled: !!transactionId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get payment intents
export const usePaymentIntents = () => {
  return useQuery({
    queryKey: paymentKeys.paymentIntents(),
    queryFn: () => paymentsApi.getPaymentIntents(),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

// Get single payment intent
export const usePaymentIntent = (intentId: string) => {
  return useQuery({
    queryKey: paymentKeys.paymentIntent(intentId),
    queryFn: () => paymentsApi.getPaymentIntent(intentId),
    enabled: !!intentId,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

// Get refunds
export const useRefunds = () => {
  return useQuery({
    queryKey: paymentKeys.refunds(),
    queryFn: () => paymentsApi.getRefunds(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get single refund
export const useRefund = (refundId: string) => {
  return useQuery({
    queryKey: paymentKeys.refund(refundId),
    queryFn: () => paymentsApi.getRefund(refundId),
    enabled: !!refundId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Create payment intent
export const useCreatePaymentIntent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (paymentData: { currency: string; bookingId: string }) =>
      paymentsApi.createPaymentIntent(paymentData),
    onSuccess: (paymentIntent) => {
      // Add to payment intents cache
      queryClient.setQueryData(
        paymentKeys.paymentIntents(),
        (oldData: PaymentIntent[] | undefined) => {
          if (!oldData) return [paymentIntent]
          return [paymentIntent, ...oldData]
        }
      )
      
      // Add individual payment intent to cache
      queryClient.setQueryData(paymentKeys.paymentIntent(paymentIntent.paymentId), paymentIntent)
    },
    onError: (error) => {
      console.error('Failed to create payment intent:', error)
    },
  })
}

// Confirm payment
export const useConfirmPayment = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ intentId, paymentMethodId }: { intentId: string; paymentMethodId: string }) =>
      paymentsApi.confirmPayment(intentId, paymentMethodId),
    onSuccess: (payment, variables) => {
      // Update payment intent status
      queryClient.setQueryData(paymentKeys.paymentIntent(variables.intentId), (oldData: PaymentIntent | undefined) => {
        if (!oldData) return oldData
        return { ...oldData, status: 'succeeded' }
      })
      
      // Invalidate transaction history
      queryClient.invalidateQueries({ queryKey: paymentKeys.transactions() })
    },
    onError: (error) => {
      console.error('Failed to confirm payment:', error)
    },
  })
}

// Request refund
export const useRequestRefund = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (refundData: RefundRequest) => paymentsApi.requestRefund(refundData),
    onSuccess: (refund) => {
      // Add to refunds cache
      queryClient.setQueryData(
        paymentKeys.refunds(),
        (oldData: any[] | undefined) => {
          if (!oldData) return [refund]
          return [refund, ...oldData]
        }
      )
      
      // Add individual refund to cache
      queryClient.setQueryData(paymentKeys.refund(refund.id), refund)
      
      // Invalidate transaction history
      queryClient.invalidateQueries({ queryKey: paymentKeys.transactions() })
    },
    onError: (error) => {
      console.error('Failed to request refund:', error)
    },
  })
}

// Cancel payment intent
export const useCancelPaymentIntent = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (intentId: string) => paymentsApi.cancelPaymentIntent(intentId),
    onSuccess: (_, intentId) => {
      // Update payment intent status
      queryClient.setQueryData(paymentKeys.paymentIntent(intentId), (oldData: PaymentIntent | undefined) => {
        if (!oldData) return oldData
        return { ...oldData, status: 'canceled' }
      })
      
      // Invalidate payment intents list
      queryClient.invalidateQueries({ queryKey: paymentKeys.paymentIntents() })
    },
    onError: (error) => {
      console.error('Failed to cancel payment intent:', error)
    },
  })
}

// Update payment method
export const useUpdatePaymentMethod = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ paymentMethodId, data }: { paymentMethodId: string; data: any }) =>
      paymentsApi.updatePaymentMethod(paymentMethodId, data),
    onSuccess: () => {
      // Invalidate payment intents that might use this payment method
      queryClient.invalidateQueries({ queryKey: paymentKeys.paymentIntents() })
    },
    onError: (error) => {
      console.error('Failed to update payment method:', error)
    },
  })
}

// Delete payment method
export const useDeletePaymentMethod = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (paymentMethodId: string) => paymentsApi.deletePaymentMethod(paymentMethodId),
    onSuccess: () => {
      // Invalidate payment intents that might use this payment method
      queryClient.invalidateQueries({ queryKey: paymentKeys.paymentIntents() })
    },
    onError: (error) => {
      console.error('Failed to delete payment method:', error)
    },
  })
} 