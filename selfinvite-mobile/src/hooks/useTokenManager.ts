import { useCallback } from 'react'
import { useAuthStore } from '../stores/authStore'
import { TokenManager } from '../utils/tokenManager'
import supabase from '../../lib/supabase'

export const useTokenManager = () => {
  const { session, setSession } = useAuthStore()

  const refreshToken = useCallback(async () => {
    try {
      const newSession = await TokenManager.refreshAndSaveSession(supabase)
      if (newSession) {
        await setSession(newSession)
        return newSession
      }
      return null
    } catch (error) {
      console.error('Error refreshing token:', error)
      return null
    }
  }, [setSession])

  const getAccessToken = useCallback(async () => {
    // First try to get from current session
    if (session?.access_token) {
      return session.access_token
    }
    
    // If no current session, try to get from storage
    return await TokenManager.getAccessToken()
  }, [session])

  const isTokenExpired = useCallback(() => {
    return TokenManager.isTokenExpired(session)
  }, [session])

  const clearTokens = useCallback(async () => {
    await TokenManager.clearTokens()
  }, [])

  return {
    session,
    refreshToken,
    getAccessToken,
    isTokenExpired,
    clearTokens,
  }
} 