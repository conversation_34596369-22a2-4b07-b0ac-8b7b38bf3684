import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { eventBookingsApi } from '../api/eventBookingsApi'
import type { EventBooking, CreateEventBookingRequest, RespondEventBookingRequest, ChangeEventBookingRequest } from '../types'
import logger from '../common/logger'

// Query Keys
export const eventBookingKeys = {
  all: ['event-bookings'] as const,
  lists: () => [...eventBookingKeys.all, 'list'] as const,
  list: (filters?: any) => [...eventBookingKeys.lists(), filters] as const,
  details: () => [...eventBookingKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventBookingKeys.details(), id] as const,
  myBookings: () => [...eventBookingKeys.all, 'my-bookings'] as const,
  receivedBookings: () => [...eventBookingKeys.all, 'received-bookings'] as const,
  eventBookings: (eventId: string) => [...eventBookingKeys.all, 'event', eventId] as const,
  pendingBookings: () => [...eventBookingKeys.all, 'pending'] as const,
}

// Get all event bookings with filters
export const useEventBookings = (filters?: any) => {
  return useQuery({
    queryKey: eventBookingKeys.list(filters),
    queryFn: () => eventBookingsApi.getEventBookings(filters),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

// Get single event booking by ID
export const useEventBookingDetail = (bookingId: string) => {
  return useQuery({
    queryKey: eventBookingKeys.detail(bookingId),
    queryFn: () => eventBookingsApi.getEventBooking(bookingId),
    enabled: !!bookingId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get user's sent bookings (outgoing)
export const useOutgoingEventBookings = () => {
  return useQuery({
    queryKey: eventBookingKeys.myBookings(),
    queryFn: () => eventBookingsApi.getOutgoingBookings(),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

// Get user's received bookings (incoming)
export const useIncomingEventBookings = () => {
  return useQuery({
    queryKey: eventBookingKeys.receivedBookings(),
    queryFn: () => eventBookingsApi.getIncomingBookings(),
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

// Get bookings for a specific event
export const useEventBookingsForEvent = (eventId: string) => {
  return useQuery({
    queryKey: eventBookingKeys.eventBookings(eventId),
    queryFn: () => eventBookingsApi.getEventBookings(eventId),
    enabled: !!eventId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

// Get pending bookings (for hosts)
export const usePendingEventBookings = () => {
  return useQuery({
    queryKey: eventBookingKeys.pendingBookings(),
    queryFn: () => eventBookingsApi.getPendingBookingsCount(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  })
}

// Request participation to an event
export const useRequestParticipationEventBooking = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (bookingData: CreateEventBookingRequest) => eventBookingsApi.requestParticipationEventBooking(bookingData),
    onSuccess: (newBooking) => {
      // Invalidate and refetch booking lists
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.myBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.receivedBookings() })
      
      // Add the new booking to the cache
      queryClient.setQueryData(eventBookingKeys.detail(newBooking.id), newBooking)
    },
    onError: (error) => {
      throw error
    },
  })
}

// Cancel participation to an event (for guests to cancel their own request)
export const useCancelParticipationEventBooking = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (bookingData: ChangeEventBookingRequest) => eventBookingsApi.cancelParticipationEventBooking(bookingData),
    onSuccess: (updatedBooking) => {
      // Invalidate and refetch booking lists
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.myBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.receivedBookings() })
      
      // Update the booking in the cache
      if (updatedBooking?.id) {
        queryClient.setQueryData(eventBookingKeys.detail(updatedBooking.id), updatedBooking)
      }
    },
    onError: (error) => {
      logger.error('Failed to cancel participation booking:', error)
      throw error
    },
  })
}

// Accept participation to an event (for hosts to accept guest requests)
export const useAcceptParticipationEventBooking = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (bookingData: ChangeEventBookingRequest) => eventBookingsApi.acceptParticipationEventBooking(bookingData),
    onSuccess: (updatedBooking) => {
      // Invalidate and refetch booking lists
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.myBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.receivedBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.pendingBookings() })
      
      // Update the booking in the cache
      if (updatedBooking?.id) {
        queryClient.setQueryData(eventBookingKeys.detail(updatedBooking.id), updatedBooking)
      }
    },
    onError: (error) => {
      logger.error('Failed to accept participation booking:', error)
      throw error
    },
  })
}

// Reject participation to an event (for hosts to reject guest requests)
export const useRejectParticipationEventBooking = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (bookingData: ChangeEventBookingRequest) => eventBookingsApi.rejectParticipationEventBooking(bookingData),
    onSuccess: (updatedBooking) => {
      // Invalidate and refetch booking lists
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.myBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.receivedBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.pendingBookings() })
      
      // Update the booking in the cache
      if (updatedBooking?.id) {
        queryClient.setQueryData(eventBookingKeys.detail(updatedBooking.id), updatedBooking)
      }
    },
    onError: (error) => {
      logger.error('Failed to reject participation booking:', error)
      throw error
    },
  })
}

// Respond to event booking (accept/reject)
export const useRespondToEventBooking = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ bookingId, response }: { bookingId: string; response: RespondEventBookingRequest }) =>
      eventBookingsApi.respondToEventBooking(bookingId, response),
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.detail(variables.bookingId) })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.receivedBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.pendingBookings() })
    },
    onError: (error) => {
      throw error
    },
  })
}

// Cancel event booking
export const useCancelEventBooking = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (bookingId: string) => eventBookingsApi.cancelEventBooking(bookingId),
    onSuccess: (_, bookingId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: eventBookingKeys.detail(bookingId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.lists() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.myBookings() })
      queryClient.invalidateQueries({ queryKey: eventBookingKeys.receivedBookings() })
    },
    onError: (error) => {
      throw error
    },
  })
} 