import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  Image,
  Dimensions,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, useTheme } from 'react-native-paper';
import { useRouter } from 'expo-router';
import PagerView from 'react-native-pager-view';
import { images } from '../types/images';
import { useI18n } from '../hooks';
import { useOnboardingStore } from '../stores/onboardingStore';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface Slide {
  key: string;
  title: string;
  text: string;
  image: any;
  backgroundColor: string;
}

export default function OnboardingScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { t, currentLocale } = useI18n();
  const { completeOnboarding } = useOnboardingStore();
  const [currentPage, setCurrentPage] = useState(0);
  const pagerRef = useRef<PagerView>(null);

  const slides: Slide[] = [
    {
      key: 'k1',
      title: t('__slide_1_title'),
      text: t('__slide_1_text'),
      image: images._people_slide,
      backgroundColor: '#ef754b',
    },
    {
      key: 'k2',
      title: t('__slide_2_title'),
      text: t('__slide_2_text'),
      image: images._map_slide,
      backgroundColor: '#27aae1',
    },
    {
      key: 'k3',
      title: t('__slide_3_title'),
      text: t('__slide_3_text'),
      image: images._food_slide,
      backgroundColor: '#a8cf5a',
    },
    {
      key: 'k4',
      title: t('__slide_4_title'),
      text: t('__slide_4_text'),
      image: images._table_slide,
      backgroundColor: theme.colors.primary,
    },
  ];

  const handleNext = () => {
    if (currentPage < slides.length - 1) {
      pagerRef.current?.setPage(currentPage + 1);
    } else {
      handleComplete();
    }
  };

  const handleSkip = () => {
    handleComplete();
  };

  const handleComplete = async () => {
    await completeOnboarding();
    router.replace('/(tabs)');
  };

  const handlePageSelected = (e: any) => {
    setCurrentPage(e.nativeEvent.position);
  };

  const renderSlide = (slide: Slide, index: number) => (
    <View key={slide.key} style={[styles.slide, { backgroundColor: slide.backgroundColor }]}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={slide.backgroundColor}
        translucent={Platform.OS === 'android'}
      />
      <SafeAreaView style={styles.slideContent} edges={['top', 'bottom']}>
        <View style={styles.imageContainer}>
          <Image source={slide.image} style={styles.image} resizeMode="contain" />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={styles.title}>{slide.title}</Text>
          <Text style={styles.text}>{slide.text}</Text>
        </View>

        <View style={styles.bottomContainer}>
          {/* Pagination dots */}
          <View style={styles.pagination}>
            {slides.map((_, i) => (
              <View
                key={i}
                style={[
                  styles.dot,
                  {
                    backgroundColor: i === index ? '#fff' : 'rgba(255, 255, 255, 0.3)',
                  },
                ]}
              />
            ))}
          </View>

          {/* Action buttons */}
          <View style={styles.buttonContainer}>
            {index < slides.length - 1 ? (
              <>
                <Button
                  mode="text"
                  onPress={handleSkip}
                  textColor="#fff"
                  style={styles.skipButton}
                >
                  Skip
                </Button>
                <Button
                  mode="contained"
                  onPress={handleNext}
                  buttonColor="rgba(255, 255, 255, 0.2)"
                  textColor="#fff"
                  style={styles.nextButton}
                >
                  Next
                </Button>
              </>
            ) : (
              <Button
                mode="contained"
                onPress={handleComplete}
                buttonColor="rgba(255, 255, 255, 0.2)"
                textColor="#fff"
                style={styles.getStartedButton}
              >
                Get Started
              </Button>
            )}
          </View>
        </View>
      </SafeAreaView>
    </View>
  );

  return (
    <View style={styles.container}>
      <PagerView
        ref={pagerRef}
        style={styles.pagerView}
        initialPage={0}
        onPageSelected={handlePageSelected}
      >
        {slides.map((slide, index) => renderSlide(slide, index))}
      </PagerView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  pagerView: {
    flex: 1,
  },
  slide: {
    flex: 1,
    width: screenWidth,
  },
  slideContent: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 40,
  },
  image: {
    width: Math.min(screenWidth * 0.8, 360),
    height: Math.min(screenWidth * 0.8, 360),
  },
  textContainer: {
    flex: 0.6,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 16,
  },
  text: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    lineHeight: 24,
    opacity: 0.9,
  },
  bottomContainer: {
    paddingBottom: 20,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  skipButton: {
    flex: 1,
  },
  nextButton: {
    flex: 1,
    marginLeft: 16,
  },
  getStartedButton: {
    flex: 1,
  },
});
