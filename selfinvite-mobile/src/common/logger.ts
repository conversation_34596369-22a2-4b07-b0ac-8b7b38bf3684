import { logger, consoleTransport, configLoggerType } from 'react-native-logs';

const defaultConfig = {
  severity: __DEV__ ? 'debug' : 'warning',
  transport: [consoleTransport],
  transportOptions: {
    colors: {
      info: 'blueBright',
      warn: 'yellowBright', 
      error: 'redBright',
    } as const,
  },
  async: true,
  dateFormat: 'time',
  printLevel: true,
  printDate: true,
  enabled: true,
};

const log = logger.createLogger(defaultConfig);

export default log;

export const useLogger = () => {
  const logInfo = (message: string, extra = {}) => {
    log.info(message, extra);
  };

  const logError = (message: string, error = null) => {
    log.error(message, error);
  };

  const logDebug = (message: string, data = {}) => {
    log.debug(message, data);
  };

  const logWarn = (message: string, data = {}) => {
    log.warn(message, data);
  };

  return { logInfo, logError, logDebug, logWarn };
};
