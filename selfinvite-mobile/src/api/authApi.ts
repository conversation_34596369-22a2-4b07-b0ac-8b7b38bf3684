import log from '@/common/logger';
import { Session, User } from '@supabase/supabase-js';

const API_URL =
  process.env.EXPO_PUBLIC_API_URL ||
  'https://reliable-respect-staging.up.railway.app/api/v1';

interface SignInResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  expires_at: number;
}

async function signInWithEmail(
  email: string,
  password: string
): Promise<{ data: SignInResponse | null; error: { message: string } | null }> {
  try {
    const body = new URLSearchParams();
    body.append('email', email);
    body.append('password', password);

    const response = await fetch(`${API_URL}/auth/sign-in`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: body.toString(),
    });

    const responseData = await response.json();

    if (!response.ok) {
      log.error('Sign-in API error:', responseData);
      return { data: null, error: { message: responseData.message || 'Authentication failed' } };
    }

    return { data: responseData, error: null };
  } catch (error: any) {
    log.error('Network error during sign-in:', error);
    return { data: null, error: { message: error.message || 'A network error occurred.' } };
  }
}

export const authApi = {
  signInWithEmail,
};