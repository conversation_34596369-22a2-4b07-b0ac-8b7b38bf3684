import { algoliaSearchService } from '../services/algoliaService'
import type {
  SearchFilters,
  SearchResults,
  SearchSortOptions,
  FacetCounts,
  SearchSuggestion
} from '../types/search'
import type { Event } from '../types/event'
import log from '../common/logger'

export const searchApi = {
  // Main search function with comprehensive filtering
  async searchEvents(
    query: string = '',
    filters: SearchFilters = {},
    options: {
      page?: number
      limit?: number
      sort?: SearchSortOptions[]
      facets?: string[]
      attributesToRetrieve?: string[]
    } = {}
  ): Promise<SearchResults> {
    try {
      log.info('Search API request - using Algolia:', { query, filters, options })
      
      // Use Algolia search service directly
      const searchOptions: {
        page?: number;
        limit?: number;
        sort?: SearchSortOptions[];
        facets?: string[];
      } = {};
      
      if (options.page !== undefined) searchOptions.page = options.page;
      if (options.limit !== undefined) searchOptions.limit = options.limit;
      if (options.sort !== undefined) searchOptions.sort = options.sort;
      if (options.facets !== undefined) searchOptions.facets = options.facets;
      
      const results = await algoliaSearchService.searchEvents(query, filters, searchOptions)
      
      log.debug('Algolia Search API Response:', results)
      
      return results
    } catch (error) {
      log.error('Search API Error:', error)
      throw new Error('Failed to search events')
    }
  },

  // Get search suggestions/autocomplete
  async getSearchSuggestions(
    query: string,
    limit: number = 5
  ): Promise<SearchSuggestion[]> {
    try {
      if (!query || query.length < 2) return []

      const suggestions = await algoliaSearchService.getSearchSuggestions(query, limit)
      return suggestions
    } catch (error) {
      log.error('Search suggestions error:', error)
      return []
    }
  },

  // Get events near a location
  async searchNearby(
    lat: number,
    lng: number,
    radius: number = 10, // km
    filters: Omit<SearchFilters, 'coordinates' | 'radius'> = {},
    limit: number = 20
  ): Promise<SearchResults> {
    try {
      log.info('Nearby search API request - using Algolia:', { lat, lng, radius, filters, limit })
      
      const results = await algoliaSearchService.searchNearby(lat, lng, radius, filters, limit)
      
      return results
    } catch (error) {
      log.error('Nearby search API Error:', error)
      throw new Error('Failed to search nearby events')
    }
  },

  // Get popular searches or trending
  async getTrendingSearches(): Promise<string[]> {
    try {
      // For now, return static popular searches since Algolia doesn't provide trending
      // In the future, this could be enhanced with analytics data
      return [
        'Italian dinner',
        'Vegan brunch',
        'Cooking class',
        'Wine tasting',
        'BBQ party',
        'Sushi making',
        'French cuisine',
        'Cocktail workshop'
      ]
    } catch (error) {
      log.error('Trending searches error:', error)
      // Fallback to static popular searches
      return [
        'Italian dinner',
        'Vegan brunch',
        'Cooking class',
        'Wine tasting',
        'BBQ party',
        'Sushi making',
        'French cuisine',
        'Cocktail workshop'
      ]
    }
  },

  // Get facet values for filters
  async getFacetValues(facetName: string): Promise<string[]> {
    try {
      // For now, return empty array since facet values are provided in search results
      // In the future, this could be enhanced to return available facet values
      return []
    } catch (error) {
      log.error('Facet values error:', error)
      return []
    }
  },

  // Health check for search service
  async healthCheck(): Promise<boolean> {
    try {
      return await algoliaSearchService.healthCheck()
    } catch (error) {
      log.error('Search health check failed:', error)
      return false
    }
  }
}
