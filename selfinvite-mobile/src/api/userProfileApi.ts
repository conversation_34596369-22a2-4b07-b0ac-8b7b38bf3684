import { apiClient } from '../utils/apiClient'
import type {
  UserProfile,
  UserPreferences,
  UpdateProfileRequest,
  UpdatePreferencesRequest,
  ApiResponse
} from '../types'

export const userProfileApi = {
  // Get current user profile - unified endpoint that returns all profile data
  async getMyProfile(): Promise<UserProfile> {
    return apiClient.get<UserProfile>('/user/profile')
  },

  // Get user profile by ID
  async getUserProfile(userId: string): Promise<UserProfile> {
    return apiClient.get<UserProfile>(`/user/profile/${userId}`)
  },

  // Update current user profile - unified endpoint that accepts all modifiable profile data
  async updateMyProfile(profileData: UpdateProfileRequest): Promise<UserProfile> {
    return apiClient.put<UserProfile>('/user/profile', profileData)
  },

  // Get user badges
  async getMyBadges(): Promise<string[]> {
    return apiClient.get<string[]>('/user/badges')
  },

  // Get user statistics
  async getMyStats(): Promise<{
    eventsHosted: number
    eventsJoined: number
    averageRating: number
    totalFeedbacks: number
  }> {
    return apiClient.get('/user/stats')
  },

  // Search users
  async searchUsers(query: string, page: number = 1, limit: number = 20): Promise<{
    users: UserProfile[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    return apiClient.get(`/users/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`)
  },

  // Get users by location
  async getUsersByLocation(
    lat: number, 
    lng: number, 
    radius: number = 10
  ): Promise<UserProfile[]> {
    return apiClient.get<UserProfile[]>(
      `/users/nearby?lat=${lat}&lng=${lng}&radius=${radius}`
    )
  },

  // Block user
  async blockUser(userId: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>(`/user/block/${userId}`, {})
  },

  // Unblock user
  async unblockUser(userId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/user/block/${userId}`)
  },

  // Get blocked users
  async getBlockedUsers(): Promise<UserProfile[]> {
    return apiClient.get<UserProfile[]>('/user/blocked')
  },

  // Report user
  async reportUser(userId: string, reason: string, description?: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>(`/user/report/${userId}`, { reason, description })
  },

  // Delete account
  async deleteAccount(): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>('/user/account')
  },

  // Get user preferences
  async getMyPreferences(): Promise<UserPreferences> {
    return apiClient.get<UserPreferences>('/user/preferences')
  },

  // Update user preferences
  async updateMyPreferences(preferencesData: UpdatePreferencesRequest): Promise<UserPreferences> {
    return apiClient.put<UserPreferences>('/user/preferences', preferencesData)
  }
} 