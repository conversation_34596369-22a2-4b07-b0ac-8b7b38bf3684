import { apiClient } from '../utils/apiClient'
import type {
  Event,
  CreateEventRequest,
  UpdateEventRequest,
  EventSearchParams,
  PaginatedResponse,
  ApiResponse,
  MediaProperty
} from '../types'
import type { UploadResult } from '../utils/mediaUploadService'

export const eventsApi = {
  // Get all events with search/filter
  async getEvents(params?: EventSearchParams): Promise<PaginatedResponse<Event>> {
    const queryParams = new URLSearchParams()
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            queryParams.append(key, value.join(','))
          } else if (typeof value === 'object') {
            queryParams.append(key, JSON.stringify(value))
          } else {
            queryParams.append(key, value.toString())
          }
        }
      })
    }

    const query = queryParams.toString()
    return apiClient.get<PaginatedResponse<Event>>(
      `/events${query ? `?${query}` : ''}`
    )
  },

  // Get single event by ID
  async getEvent(eventId: string): Promise<Event> {
    return apiClient.get<Event>(`/events/${eventId}`)
  },

  // Create new event
  async createEvent(eventData: CreateEventRequest): Promise<Event> {
    return apiClient.post<Event>('/events', eventData)
  },

  // Update event
  async updateEvent(eventId: string, eventData: UpdateEventRequest): Promise<Event> {
    return apiClient.put<Event>(`/events/${eventId}`, eventData)
  },

  // Delete event
  async deleteEvent(eventId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/events/${eventId}`)
  },

  // Get user's hosted events
  async getMyHostedEvents(): Promise<Event[]> {
    return apiClient.get<Event[]>('/events/my-hosted')
  },

  // Get user's joined events
  async getMyJoinedEvents(): Promise<Event[]> {
    return apiClient.get<Event[]>('/events/my-joined')
  },

  // Get events by user ID
  async getEventsByUser(userId?: string): Promise<Event[]> {
    const endpoint = userId ? `/events/get-by-user?user_id=${userId}` : '/events/get-by-user'
    return apiClient.get<Event[]>(endpoint)
  },

  // Get events by location
  async getEventsByLocation(
    lat: number, 
    lng: number, 
    radius: number = 10
  ): Promise<Event[]> {
    return apiClient.get<Event[]>(
      `/events/nearby?lat=${lat}&lng=${lng}&radius=${radius}`
    )
  },

  // Join event (for free events)
  async joinEvent(eventId: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>(`/events/${eventId}/join`, {})
  },

  // Leave event
  async leaveEvent(eventId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/events/${eventId}/leave`)
  },

  // Upload event media files
  async uploadEventMedia(eventId: string, mediaFiles: File[]): Promise<MediaProperty[]> {
    const formData = new FormData()
    
    mediaFiles.forEach((file, index) => {
      formData.append(`media_${index}`, file)
    })

    return apiClient.request<MediaProperty[]>(`/events/${eventId}/media`, {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set content-type for FormData
    })
  },

  // Upload event images (legacy support)
  async uploadEventImages(eventId: string, images: FormData): Promise<string[]> {
    return apiClient.request<string[]>(`/events/${eventId}/images`, {
      method: 'POST',
      body: images,
      headers: {} // Let browser set content-type for FormData
    })
  },

  // Get event participants
  async getEventParticipants(eventId: string): Promise<any[]> {
    return apiClient.get<any[]>(`/events/${eventId}/participants`)
  },

  // Cancel event
  async cancelEvent(eventId: string, reason?: string): Promise<ApiResponse> {
    return apiClient.patch<ApiResponse>(`/events/${eventId}/cancel`, { reason })
  },

  // Publish draft event
  async publishEvent(eventId: string): Promise<Event> {
    return apiClient.patch<Event>(`/events/${eventId}/publish`, {})
  }
} 