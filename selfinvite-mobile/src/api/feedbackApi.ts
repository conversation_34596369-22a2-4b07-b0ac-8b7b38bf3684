import { apiClient } from '../utils/apiClient'
import type { Feedback, CreateFeedbackRequest, ApiResponse, PaginatedResponse } from '../types'

export interface UpdateFeedbackRequest {
  rating?: number
  comment?: string
  isPublic?: boolean
}

export const feedbackApi = {
  // Get all feedback with filters
  async getFeedback(filters?: any): Promise<PaginatedResponse<Feedback>> {
    const queryParams = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }
    
    const query = queryParams.toString()
    return apiClient.get<PaginatedResponse<Feedback>>(`/feedback${query ? `?${query}` : ''}`)
  },

  // Get single feedback by ID
  async getFeedbackDetail(feedbackId: string): Promise<Feedback> {
    return apiClient.get<Feedback>(`/feedback/${feedbackId}`)
  },

  // Get user's sent feedback
  async getMyFeedback(): Promise<Feedback[]> {
    return apiClient.get<Feedback[]>('/feedback/my-feedback')
  },

  // Get user's received feedback
  async getReceivedFeedback(): Promise<Feedback[]> {
    return apiClient.get<Feedback[]>('/feedback/received-feedback')
  },

  // Get feedback for a specific event
  async getEventFeedback(eventId: string): Promise<Feedback[]> {
    return apiClient.get<Feedback[]>(`/feedback/event/${eventId}`)
  },

  // Create new feedback
  async createFeedback(feedbackData: CreateFeedbackRequest): Promise<Feedback> {
    return apiClient.post<Feedback>('/feedback', feedbackData)
  },

  // Update feedback
  async updateFeedback(feedbackId: string, feedbackData: UpdateFeedbackRequest): Promise<Feedback> {
    return apiClient.put<Feedback>(`/feedback/${feedbackId}`, feedbackData)
  },

  // Delete feedback
  async deleteFeedback(feedbackId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/feedback/${feedbackId}`)
  },

  // Report feedback
  async reportFeedback(feedbackId: string, reason: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>(`/feedback/${feedbackId}/report`, { reason })
  },
} 