import { apiClient } from '../utils/apiClient';
import type {
  NotificationToken,
  NotificationPreferences,
  UpdatePreferencesRequest,
  SendNotificationRequest,
  NotificationHistory,
  NotificationCampaign,
} from '../types/notifications';

export const notificationsApi = {
  // Register notification token
  async registerToken(tokenData: NotificationToken): Promise<void> {
    return apiClient.post('/notifications/register-token', tokenData);
  },

  // Get notification preferences
  async getPreferences(): Promise<NotificationPreferences> {
    const response = await apiClient.get<NotificationPreferences>('/profile/preferences');
    return response;
  },

  // Update notification preferences
  async updatePreferences(preferences: UpdatePreferencesRequest): Promise<void> {
    return apiClient.patch('/profile/preferences', preferences);
  },

  // Send notification to user
  async sendNotification(notification: SendNotificationRequest): Promise<void> {
    return apiClient.post('/notifications/send', notification);
  },

  // Get notification history
  async getNotificationHistory(page: number = 1, limit: number = 20): Promise<{
    data: NotificationHistory[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const response = await apiClient.get<{
      notifications: NotificationHistory[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(`/notifications/history?page=${page}&limit=${limit}`);

    return {
      data: response.notifications,
      pagination: response.pagination,
    };
  },

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    return apiClient.patch(`/notifications/${notificationId}/read`, {});
  },

  // Mark multiple notifications as read
  async markMultipleAsRead(notificationIds: string[]): Promise<void> {
    return apiClient.patch('/notifications/mark-read', { notificationIds });
  },

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    return apiClient.delete(`/notifications/${notificationId}`);
  },

  // Clear all notifications
  async clearAllNotifications(): Promise<void> {
    return apiClient.delete('/notifications/clear-all');
  },

  // Get notification campaigns (for admin/marketing)
  async getCampaigns(): Promise<NotificationCampaign[]> {
    return apiClient.get<NotificationCampaign[]>('/notifications/campaigns');
  },

  // Create notification campaign
  async createCampaign(campaign: Omit<NotificationCampaign, 'id' | 'createdAt' | 'updatedAt' | 'analytics'>): Promise<NotificationCampaign> {
    return apiClient.post<NotificationCampaign>('/notifications/campaigns', campaign);
  },

  // Update notification campaign
  async updateCampaign(campaignId: string, updates: Partial<NotificationCampaign>): Promise<NotificationCampaign> {
    return apiClient.patch<NotificationCampaign>(`/notifications/campaigns/${campaignId}`, updates);
  },

  // Delete notification campaign
  async deleteCampaign(campaignId: string): Promise<void> {
    return apiClient.delete(`/notifications/campaigns/${campaignId}`);
  },

  // Send campaign
  async sendCampaign(campaignId: string): Promise<void> {
    return apiClient.post(`/notifications/campaigns/${campaignId}/send`, {});
  },

  // Get notification analytics
  async getAnalytics(campaignId?: string): Promise<{
    totalSent: number;
    totalDelivered: number;
    totalOpened: number;
    totalClicked: number;
    deliveryRate: number;
    openRate: number;
    clickRate: number;
    conversionRate: number;
  }> {
    const url = campaignId 
      ? `/notifications/analytics?campaignId=${campaignId}`
      : '/notifications/analytics';
    
    return apiClient.get(url);
  },

  // Test notification (for development)
  async sendTestNotification(userId: string, title: string, body: string): Promise<void> {
    return apiClient.post('/notifications/test', {
      userId,
      title,
      body,
      data: {
        type: 'marketing',
        priority: 'normal',
        category: 'TEST',
        data: { test: true },
      },
    });
  },
};
