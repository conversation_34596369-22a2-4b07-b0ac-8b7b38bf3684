import { apiClient } from '../utils/apiClient'
import type {
  EventBooking,
  CreateEventBookingRequest,
  RespondEventBookingRequest,
  ApiResponse,
  PaginatedResponse,
  AugmentedEventBooking,
  EventBookingResponse,
  ChangeEventBookingRequest
} from '../types'

export const eventBookingsApi = {
  // Create a booking to join an event
  async requestParticipationEventBooking(bookingData: CreateEventBookingRequest): Promise<EventBooking> {
    return apiClient.post<EventBooking>('/bookings/request-participation', bookingData)
  },

  async cancelParticipationEventBooking(bookingData: ChangeEventBookingRequest): Promise<EventBookingResponse> {
    return apiClient.post<EventBookingResponse>('/bookings/cancel-participation', bookingData)
  },

  async acceptParticipationEventBooking(bookingData: ChangeEventBookingRequest): Promise<EventBookingResponse> {
    return apiClient.post<EventBookingResponse>('/bookings/accept-participation', bookingData)
  },

  async rejectParticipationEventBooking(bookingData: ChangeEventBookingRequest): Promise<EventBookingResponse> {
    return apiClient.post<EventBookingResponse>('/bookings/reject-participation', bookingData)
  },

  // Get incoming bookings for events I host
  async getIncomingBookings(page: number = 1, limit: number = 20): Promise<PaginatedResponse<AugmentedEventBooking>> {
    const response = await apiClient.get<{event_bookings: AugmentedEventBooking[]}>(
      `/bookings/incoming?page=${page}&limit=${limit}`
    )
    
    // Transform the response to match PaginatedResponse structure
    return {
      data: response.event_bookings,
      pagination: {
        page,
        limit,
        total: response.event_bookings.length,
        totalPages: 1
      }
    }
  },

  // Get outgoing bookings I've made
  async getOutgoingBookings(page: number = 1, limit: number = 20): Promise<PaginatedResponse<AugmentedEventBooking>> {
    const response = await apiClient.get<{event_bookings: AugmentedEventBooking[]}>(
      `/bookings/outgoing?page=${page}&limit=${limit}`
    )
    
    // Transform the response to match PaginatedResponse structure
    return {
      data: response.event_bookings,
      pagination: {
        page,
        limit,
        total: response.event_bookings.length,
        totalPages: 1
      }
    }
  },

  // Get bookings for a specific event (for hosts)
  async getEventBookings(eventId: string): Promise<EventBooking[]> {
    return apiClient.get<EventBooking[]>(`/events/${eventId}/bookings`)
  },
  // Get bookings for a specific user (for hosts)
  async getEventBookingsByUser(userId: string): Promise<EventBooking[]> {
    return apiClient.get<EventBooking[]>(`/bookings/get-by-user/${userId}`)
  },

  // Get single booking by ID
  async getEventBooking(bookingId: string): Promise<EventBooking> {
    return apiClient.get<EventBooking>(`/bookings/${bookingId}`)
  },

  // Respond to an event booking (accept/reject)
  async respondToEventBooking(
    bookingId: string, 
    response: RespondEventBookingRequest
  ): Promise<EventBookingResponse> {
    return apiClient.patch<EventBookingResponse>(`/bookings/${bookingId}/respond`, response)
  },

  // Cancel my own booking
  async cancelEventBooking(bookingId: string): Promise<EventBookingResponse> {
    return apiClient.delete<EventBookingResponse>(`/bookings/${bookingId}`)
  },

  // Bulk respond to multiple bookings
  async bulkRespondToBookings(responses: {
    bookingId: string
    status: 'accepted' | 'rejected'
    responseMessage?: string
  }[]): Promise<EventBookingResponse> {
    return apiClient.post<EventBookingResponse>('/bookings/bulk-respond', { responses })
  },

  // Get booking statistics for an event
  async getEventBookingStats(eventId: string): Promise<{
    total: number
    pending_confirmation: number
    confirmed: number
    accepted: number
    rejected: number
  }> {
    return apiClient.get(`/events/${eventId}/booking-stats`)
  },

  // Check if user has pending booking for event
  async checkPendingBooking(eventId: string): Promise<{
    hasPendingBooking: boolean
    booking?: EventBooking
  }> {
    return apiClient.get(`/events/${eventId}/my-booking-status`)
  },

  // Get all pending bookings count
  async getPendingBookingsCount(): Promise<{ count: number }> {
    return apiClient.get('/bookings/pending-count')
  },

  // Mark booking notifications as read
  async markBookingNotificationsAsRead(bookingIds: string[]): Promise<EventBookingResponse> {
    return apiClient.patch<EventBookingResponse>('/bookings/mark-read', { bookingIds })
  },

  // Get booking history for an event
  async getEventBookingHistory(eventId: string): Promise<EventBookingResponse[]> {
    return apiClient.get<EventBookingResponse[]>(`/events/${eventId}/booking-history`)
  },

  // Send reminder to pending bookers
  async sendReminderToPendingBookers(eventId: string, message?: string): Promise<EventBookingResponse> {
    return apiClient.post<EventBookingResponse>(`/events/${eventId}/send-reminder`, { message })
  }
} 