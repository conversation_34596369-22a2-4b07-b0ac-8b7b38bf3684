import { apiClient } from '../utils/apiClient'
import type { Review, CreateReviewRequest, ApiResponse, PaginatedResponse } from '../types'

export interface UpdateReviewRequest {
  rating?: number
  comment?: string
  isPublic?: boolean
}

export const reviewsApi = {
  // Get all reviews with filters
  async getReviews(filters?: any): Promise<PaginatedResponse<Review>> {
    const queryParams = new URLSearchParams()
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })
    }
    
    const query = queryParams.toString()
    return apiClient.get<PaginatedResponse<Review>>(`/reviews${query ? `?${query}` : ''}`)
  },

  // Get single review by ID
  async getReviewDetail(reviewId: string): Promise<Review> {
    return apiClient.get<Review>(`/reviews/${reviewId}`)
  },

  // Get user's sent reviews
  async getMyReviews(): Promise<Review[]> {
    return apiClient.get<Review[]>('/reviews/my-reviews')
  },

  // Get user's received reviews
  async getReceivedReviews(): Promise<Review[]> {
    return apiClient.get<Review[]>('/reviews/received-reviews')
  },

  // Get reviews for a specific event
  async getEventReviews(eventId: string): Promise<Review[]> {
    return apiClient.get<Review[]>(`/reviews/event/${eventId}`)
  },

  // Get reviews for a specific user
  async getUserReviews(userId: string): Promise<Review[]> {
    return apiClient.get<Review[]>(`/reviews/user/${userId}`)
  },

  // Create new review
  async createReview(reviewData: CreateReviewRequest): Promise<Review> {
    return apiClient.post<Review>('/reviews', reviewData)
  },

  // Update review
  async updateReview(reviewId: string, reviewData: UpdateReviewRequest): Promise<Review> {
    return apiClient.put<Review>(`/reviews/${reviewId}`, reviewData)
  },

  // Delete review
  async deleteReview(reviewId: string): Promise<ApiResponse> {
    return apiClient.delete<ApiResponse>(`/reviews/${reviewId}`)
  },

  // Report review
  async reportReview(reviewId: string, reason: string): Promise<ApiResponse> {
    return apiClient.post<ApiResponse>(`/reviews/${reviewId}/report`, { reason })
  },
} 