import { StatusBar, Platform } from 'react-native';
import { MD3Theme } from 'react-native-paper';

/**
 * Utility functions for managing status bar appearance across the app
 */

export interface StatusBarConfig {
  backgroundColor?: string;
  barStyle?: 'default' | 'light-content' | 'dark-content';
  translucent?: boolean;
  animated?: boolean;
}

/**
 * Set status bar configuration programmatically
 */
export const setStatusBarConfig = (config: StatusBarConfig) => {
  const {
    backgroundColor,
    barStyle = 'light-content',
    translucent = false,
    animated = true,
  } = config;

  StatusBar.setBarStyle(barStyle, animated);

  if (Platform.OS === 'android' && backgroundColor) {
    StatusBar.setBackgroundColor(backgroundColor, animated);
    StatusBar.setTranslucent(translucent);
  }
};

/**
 * Get primary status bar configuration
 */
export const getPrimaryStatusBarConfig = (theme: MD3Theme): StatusBarConfig => ({
  backgroundColor: theme.colors.primary,
  barStyle: 'light-content',
  translucent: false,
  animated: true,
});

/**
 * Get surface status bar configuration (for modals, secondary screens)
 */
export const getSurfaceStatusBarConfig = (theme: MD3Theme): StatusBarConfig => ({
  backgroundColor: theme.colors.surface,
  barStyle: theme.dark ? 'light-content' : 'dark-content',
  translucent: false,
  animated: true,
});

/**
 * Get transparent status bar configuration (for full-screen content)
 */
export const getTransparentStatusBarConfig = (): StatusBarConfig => ({
  backgroundColor: 'transparent',
  barStyle: 'light-content',
  translucent: true,
  animated: true,
});

/**
 * Get custom color status bar configuration
 */
export const getCustomStatusBarConfig = (
  backgroundColor: string,
  barStyle: 'light-content' | 'dark-content' = 'light-content'
): StatusBarConfig => ({
  backgroundColor,
  barStyle,
  translucent: false,
  animated: true,
});

/**
 * Determine appropriate bar style based on background color brightness
 */
export const getBarStyleForColor = (backgroundColor: string): 'light-content' | 'dark-content' => {
  // Remove # if present
  const hex = backgroundColor.replace('#', '');
  
  // Convert to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  // Calculate brightness (0-255)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  
  // Return appropriate style
  return brightness > 128 ? 'dark-content' : 'light-content';
};

/**
 * Common status bar configurations for different screen types
 */
export const StatusBarConfigs = {
  primary: (theme: MD3Theme) => getPrimaryStatusBarConfig(theme),
  surface: (theme: MD3Theme) => getSurfaceStatusBarConfig(theme),
  transparent: () => getTransparentStatusBarConfig(),
  onboarding: (backgroundColor: string) => getCustomStatusBarConfig(backgroundColor, 'light-content'),
  modal: (theme: MD3Theme) => getSurfaceStatusBarConfig(theme),
  fullscreen: () => getTransparentStatusBarConfig(),
};

export default {
  setStatusBarConfig,
  getPrimaryStatusBarConfig,
  getSurfaceStatusBarConfig,
  getTransparentStatusBarConfig,
  getCustomStatusBarConfig,
  getBarStyleForColor,
  StatusBarConfigs,
};
