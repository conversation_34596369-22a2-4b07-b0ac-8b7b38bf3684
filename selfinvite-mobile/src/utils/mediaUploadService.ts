import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'react-native';

import logger from '@/common/logger';
import { supabaseAuth, supabaseStorage } from '../../lib/supabase';

// Types for media upload
export interface MediaFile {
  uri: string;
  type: 'image' | 'video';
  name: string;
  size: number;
  mimeType: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  url: string;
  path: string;
  size: number;
  type: 'image' | 'video';
  thumbnailUrl?: string;
}

export interface UploadError {
  code: string;
  message: string;
  details?: any;
}

// Configuration
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_VIDEO_SIZE = 50 * 1024 * 1024; // 50MB
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/mov', 'video/avi', 'video/mkv'];
const IMAGE_COMPRESSION_QUALITY = 0.8;
const THUMBNAIL_SIZE = { width: 300, height: 300 };

// Storage bucket path from environment
const MEDIA_PATH = process.env.EXPO_PUBLIC_SUPABASE_MEDIAS_EVENT_OFFER_PATH || 'event_offer_medias';

class MediaUploadService {
  /**
   * Validate file before upload
   */
  private validateFile(file: MediaFile): UploadError | null {
    // Check file size
    if (file.type === 'image' && file.size > MAX_IMAGE_SIZE) {
      return {
        code: 'FILE_TOO_LARGE',
        message: `Image file size must be less than ${MAX_IMAGE_SIZE / (1024 * 1024)}MB`,
        details: { maxSize: MAX_IMAGE_SIZE, actualSize: file.size }
      };
    }

    if (file.type === 'video' && file.size > MAX_VIDEO_SIZE) {
      return {
        code: 'FILE_TOO_LARGE',
        message: `Video file size must be less than ${MAX_VIDEO_SIZE / (1024 * 1024)}MB`,
        details: { maxSize: MAX_VIDEO_SIZE, actualSize: file.size }
      };
    }

    // Check file type
    if (file.type === 'image' && !ALLOWED_IMAGE_TYPES.includes(file.mimeType)) {
      return {
        code: 'INVALID_FILE_TYPE',
        message: `Image type ${file.mimeType} is not supported. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`,
        details: { allowedTypes: ALLOWED_IMAGE_TYPES, actualType: file.mimeType }
      };
    }

    if (file.type === 'video' && !ALLOWED_VIDEO_TYPES.includes(file.mimeType)) {
      return {
        code: 'INVALID_FILE_TYPE',
        message: `Video type ${file.mimeType} is not supported. Allowed types: ${ALLOWED_VIDEO_TYPES.join(', ')}`,
        details: { allowedTypes: ALLOWED_VIDEO_TYPES, actualType: file.mimeType }
      };
    }

    return null;
  }

  /**
   * Get file info from URI
   */
  private async getFileInfo(uri: string): Promise<MediaFile> {
    const fileInfo = await FileSystem.getInfoAsync(uri);
    const fileName = uri.split('/').pop() || `file_${Date.now()}`;
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
    
    // Determine file type and mime type
    let type: 'image' | 'video' = 'image';
    let mimeType = 'image/jpeg';
    
    if (['mp4', 'mov', 'avi', 'mkv'].includes(fileExtension)) {
      type = 'video';
      mimeType = `video/${fileExtension}`;
    } else if (fileExtension === 'png') {
      mimeType = 'image/png';
    } else if (fileExtension === 'webp') {
      mimeType = 'image/webp';
    }

    return {
      uri,
      type,
      name: fileName,
      size: fileInfo.exists ? (fileInfo as any).size || 0 : 0,
      mimeType
    };
  }

  /**
   * Compress image for better upload performance
   */
  private async compressImage(uri: string): Promise<string> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1920, height: 1920 } }],
        {
          compress: IMAGE_COMPRESSION_QUALITY,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );
      return result.uri;
    } catch (error) {
      logger.error('Image compression failed:', error);
      return uri; // Return original if compression fails
    }
  }

  /**
   * Generate thumbnail for image
   */
  private async generateThumbnail(uri: string): Promise<string> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: THUMBNAIL_SIZE }],
        {
          compress: 0.7,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );
      return result.uri;
    } catch (error) {
      logger.error('Thumbnail generation failed:', error);
      return uri; // Return original if thumbnail generation fails
    }
  }

  /**
   * Upload file to Supabase Storage
   */
  private async uploadToStorage(
    file: MediaFile,
    eventId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    const timestamp = Date.now();
    const fileName = `${file.type}_${timestamp}_${file.name}`;
    const filePath = `${eventId}/${file.type}s/${fileName}`;

    try {
      logger.info(`Starting upload to bucket: ${MEDIA_PATH}, path: ${filePath}`);
      
      // Convert file to base64 for upload
      const base64 = await FileSystem.readAsStringAsync(file.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      logger.info(`File converted to base64, size: ${base64.length} characters`);
      
      // Validate base64
      if (!base64 || base64.length === 0) {
        throw new Error('File could not be read or is empty');
      }
      
      // Check if base64 is valid
      try {
        const decoded = decode(base64);
        logger.info(`Base64 decoded successfully, binary size: ${decoded.length} bytes`);
      } catch (decodeError) {
        throw new Error('Invalid file format or corrupted file');
      }

      // Upload to Supabase Storage
      const { data, error } = await supabaseStorage.storage
        .from(MEDIA_PATH)
        .upload(filePath, decode(base64), {
          contentType: file.mimeType,
          upsert: false,
        });

      if (error) {
        logger.error(`Supabase upload error:`, error);
        throw new Error(`Upload failed: ${error.message}`);
      }

      logger.info(`File uploaded successfully, data:`, data);

      // Get public URL
      const { data: urlData } = supabaseStorage.storage
        .from(MEDIA_PATH)
        .getPublicUrl(filePath);

      logger.info(`Public URL generated: ${urlData.publicUrl}`);

      return {
        url: urlData.publicUrl,
        path: filePath,
        size: file.size,
        type: file.type,
      };
    } catch (error) {
      logger.error('Storage upload failed:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload single media file
   */
  async uploadMedia(
    uri: string,
    eventId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Test bucket access first
      const bucketAccessible = await this.testBucketAccess();
      if (!bucketAccessible) {
        throw new Error(`Storage bucket '${MEDIA_PATH}' is not accessible. Please check if the bucket exists and has proper permissions.`);
      }

      // Get file info
      const file = await this.getFileInfo(uri);
      
      // Validate file
      const validationError = this.validateFile(file);
      if (validationError) {
        throw new Error(validationError.message);
      }

      // Compress image if needed
      let processedUri = uri;
      if (file.type === 'image') {
        processedUri = await this.compressImage(uri);
      }

      // Upload to storage
      const result = await this.uploadToStorage(
        { ...file, uri: processedUri },
        eventId,
        onProgress
      );

      // Generate thumbnail for images
      if (file.type === 'image') {
        const thumbnailUri = await this.generateThumbnail(processedUri);
        const thumbnailFile = await this.getFileInfo(thumbnailUri);
        const thumbnailResult = await this.uploadToStorage(
          { ...thumbnailFile, name: `thumb_${file.name}` },
          eventId
        );
        result.thumbnailUrl = thumbnailResult.url;
      }

      logger.info(`Media uploaded successfully: ${result.url}`);
      return result;
    } catch (error) {
      logger.error('Media upload failed:', error);
      throw error;
    }
  }

  /**
   * Upload multiple media files
   */
  async uploadMultipleMedia(
    uris: string[],
    eventId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    const totalFiles = uris.length;

    for (let i = 0; i < uris.length; i++) {
      const uri = uris[i];
      if (!uri) continue;
      
      try {
        const result = await this.uploadMedia(uri, eventId, (fileProgress) => {
          // Calculate overall progress
          const overallProgress = {
            loaded: (i * 100) + fileProgress.percentage,
            total: totalFiles * 100,
            percentage: Math.round(((i * 100) + fileProgress.percentage) / totalFiles)
          };
          onProgress?.(overallProgress);
        });
        results.push(result);
      } catch (error) {
        logger.error(`Failed to upload file ${i + 1}:`, error);
        throw new Error(`Failed to upload file ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return results;
  }

  /**
   * Delete media file from storage
   */
  async deleteMedia(filePath: string): Promise<void> {
    try {
      const { error } = await supabaseStorage.storage
        .from(MEDIA_PATH)
        .remove([filePath]);

      if (error) {
        throw new Error(`Delete failed: ${error.message}`);
      }

      logger.info(`Media deleted successfully: ${filePath}`);
    } catch (error) {
      logger.error('Media deletion failed:', error);
      throw error;
    }
  }

  /**
   * Get storage usage for an event
   */
  async getEventStorageUsage(eventId: string): Promise<number> {
    try {
      const { data, error } = await supabaseStorage.storage
        .from(MEDIA_PATH)
        .list(eventId, {
          limit: 1000,
        });

      if (error) {
        throw new Error(`Failed to get storage usage: ${error.message}`);
      }

      // Calculate total size (this is approximate as we don't have file sizes in list)
      return data?.length || 0;
    } catch (error) {
      logger.error('Failed to get storage usage:', error);
      return 0;
    }
  }

  /**
   * Test if the storage bucket is accessible
   */
  async testBucketAccess(): Promise<boolean> {
    try {
      logger.info(`Testing access to bucket: ${MEDIA_PATH}`);
      
      // Check authentication first
      const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();
      if (authError || !user) {
        logger.error('User not authenticated for storage access');
        throw new Error('Authentication required for media upload');
      }
      
      logger.info(`User authenticated: ${user.email}`);
      
      const { data, error } = await supabaseStorage.storage
        .from(MEDIA_PATH)
        .list('', { limit: 1 });

      if (error) {
        logger.error(`Bucket access test failed:`, error);
        return false;
      }

      logger.info(`Bucket access test successful, bucket exists`);
      return true;
    } catch (error) {
      logger.error('Bucket access test error:', error);
      return false;
    }
  }
}

// Helper function to decode base64
function decode(base64: string): Uint8Array {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

// Export singleton instance
export const mediaUploadService = new MediaUploadService();
export default mediaUploadService;
