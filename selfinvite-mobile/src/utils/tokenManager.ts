import AsyncStorage from '@react-native-async-storage/async-storage'
import { Session } from '@supabase/supabase-js'
import logger from '@/common/logger'

const TOKEN_STORAGE_KEY = 'supabase.auth.token'
const SESSION_STORAGE_KEY = 'supabase.auth.session'

export class TokenManager {
  /**
   * Save session tokens to AsyncStorage
   */
  static async saveSession(session: Session): Promise<void> {
    try {
      // Save the full session
      await AsyncStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(session))
      
      // Also save just the tokens for quick access
      await AsyncStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify({
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at,
        token_type: session.token_type,
      }))
      
      logger.info('Session tokens saved successfully')
    } catch (error) {
      logger.error('Error saving session tokens:', error)
      throw error
    }
  }

  /**
   * Get stored session from AsyncStorage
   */
  static async getStoredSession(): Promise<Session | null> {
    try {
      const sessionData = await AsyncStorage.getItem(SESSION_STORAGE_KEY)
      if (sessionData) {
        return JSON.parse(sessionData) as Session
      }
      return null
    } catch (error) {
      logger.error('Error getting stored session:', error)
      return null
    }
  }

  /**
   * Check if current token is expired
   */
  static isTokenExpired(session: Session | null): boolean {
    if (!session?.expires_at) return true
    
    const expiresAt = new Date(session.expires_at).getTime()
    const now = Date.now()
    
    // Consider token expired if it expires within the next 5 minutes
    return now >= (expiresAt - 5 * 60 * 1000)
  }

  /**
   * Clear all stored tokens
   */
  static async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([TOKEN_STORAGE_KEY, SESSION_STORAGE_KEY])
      logger.info('All tokens cleared successfully')
    } catch (error) {
      logger.error('Error clearing tokens:', error)
      throw error
    }
  }

  /**
   * Get access token from stored session
   */
  static async getAccessToken(): Promise<string | null> {
    try {
      const session = await this.getStoredSession()
      return session?.access_token || null
    } catch (error) {
      logger.error('Error getting access token:', error)
      return null
    }
  }

  /**
   * Refresh session and save new tokens
   */
  static async refreshAndSaveSession(supabase: any): Promise<Session | null> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        logger.error('Error refreshing session:', error)
        return null
      }
      
      if (session) {
        await this.saveSession(session)
        logger.info('Session refreshed and saved successfully')
        return session
      }
      
      return null
    } catch (error) {
      logger.error('Error in refreshAndSaveSession:', error)
      return null
    }
  }
} 