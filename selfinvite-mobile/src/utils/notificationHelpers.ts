import { notificationService } from '../services/notificationService';
import type {
  MessageNotificationData,
  BookingNotificationData,
  PaymentNotificationData,
  EventNotificationData,
} from '../types/notifications';
import logger from '../common/logger';

/**
 * Helper functions for sending different types of notifications
 */

// Message notifications
export async function sendMessageNotification(
  threadId: string,
  senderId: string,
  senderName: string,
  messagePreview: string
): Promise<void> {
  try {
    const notificationData: MessageNotificationData = {
      type: 'message',
      priority: 'high',
      category: 'MESSAGE',
      data: {
        threadId,
        senderId,
        senderName,
        messagePreview,
      },
      deepLink: `selfinvite://chat/${threadId}`,
    };

    await notificationService.scheduleNotification({
      id: `message-${threadId}-${Date.now()}`,
      title: `New message from ${senderName}`,
      body: messagePreview,
      data: notificationData,
      trigger: null, // Send immediately
    });

    logger.info('Message notification sent:', { threadId, senderName });
  } catch (error) {
    logger.error('Failed to send message notification:', error);
  }
}

// Booking notifications
export async function sendBookingNotification(
  bookingId: string,
  eventId: string,
  eventTitle: string,
  status: 'pending_confirmation' | 'confirmed' | 'accepted' | 'rejected' | 'cancelled',
  hostName?: string,
  guestName?: string
): Promise<void> {
  try {
    let title: string;
    let body: string;

    switch (status) {
      case 'pending_confirmation':
        title = 'New Booking Request';
        body = `${guestName || 'Someone'} wants to join "${eventTitle}"`;
        break;
      case 'confirmed':
        title = 'Booking Confirmed';
        body = `Your booking for "${eventTitle}" has been confirmed`;
        break;
      case 'accepted':
        title = 'Booking Accepted';
        body = `${hostName || 'Host'} accepted your booking for "${eventTitle}"`;
        break;
      case 'rejected':
        title = 'Booking Declined';
        body = `${hostName || 'Host'} declined your booking for "${eventTitle}"`;
        break;
      case 'cancelled':
        title = 'Booking Cancelled';
        body = `Booking for "${eventTitle}" has been cancelled`;
        break;
      default:
        title = 'Booking Update';
        body = `Update for "${eventTitle}"`;
    }

    const notificationData: BookingNotificationData = {
      type: 'booking',
      priority: status === 'accepted' || status === 'confirmed' ? 'high' : 'normal',
      category: 'BOOKING',
      data: {
        bookingId,
        eventId,
        eventTitle,
        status,
        hostName,
        guestName,
      },
      deepLink: `selfinvite://booking/${bookingId}`,
    };

    await notificationService.scheduleNotification({
      id: `booking-${bookingId}-${status}`,
      title,
      body,
      data: notificationData,
      trigger: null, // Send immediately
    });

    logger.info('Booking notification sent:', { bookingId, status, eventTitle });
  } catch (error) {
    logger.error('Failed to send booking notification:', error);
  }
}

// Payment notifications
export async function sendPaymentNotification(
  paymentId: string,
  bookingId: string,
  amount: number,
  currency: string,
  status: 'pending' | 'completed' | 'failed' | 'refunded',
  eventTitle: string
): Promise<void> {
  try {
    let title: string;
    let body: string;

    switch (status) {
      case 'pending':
        title = 'Payment Processing';
        body = `Payment for "${eventTitle}" is being processed`;
        break;
      case 'completed':
        title = 'Payment Successful';
        body = `Payment of ${amount} ${currency} for "${eventTitle}" completed`;
        break;
      case 'failed':
        title = 'Payment Failed';
        body = `Payment for "${eventTitle}" failed. Please try again.`;
        break;
      case 'refunded':
        title = 'Payment Refunded';
        body = `Refund of ${amount} ${currency} for "${eventTitle}" processed`;
        break;
      default:
        title = 'Payment Update';
        body = `Update for payment of "${eventTitle}"`;
    }

    const notificationData: PaymentNotificationData = {
      type: 'payment',
      priority: status === 'failed' ? 'high' : 'normal',
      category: 'PAYMENT',
      data: {
        paymentId,
        bookingId,
        amount,
        currency,
        status,
        eventTitle,
      },
      deepLink: `selfinvite://payment/${paymentId}`,
    };

    await notificationService.scheduleNotification({
      id: `payment-${paymentId}-${status}`,
      title,
      body,
      data: notificationData,
      trigger: null, // Send immediately
    });

    logger.info('Payment notification sent:', { paymentId, status, amount, currency });
  } catch (error) {
    logger.error('Failed to send payment notification:', error);
  }
}

// Event notifications
export async function sendEventNotification(
  eventId: string,
  eventTitle: string,
  eventDate: string,
  hostName: string,
  action: 'created' | 'updated' | 'cancelled' | 'reminder'
): Promise<void> {
  try {
    let title: string;
    let body: string;

    switch (action) {
      case 'created':
        title = 'New Event Available';
        body = `${hostName} created "${eventTitle}"`;
        break;
      case 'updated':
        title = 'Event Updated';
        body = `"${eventTitle}" has been updated`;
        break;
      case 'cancelled':
        title = 'Event Cancelled';
        body = `"${eventTitle}" has been cancelled`;
        break;
      case 'reminder':
        title = 'Event Reminder';
        body = `"${eventTitle}" is starting soon!`;
        break;
      default:
        title = 'Event Update';
        body = `Update for "${eventTitle}"`;
    }

    const notificationData: EventNotificationData = {
      type: 'event',
      priority: action === 'cancelled' ? 'high' : 'normal',
      category: 'EVENT',
      data: {
        eventId,
        eventTitle,
        eventDate,
        hostName,
        action,
      },
      deepLink: `selfinvite://event/${eventId}`,
    };

    await notificationService.scheduleNotification({
      id: `event-${eventId}-${action}`,
      title,
      body,
      data: notificationData,
      trigger: null, // Send immediately
    });

    logger.info('Event notification sent:', { eventId, action, eventTitle });
  } catch (error) {
    logger.error('Failed to send event notification:', error);
  }
}

// Schedule event reminders
export async function scheduleEventReminders(
  eventId: string,
  eventTitle: string,
  eventDate: Date
): Promise<{ reminder24h: string; reminder2h: string }> {
  try {
    const now = new Date();
    const reminder24h = new Date(eventDate.getTime() - 24 * 60 * 60 * 1000);
    const reminder2h = new Date(eventDate.getTime() - 2 * 60 * 60 * 1000);

    const reminders = {
      reminder24h: '',
      reminder2h: '',
    };

    // Schedule 24-hour reminder
    if (reminder24h > now) {
      reminders.reminder24h = await notificationService.scheduleNotification({
        id: `event-reminder-24h-${eventId}`,
        title: 'Event Tomorrow',
        body: `"${eventTitle}" is happening tomorrow!`,
        data: {
          type: 'event',
          priority: 'normal',
          category: 'EVENT',
          data: { eventId, eventTitle, reminderType: '24h' },
          deepLink: `selfinvite://event/${eventId}`,
        },
        trigger: { date: reminder24h },
      });
    }

    // Schedule 2-hour reminder
    if (reminder2h > now) {
      reminders.reminder2h = await notificationService.scheduleNotification({
        id: `event-reminder-2h-${eventId}`,
        title: 'Event Starting Soon',
        body: `"${eventTitle}" starts in 2 hours!`,
        data: {
          type: 'event',
          priority: 'high',
          category: 'EVENT',
          data: { eventId, eventTitle, reminderType: '2h' },
          deepLink: `selfinvite://event/${eventId}`,
        },
        trigger: { date: reminder2h },
      });
    }

    logger.info('Event reminders scheduled:', { eventId, eventTitle, reminders });
    return reminders;
  } catch (error) {
    logger.error('Failed to schedule event reminders:', error);
    throw error;
  }
}

// Cancel event reminders
export async function cancelEventReminders(eventId: string): Promise<void> {
  try {
    await notificationService.cancelNotification(`event-reminder-24h-${eventId}`);
    await notificationService.cancelNotification(`event-reminder-2h-${eventId}`);
    logger.info('Event reminders cancelled:', { eventId });
  } catch (error) {
    logger.error('Failed to cancel event reminders:', error);
  }
}

// Check if user is in quiet hours
export function isInQuietHours(
  quietHours: { enabled: boolean; start: string; end: string }
): boolean {
  if (!quietHours.enabled) return false;

  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();

  const [startHour, startMin] = quietHours.start.split(':').map(Number);
  const [endHour, endMin] = quietHours.end.split(':').map(Number);
  
  const startTime = startHour * 60 + startMin;
  const endTime = endHour * 60 + endMin;

  // Handle overnight quiet hours (e.g., 22:00 to 08:00)
  if (startTime > endTime) {
    return currentTime >= startTime || currentTime <= endTime;
  }

  return currentTime >= startTime && currentTime <= endTime;
}

// Format notification time for display
export function formatNotificationTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
}
