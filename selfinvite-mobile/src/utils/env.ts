import logger from '@/common/logger';
import Constants from 'expo-constants';

/**
 * Gets the current application environment (e.g., 'development', 'preview', 'production').
 * @returns The current environment string.
 */
export const getCurrentEnv = (): string => {
  return Constants.expoConfig?.extra?.appEnv || 'development';
};

/**
 * Gets the configuration object for the current environment.
 * @returns The configuration object for the current environment.
 */
export const getCurrentConfig = (): Record<string, any> => {
  const currentEnv = getCurrentEnv();
  const environments = Constants.expoConfig?.extra?.environments || {};
  return environments[currentEnv] || {};
};

/**
 * Gets the base URL for the backend API from the current environment's configuration.
 * @returns The API base URL string.
 */
export const getApiBaseUrl = (): string => {
  const config = getCurrentConfig();
  logger.info(`getApiBaseUrl: ${config.endpointUrl}`);  // Log the URL
  return config.endpointUrl || 'http://localhost:8080';
};