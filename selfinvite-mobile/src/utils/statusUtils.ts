import { BookingStatus } from '@/types';
import { MD3Theme } from 'react-native-paper';

export interface StatusDisplay {
  text: string;
  color: string;
}

/**
 * Get status text with emoji for display
 */
export const getStatusText = (status: BookingStatus): string => {
  switch (status) {
    case BookingStatus.PENDING_CONFIRMATION:
      return '⏳ Pending';
    case BookingStatus.ACCEPTED:
      return '✅ Accepted';
    case BookingStatus.PAYMENT_CONFIRMED:
      return '✅ Payment Confirmed';
    case BookingStatus.REJECTED:
      return '❌ Rejected';
    case BookingStatus.CANCELLED:
      return '🚫 Cancelled';
    case BookingStatus.CANCELLED_BY_GUEST:
      return '🚫 Cancelled by Guest';
    case BookingStatus.CANCELLED_BY_HOST:
      return '🚫 Cancelled by Host';
    default:
      return status;
  }
};

/**
 * Get status display object with text and color
 */
export const getStatusDisplay = (status: string, theme: MD3Theme): StatusDisplay => {
  switch (status) {
    case 'pending_confirmation':
      return { text: '⏳ Pending', color: '#F57C00' };
    case 'accepted':
      return { text: '✅ Accepted', color: '#4CAF50' };
    case 'confirmed':
      return { text: '✅ Confirmed', color: '#4CAF50' };
    case 'rejected':
      return { text: '❌ Rejected', color: '#F44336' };
    case 'cancelled':
      return { text: '🚫 Cancelled', color: '#666666' };
    case 'cancelled_by_guest':
      return { text: '🚫 Cancelled by Guest', color: '#FF9800' };
    case 'cancelled_by_host':
      return { text: '🚫 Cancelled by Host', color: '#F44336' };
    default:
      return { text: status, color: theme.colors.onSurface };
  }
};

/**
 * Get status style object for text styling
 */
export const getStatusStyle = (status: string, theme: MD3Theme) => {
  switch (status) {
    case 'accepted':
    case 'confirmed':
      return { color: theme.colors.primary };
    case 'rejected':
      return { color: theme.colors.error };
    case 'pending_confirmation':
      return { color: '#F57C00' };
    case 'cancelled':
      return { color: '#666666' };
    case 'cancelled_by_guest':
      return { color: '#FF9800' };
    case 'cancelled_by_host':
      return { color: '#F44336' };
    default:
      return { color: theme.colors.onSurface };
  }
};

/**
 * Check if a booking status requires payment (for sent bookings)
 */
export const isPaymentRequired = (status: string): boolean => {
  return status === 'confirmed' || status === 'accepted';
};

/**
 * Get payment status text with emoji for display
 */
export const getPaymentStatusText = (paymentStatus: string): string => {
  switch (paymentStatus) {
    case 'succeeded':
      return '💳 Paid';
    case 'processing':
      return '⏳ Processing';
    case 'requires_payment_method':
      return '❌ Payment Failed';
    case 'requires_confirmation':
      return '⏳ Awaiting Confirmation';
    case 'requires_action':
      return '⚠️ Action Required';
    case 'canceled':
      return '🚫 Cancelled';
    default:
      return paymentStatus;
  }
};

/**
 * Get payment status display object with text and color
 */
export const getPaymentStatusDisplay = (paymentStatus: string, theme: MD3Theme): StatusDisplay => {
  switch (paymentStatus) {
    case 'succeeded':
      return { text: '💳 Paid', color: '#4CAF50' };
    case 'processing':
      return { text: '⏳ Processing', color: '#FF9800' };
    case 'requires_payment_method':
      return { text: '❌ Payment Failed', color: '#F44336' };
    case 'requires_confirmation':
      return { text: '⏳ Awaiting Confirmation', color: '#FF9800' };
    case 'requires_action':
      return { text: '⚠️ Action Required', color: '#FF9800' };
    case 'canceled':
      return { text: '🚫 Cancelled', color: '#666666' };
    default:
      return { text: paymentStatus, color: theme.colors.onSurface };
  }
};

/**
 * Check if payment is already completed
 */
export const isPaymentCompleted = (paymentStatus: string): boolean => {
  return paymentStatus === 'succeeded';
};

/**
 * Check if payment is required and not yet completed
 */
export const isPaymentRequiredAndNotCompleted = (bookingStatus: string, paymentStatus?: string): boolean => {
  if (!isPaymentRequired(bookingStatus)) {
    return false;
  }
  
  // If no payment info, assume payment is required
  if (!paymentStatus) {
    return true;
  }
  
  // If payment is already completed, no further payment needed
  return !isPaymentCompleted(paymentStatus);
};
